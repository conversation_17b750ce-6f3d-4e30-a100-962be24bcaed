<h3 align="center">
    larke-admin-frontend build docs
</h3>

English | [简体中文](./README-build.zh-CN.md)


## Introduction

The program is based on [vue-element-admin](https://github.com/PanJiaChen/vue-element-admin).
build on `pnpm`

## Features

```
- Login / Logout

- Permission Authentication
  - Page permission
  - Directive permission
  - Permission configuration page
  - Two-step login

- Multi-environment build
  - Develop (dev)
  - sit
  - Stage Test (stage)
  - Production (prod)

- Global Features
  - I18n
  - Multiple dynamic themes
  - Dynamic sidebar (supports multi-level routing)
  - Dynamic breadcrumb
  - Tags-view (Tab page Support right-click operation)
  - Svg Sprite
  - Mock data
  - Screenfull
  - Responsive Sidebar

- Editor
  - Rich Text Editor
  - Markdown Editor
  - JSON Editor

- Excel
  - Export Excel
  - Upload Excel
  - Visualization Excel
  - Export zip

- Table
  - Dynamic Table
  - Drag And Drop Table
  - Inline Edit Table

- Error Page
  - 401
  - 404

- Components
  - Avatar Upload
  - Back To Top
  - Drag Dialog
  - Drag Select
  - Drag Kanban
  - Drag List
  - SplitPane
  - Dropzone
  - Sticky
  - CountTo

- Advanced Example
- Error Log
- Dashboard
- Guide Page
- ECharts
- Clipboard
- Markdown to html
```

## Getting started

```bash
# enter the project directory
cd /public/admin

# vi env'VUE_APP_BASE_API
vi .env.development and .env.production VUE_APP_BASE_API

# install dependency
pnpm install

# develop
pnpm run dev
```

This will automatically open http://localhost:9527

## Build

```bash
# build for test environment
pnpm run build:stage

# build for production environment
pnpm run build:prod
```

## Advanced

```bash
# preview the release environment effect
pnpm run preview

# preview the release environment effect + static resource analysis
pnpm run preview -- --report

# code format check
pnpm run lint

# code format check and auto fix
pnpm run lint -- --fix
```

Refer to [Documentation](https://panjiachen.github.io/vue-element-admin-site/guide/essentials/deploy.html) for more information

## License

[Apache2](https://github.com/deatil/larke-admin/blob/main/LICENSE)

Copyright (c) 2021-present Deatil
