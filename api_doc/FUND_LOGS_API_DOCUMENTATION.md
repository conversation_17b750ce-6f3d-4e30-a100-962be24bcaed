# Fund Logs Admin API Documentation

## Overview

The Fund Logs Admin API provides comprehensive functionality for managing and querying fund transaction logs within the admin system. This API follows the established admin system patterns with proper authentication, validation, and response formatting.

## Features

- **Comprehensive Filtering**: Filter by client_id, player_id, player_username, type, amount range, currency, and date ranges
- **Pagination**: Laravel standard pagination with configurable per_page limits
- **Sorting**: Flexible sorting by any field with ascending/descending order
- **Case-Insensitive Search**: Player username search supports case-insensitive matching
- **Statistics**: Comprehensive statistics and summary data
- **Filter Options**: Dynamic filter options based on actual data
- **Validation**: Robust validation with detailed error messages
- **Testing**: Comprehensive test coverage with both unit and feature tests

## Endpoints

### 1. List Fund Logs

**GET** `/admin-api/transactions/fund-logs`

Retrieve paginated fund logs with comprehensive filtering support.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `client_id` | integer | No | Filter by client ID |
| `player_id` | string | No | Filter by player ID |
| `player_username` | string | No | Filter by player username (case-insensitive) |
| `type` | string | No | Filter by transaction type (`bet`, `winning`) |
| `amount_min` | numeric | No | Minimum amount filter |
| `amount_max` | numeric | No | Maximum amount filter |
| `currency` | string | No | Filter by currency |
| `date_from` | date | No | Start date for filtering (YYYY-MM-DD) |
| `date_to` | date | No | End date for filtering (YYYY-MM-DD) |
| `created_at_from` | datetime | No | Start datetime for filtering |
| `created_at_to` | datetime | No | End datetime for filtering |
| `sort_field` | string | No | Field to sort by (default: `created_at`) |
| `sort_order` | string | No | Sort order: `asc` or `desc` (default: `desc`) |
| `per_page` | integer | No | Items per page (1-100, default: 15) |

#### Validation Rules

- **client_id**: Must be a positive integer
- **player_id**: String, max 255 characters
- **player_username**: String, max 255 characters
- **type**: Must be one of: `bet`, `winning`
- **amount_min/amount_max**: Numeric, min 0, max >= min
- **currency**: String, max 10 characters
- **date_from/date_to**: Valid dates, from <= to
- **created_at_from/created_at_to**: Valid datetimes, from <= to
- **Date range limitation**: Maximum 365 days
- **Exclusive date filtering**: Cannot use both date_from/date_to AND created_at_from/created_at_to

#### Response Format

```json
{
  "status": "success",
  "message": "Fund logs retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "client_id": 123,
        "player_id": "player-uuid",
        "player_username": "john_doe",
        "type": "bet",
        "amount": "50.00",
        "currency": "USD",
        "balance_before": "1000.00",
        "balance_after": "950.00",
        "description": "Bet placed on dice game",
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "player": {
          "id": "player-uuid",
          "username": "john_doe",
          "client_id": 123
        }
      }
    ],
    "first_page_url": "http://localhost/admin-api/transactions/fund-logs?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://localhost/admin-api/transactions/fund-logs?page=5",
    "links": [...],
    "next_page_url": "http://localhost/admin-api/transactions/fund-logs?page=2",
    "path": "http://localhost/admin-api/transactions/fund-logs",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 75
  }
}
```

### 2. Fund Logs Statistics

**GET** `/admin-api/transactions/fund-logs/statistics`

Get comprehensive statistics and summary data for fund logs.

#### Response Format

```json
{
  "status": "success",
  "message": "Fund logs statistics retrieved successfully",
  "data": {
    "statistics": {
      "total_logs": 1250,
      "logs_today": 45,
      "logs_this_week": 320,
      "logs_this_month": 890,
      "logs_by_type": {
        "bet": 750,
        "winning": 500
      },
      "logs_by_currency": {
        "USD": 800,
        "EUR": 300,
        "BTC": 150
      },
      "amounts_by_type": {
        "bet": "125000.00",
        "winning": "98000.00"
      },
      "unique_players": 245,
      "unique_clients": 12
    }
  }
}
```

### 3. Fund Logs Filter Options

**GET** `/admin-api/transactions/fund-logs/filter-options`

Get available filter options based on actual data in the database.

#### Response Format

```json
{
  "status": "success",
  "message": "Fund logs filter options retrieved successfully",
  "data": {
    "types": ["bet", "winning"],
    "currencies": ["USD", "EUR", "GBP", "BTC", "ETH"]
  }
}
```

## Usage Examples

### Basic Listing
```bash
GET /admin-api/transactions/fund-logs
```

### Filter by Client
```bash
GET /admin-api/transactions/fund-logs?client_id=123
```

### Filter by Player Username (Case-Insensitive)
```bash
GET /admin-api/transactions/fund-logs?player_username=john
```

### Filter by Transaction Type
```bash
GET /admin-api/transactions/fund-logs?type=bet
```

### Filter by Amount Range
```bash
GET /admin-api/transactions/fund-logs?amount_min=50&amount_max=500
```

### Filter by Date Range
```bash
GET /admin-api/transactions/fund-logs?date_from=2024-01-01&date_to=2024-01-31
```

### Custom Pagination and Sorting
```bash
GET /admin-api/transactions/fund-logs?per_page=25&sort_field=amount&sort_order=desc
```

### Complex Filtering
```bash
GET /admin-api/transactions/fund-logs?client_id=123&type=bet&currency=USD&amount_min=100&date_from=2024-01-01
```

## Error Handling

### Validation Errors (422)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "client_id": ["Client ID must be a valid integer."],
    "amount_max": ["Maximum amount must be greater than or equal to minimum amount."],
    "date_range": ["Date range cannot exceed 365 days."]
  }
}
```

### Server Errors (500)
```json
{
  "status": "error",
  "message": "Failed to retrieve fund logs: Database connection error"
}
```

## Implementation Details

### Architecture
- **Controller**: Lean controllers following admin system patterns
- **Service Layer**: Business logic separated into dedicated service class
- **FormRequest**: Validation logic encapsulated in request classes
- **RouteRule Attributes**: Proper admin authentication and authorization

### Database Integration
- **Model**: Uses existing FundLog model with proper relationships
- **Relationships**: Includes player relationship loading for additional data
- **Optimized Queries**: Efficient filtering and pagination
- **Case-Insensitive Search**: Uses database-agnostic LOWER() function

### Testing
- **Feature Tests**: End-to-end API testing with database
- **Unit Tests**: Individual component testing for service layer
- **Edge Cases**: Empty results, validation errors, limits
- **Filter Combinations**: Comprehensive testing of all filter combinations

## Security

- **Authentication**: Requires admin authentication via larke middleware
- **Authorization**: Uses established admin system authorization patterns
- **Input Validation**: Comprehensive validation with sanitization
- **SQL Injection Protection**: Uses parameterized queries and Eloquent ORM

## Performance Considerations

- **Pagination**: Limits results to prevent memory issues
- **Indexing**: Recommends indexes on frequently filtered columns:
  - `client_id`
  - `player_id`
  - `player_username`
  - `type`
  - `currency`
  - `created_at`
- **Relationship Loading**: Efficient eager loading of player relationship
- **Date Range Limits**: Maximum 365-day range to prevent performance issues

## Configuration

The implementation uses standard Laravel configurations:

- **Pagination**: Default 15 items per page, max 100
- **Date Format**: ISO 8601 format for datetime fields
- **Decimal Precision**: 2 decimal places for monetary amounts
- **Timezone**: Uses application timezone settings
