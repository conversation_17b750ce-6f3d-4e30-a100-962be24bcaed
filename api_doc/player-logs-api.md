# Player Logs API Documentation

## Overview
The Player Logs API provides endpoints for retrieving and filtering player activity logs in the admin system. This API follows the established admin authentication patterns and response formats.

## Base URL
```
{BASE_URL}/admin-api/player-logs
```

## Authentication
All endpoints require admin authentication using Bear<PERSON> token in the Authorization header.

```http
Authorization: Bearer {access_token}
```

**Authentication Guard:** `larke`  
**Middleware:** `larke-admin.auth`

---

## Endpoints

### 1. List Player Logs

Retrieve a paginated list of player logs with comprehensive filtering options.

#### Endpoint Details
- **HTTP Method:** `GET`
- **URL:** `/admin-api/player-logs`
- **Authentication:** Required (Admin)

#### Request Parameters

| Parameter | Type | Required | Default | Validation | Description |
|-----------|------|----------|---------|------------|-------------|
| `username` | string | No | - | max:255 | Filter by player username (partial match) |
| `ip_address` | string | No | - | valid IP | Filter by exact IP address |
| `type` | string | No | - | enum | Filter by log type (see available types below) |
| `from_date` | string | No | - | date, before_or_equal:to_date | Start date for filtering (YYYY-MM-DD) |
| `to_date` | string | No | - | date, after_or_equal:from_date | End date for filtering (YYYY-MM-DD) |
| `sort_field` | string | No | `created_at` | enum | Sort field: `id`, `username`, `ip_address`, `type`, `created_at`, `updated_at` |
| `sort_order` | string | No | `desc` | enum | Sort order: `asc`, `desc` |
| `per_page` | integer | No | `15` | min:1, max:100 | Number of items per page |

#### Available Log Types
- `login` - User login events
- `logout` - User logout events  
- `change_password` - Password change events
- `profile_update` - Profile modification events
- `balance_update` - Balance change events
- `bet_placed` - Betting activity
- `game_result` - Game outcome events
- `withdrawal` - Withdrawal transactions
- `deposit` - Deposit transactions
- `security_event` - Security-related events

#### Validation Rules
- Date range cannot exceed 365 days
- `from_date` must be before or equal to `to_date`
- `per_page` is limited between 1 and 100
- IP address must be valid IPv4 or IPv6 format

#### Success Response (200)

```json
{
   "success": true,
   "code": 0,
   "message": "Player logs retrieved successfully",
   "data": {
      "current_page": 1,
      "data": [
         {
            "id": "01jykm7ab9zte0rt0bq9n1w5dd",
            "player_id": "01jxc4ayjsqs2hepmvc0hsp1ng",
            "type": "login",
            "ip_address": "**********",
            "ip_area": "LOCAL",
            "username": "admin1bit",
            "metadata": [],
            "created_at": "2025-06-25T13:25:18.000000Z",
            "updated_at": "2025-06-25T13:25:18.000000Z",
            "player": {
               "id": "01jxc4ayjsqs2hepmvc0hsp1ng",
               "username": "admin1bit"
            }
         },
         {
            "id": "01jykkb652wb1tyzgmrka38c57",
            "player_id": "01jxc4ayjsqs2hepmvc0hsp1ng",
            "type": "login",
            "ip_address": "**********",
            "ip_area": "LOCAL",
            "username": "admin1bit",
            "metadata": [],
            "created_at": "2025-06-25T13:09:57.000000Z",
            "updated_at": "2025-06-25T13:09:57.000000Z",
            "player": {
               "id": "01jxc4ayjsqs2hepmvc0hsp1ng",
               "username": "admin1bit"
            }
         },
         {
            "id": "01jykhnnwc77kebx55bdps0kdx",
            "player_id": "01jxc4ayjsqs2hepmvc0hsp1ng",
            "type": "login",
            "ip_address": "**********",
            "ip_area": null,
            "username": "admin1bit",
            "metadata": [],
            "created_at": "2025-06-25T12:40:43.000000Z",
            "updated_at": "2025-06-25T12:40:43.000000Z",
            "player": {
               "id": "01jxc4ayjsqs2hepmvc0hsp1ng",
               "username": "admin1bit"
            }
         }
      ],
      "first_page_url": "http://localhost:8084/admin-api/player-logs?page=1",
      "from": 1,
      "last_page": 1,
      "last_page_url": "http://localhost:8084/admin-api/player-logs?page=1",
      "links": [
         {
            "url": null,
            "label": "&laquo; Previous",
            "active": false
         },
         {
            "url": "http://localhost:8084/admin-api/player-logs?page=1",
            "label": "1",
            "active": true
         },
         {
            "url": null,
            "label": "Next &raquo;",
            "active": false
         }
      ],
      "next_page_url": null,
      "path": "http://localhost:8084/admin-api/player-logs",
      "per_page": 10,
      "prev_page_url": null,
      "to": 3,
      "total": 3
   }
}
```

#### Error Responses

**Validation Error (422)**
```json
{
  "code": 422,
  "message": "IP address must be a valid IP address.",
  "data": {
    "ip_address": ["IP address must be a valid IP address."]
  }
}
```

**Authentication Error (401)**
```json
{
  "code": 401,
  "message": "Unauthenticated"
}
```

**Server Error (500)**
```json
{
  "code": 500,
  "message": "Failed to retrieve player logs: Database connection error"
}
```

---

### 2. Get Available Log Types

Retrieve all available log types for filtering.

#### Endpoint Details
- **HTTP Method:** `GET`
- **URL:** `/admin-api/player-logs/types`
- **Authentication:** Required (Admin)

#### Success Response (200)
```json
{
  "code": 200,
  "message": "Available log types retrieved successfully",
  "data": {
    "types": [
      "login",
      "logout", 
      "change_password",
      "profile_update",
      "balance_update",
      "bet_placed",
      "game_result",
      "withdrawal",
      "deposit",
      "security_event"
    ]
  }
}
```

---

### 3. Get Player Log Statistics

Retrieve statistical information about player logs.

#### Endpoint Details
- **HTTP Method:** `GET`
- **URL:** `/admin-api/player-logs/statistics`
- **Authentication:** Required (Admin)

#### Success Response (200)
```json
{
  "code": 200,
  "message": "Player log statistics retrieved successfully",
  "data": {
    "statistics": {
      "total_logs": 15420,
      "logs_today": 245,
      "logs_this_week": 1680,
      "logs_this_month": 7230,
      "recent_activity": 89,
      "logs_by_type": {
        "login": 3450,
        "logout": 3200,
        "bet_placed": 5670,
        "withdrawal": 890,
        "deposit": 1210
      },
      "unique_players": 1250,
      "unique_ip_addresses": 980
    }
  }
}
```

---

## Integration Examples

### Basic Request - Get All Logs
```javascript
const response = await fetch('/admin-api/player-logs', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data.data.player_logs);
```

### Filter by Username
```javascript
const response = await fetch('/admin-api/player-logs?username=john_doe', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});
```

### Filter by Date Range and Type
```javascript
const params = new URLSearchParams({
  from_date: '2024-01-01',
  to_date: '2024-01-31',
  type: 'login',
  per_page: '25'
});

const response = await fetch(`/admin-api/player-logs?${params}`, {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});
```

### Multiple Filters with Sorting
```javascript
const params = new URLSearchParams({
  username: 'player',
  ip_address: '*************',
  type: 'bet_placed',
  from_date: '2024-01-01',
  to_date: '2024-01-31',
  sort_field: 'created_at',
  sort_order: 'desc',
  per_page: '20'
});

const response = await fetch(`/admin-api/player-logs?${params}`, {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});
```

### Get Available Types
```javascript
const response = await fetch('/admin-api/player-logs/types', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});

const types = await response.json();
console.log(types.data.types);
```

### Get Statistics
```javascript
const response = await fetch('/admin-api/player-logs/statistics', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
});

const stats = await response.json();
console.log(stats.data.statistics);
```

---

## Error Handling

### Common Error Scenarios

1. **Invalid IP Address**
   - Status: 422
   - Message: "IP address must be a valid IP address."

2. **Invalid Log Type**
   - Status: 422  
   - Message: "Type must be one of: login, logout, change_password, ..."

3. **Invalid Date Range**
   - Status: 422
   - Message: "From date must be before or equal to to date."

4. **Date Range Too Large**
   - Status: 422
   - Message: "Date range cannot exceed 365 days."

5. **Authentication Required**
   - Status: 401
   - Message: "Unauthenticated"

6. **Server Error**
   - Status: 500
   - Message: "Failed to retrieve player logs: [error details]"

### Frontend Error Handling Example
```javascript
async function fetchPlayerLogs(filters = {}) {
  try {
    const params = new URLSearchParams(filters);
    const response = await fetch(`/admin-api/player-logs?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer your-admin-token',
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle different error types
      switch (response.status) {
        case 401:
          // Redirect to login
          window.location.href = '/admin/login';
          break;
        case 422:
          // Show validation errors
          console.error('Validation errors:', data.data);
          break;
        case 500:
          // Show server error message
          console.error('Server error:', data.message);
          break;
        default:
          console.error('Unexpected error:', data.message);
      }
      return null;
    }

    return data.data;
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}
```

---

## Response Format Standards

All API responses follow the consistent format:

```json
{
  "code": 200,           // HTTP status code
  "message": "string",   // Human-readable message
  "data": {}            // Response data (varies by endpoint)
}
```

### Pagination Format
When pagination is included, it follows Laravel's standard pagination format:

```json
{
  "current_page": 1,
  "per_page": 15,
  "total": 150,
  "last_page": 10,
  "from": 1,
  "to": 15,
  "path": "/admin-api/player-logs",
  "first_page_url": "/admin-api/player-logs?page=1",
  "last_page_url": "/admin-api/player-logs?page=10",
  "next_page_url": "/admin-api/player-logs?page=2",
  "prev_page_url": null
}
```

---

## Notes for Frontend Developers

1. **Authentication**: Always include the Bearer token in the Authorization header
2. **Error Handling**: Check response status and handle different error codes appropriately
3. **Pagination**: Use the pagination metadata to implement proper pagination controls
4. **Date Formats**: Use YYYY-MM-DD format for date parameters
5. **URL Encoding**: Properly encode query parameters, especially when they contain special characters
6. **Rate Limiting**: Be mindful of potential rate limiting on admin endpoints
7. **Caching**: Consider implementing appropriate caching strategies for frequently accessed data like available types
