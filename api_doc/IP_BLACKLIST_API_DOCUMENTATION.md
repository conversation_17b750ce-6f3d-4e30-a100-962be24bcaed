# IP Blacklist Management API Documentation

## Overview

The IP Blacklist Management API provides comprehensive endpoints for managing IP address blacklists in the admin system. This API allows administrators to create, read, update, and delete IP blacklist entries, perform bulk operations, and retrieve statistics.

**Base URL**: `/admin-api/ip-blacklists`

## Authentication

All endpoints require admin authentication using Bearer tokens.

**Required Headers:**
```http
Authorization: Bearer {admin_token}
Content-Type: application/json
Accept: application/json
```

## Common Response Format

All API responses follow this structure:

**Success Response:**
```json
{
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

**Error Response:**
```json
{
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

---

## 1. List IP Blacklist Entries

Retrieve a paginated list of IP blacklist entries with advanced filtering options.

### Request

**Method:** `GET`  
**URL:** `/admin-api/ip-blacklists/`

**Query Parameters:**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `ip_address` | string | No | Filter by IP address (partial match) | `192.168` |
| `status` | string | No | Filter by status (`active`, `inactive`) | `active` |
| `blocked_by` | string | No | Filter by admin ID who blocked the IP | `admin-uuid-123` |
| `date_from` | date | No | Filter entries created from this date | `2024-01-01` |
| `date_to` | date | No | Filter entries created until this date | `2024-12-31` |
| `created_at_from` | datetime | No | Filter by creation timestamp (from) | `2024-01-01T00:00:00Z` |
| `created_at_to` | datetime | No | Filter by creation timestamp (to) | `2024-12-31T23:59:59Z` |
| `expires_at_from` | datetime | No | Filter by expiration timestamp (from) | `2024-06-01T00:00:00Z` |
| `expires_at_to` | datetime | No | Filter by expiration timestamp (to) | `2024-12-31T23:59:59Z` |
| `is_expired` | boolean | No | Filter by expiration status | `true` |
| `is_effective` | boolean | No | Filter by effectiveness (active + not expired) | `true` |
| `sort_field` | string | No | Sort field | `created_at` |
| `sort_order` | string | No | Sort order (`asc`, `desc`) | `desc` |
| `per_page` | integer | No | Items per page (1-100) | `15` |

**Valid sort fields:** `id`, `ip_address`, `status`, `blocked_by`, `expires_at`, `created_at`, `updated_at`

### Response

**Success (200):**
```json
{
  "message": "IP blacklist entries retrieved successfully",
  "data": {
    "ip_blacklists": [
      {
        "id": 1,
        "ip_address": "*************",
        "reason": "Suspicious activity detected",
        "blocked_by": "admin-uuid-123",
        "blocked_by_admin": {
          "id": "admin-uuid-123",
          "name": "admin_user",
          "nickname": "Admin User",
          "email": "<EMAIL>"
        },
        "expires_at": "2024-12-31T23:59:59.000000Z",
        "status": "active",
        "is_effective": true,
        "is_expired": false,
        "created_at": "2024-06-25T10:30:00.000000Z",
        "updated_at": "2024-06-25T10:30:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "last_page": 5,
      "per_page": 15,
      "total": 67,
      "from": 1,
      "to": 15,
      "has_more_pages": true
    },
    "filters": {
      "sort_field": "created_at",
      "sort_order": "desc",
      "per_page": 15
    }
  }
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function getIpBlacklists(filters = {}) {
  const params = new URLSearchParams(filters);
  
  const response = await fetch(`/admin-api/ip-blacklists/?${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Accept': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
}

// Usage examples
const activeIps = await getIpBlacklists({ status: 'active', per_page: 20 });
const recentIps = await getIpBlacklists({ 
  date_from: '2024-06-01', 
  sort_field: 'created_at',
  sort_order: 'desc'
});
```

**cURL:**
```bash
# Basic list
curl -X GET "/admin-api/ip-blacklists/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Accept: application/json"

# With filters
curl -X GET "/admin-api/ip-blacklists/?status=active&per_page=20&sort_field=ip_address&sort_order=asc" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Accept: application/json"
```

---

## 2. Create IP Blacklist Entry

Create a new IP blacklist entry.

### Request

**Method:** `POST`  
**URL:** `/admin-api/ip-blacklists/`

**Request Body Schema:**
```json
{
  "ip_address": "string (required, max:45, valid IP, unique)",
  "reason": "string (optional, max:1000)",
  "expires_at": "datetime (optional, future date)",
  "status": "string (required, 'active'|'inactive')"
}
```

**Example Request Body:**
```json
{
  "ip_address": "***********",
  "reason": "Multiple failed login attempts",
  "expires_at": "2024-12-31T23:59:59Z",
  "status": "active"
}
```

### Response

**Success (201):**
```json
{
  "message": "IP blacklist entry created successfully",
  "data": {
    "ip_blacklist": {
      "id": 15,
      "ip_address": "***********",
      "reason": "Multiple failed login attempts",
      "blocked_by": "admin-uuid-123",
      "blocked_by_admin": {
        "id": "admin-uuid-123",
        "name": "admin_user",
        "nickname": "Admin User",
        "email": "<EMAIL>"
      },
      "expires_at": "2024-12-31T23:59:59.000000Z",
      "status": "active",
      "is_effective": true,
      "is_expired": false,
      "created_at": "2024-06-25T10:30:00.000000Z",
      "updated_at": "2024-06-25T10:30:00.000000Z"
    }
  }
}
```

**Validation Error (422):**
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "ip_address": ["The ip address field is required."],
    "status": ["The status field is required."]
  }
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function createIpBlacklist(data) {
  const response = await fetch('/admin-api/ip-blacklists/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create IP blacklist entry');
  }
  
  return await response.json();
}

// Usage
try {
  const newEntry = await createIpBlacklist({
    ip_address: '***********',
    reason: 'Suspicious activity',
    status: 'active',
    expires_at: '2024-12-31T23:59:59Z'
  });
  console.log('Created:', newEntry.data.ip_blacklist);
} catch (error) {
  console.error('Error:', error.message);
}
```

**cURL:**
```bash
curl -X POST "/admin-api/ip-blacklists/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "ip_address": "***********",
    "reason": "Multiple failed login attempts",
    "expires_at": "2024-12-31T23:59:59Z",
    "status": "active"
  }'
```

---

## 3. Get Specific IP Blacklist Entry

Retrieve details of a specific IP blacklist entry.

### Request

**Method:** `GET`
**URL:** `/admin-api/ip-blacklists/{id}`

**Path Parameters:**
- `id` (integer, required): The ID of the IP blacklist entry

### Response

**Success (200):**
```json
{
  "message": "IP blacklist entry retrieved successfully",
  "data": {
    "ip_blacklist": {
      "id": 1,
      "ip_address": "*************",
      "reason": "Suspicious activity detected",
      "blocked_by": "admin-uuid-123",
      "blocked_by_admin": {
        "id": "admin-uuid-123",
        "name": "admin_user",
        "nickname": "Admin User",
        "email": "<EMAIL>"
      },
      "expires_at": "2024-12-31T23:59:59.000000Z",
      "status": "active",
      "is_effective": true,
      "is_expired": false,
      "created_at": "2024-06-25T10:30:00.000000Z",
      "updated_at": "2024-06-25T10:30:00.000000Z"
    }
  }
}
```

**Not Found (404):**
```json
{
  "message": "IP blacklist entry not found."
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function getIpBlacklist(id) {
  const response = await fetch(`/admin-api/ip-blacklists/${id}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Accept': 'application/json'
    }
  });

  if (response.status === 404) {
    throw new Error('IP blacklist entry not found');
  }

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Usage
try {
  const entry = await getIpBlacklist(1);
  console.log('Entry:', entry.data.ip_blacklist);
} catch (error) {
  console.error('Error:', error.message);
}
```

**cURL:**
```bash
curl -X GET "/admin-api/ip-blacklists/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Accept: application/json"
```

---

## 4. Update IP Blacklist Entry

Update an existing IP blacklist entry.

### Request

**Method:** `PUT`
**URL:** `/admin-api/ip-blacklists/{id}`

**Path Parameters:**
- `id` (integer, required): The ID of the IP blacklist entry

**Request Body Schema:**
```json
{
  "ip_address": "string (required, max:45, valid IP, unique except current)",
  "reason": "string (optional, max:1000)",
  "expires_at": "datetime (optional, future date)",
  "status": "string (required, 'active'|'inactive')"
}
```

### Response

**Success (200):**
```json
{
  "message": "IP blacklist entry updated successfully",
  "data": {
    "ip_blacklist": {
      "id": 1,
      "ip_address": "***********",
      "reason": "Updated reason for blocking",
      "blocked_by": "admin-uuid-123",
      "blocked_by_admin": {
        "id": "admin-uuid-123",
        "name": "admin_user",
        "nickname": "Admin User",
        "email": "<EMAIL>"
      },
      "expires_at": "2025-01-31T23:59:59.000000Z",
      "status": "inactive",
      "is_effective": false,
      "is_expired": false,
      "created_at": "2024-06-25T10:30:00.000000Z",
      "updated_at": "2024-06-25T11:45:00.000000Z"
    }
  }
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function updateIpBlacklist(id, data) {
  const response = await fetch(`/admin-api/ip-blacklists/${id}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify(data)
  });

  if (response.status === 404) {
    throw new Error('IP blacklist entry not found');
  }

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update IP blacklist entry');
  }

  return await response.json();
}

// Usage
try {
  const updated = await updateIpBlacklist(1, {
    ip_address: '***********',
    reason: 'Updated reason',
    status: 'inactive'
  });
  console.log('Updated:', updated.data.ip_blacklist);
} catch (error) {
  console.error('Error:', error.message);
}
```

**cURL:**
```bash
curl -X PUT "/admin-api/ip-blacklists/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "ip_address": "***********",
    "reason": "Updated reason for blocking",
    "status": "inactive"
  }'
```

---

## 5. Delete IP Blacklist Entry

Delete a specific IP blacklist entry.

### Request

**Method:** `DELETE`
**URL:** `/admin-api/ip-blacklists/{id}`

**Path Parameters:**
- `id` (integer, required): The ID of the IP blacklist entry

### Response

**Success (200):**
```json
{
  "message": "IP blacklist entry deleted successfully"
}
```

**Not Found (404):**
```json
{
  "message": "IP blacklist entry not found."
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function deleteIpBlacklist(id) {
  const response = await fetch(`/admin-api/ip-blacklists/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Accept': 'application/json'
    }
  });

  if (response.status === 404) {
    throw new Error('IP blacklist entry not found');
  }

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Usage with confirmation
async function deleteWithConfirmation(id) {
  if (confirm('Are you sure you want to delete this IP blacklist entry?')) {
    try {
      await deleteIpBlacklist(id);
      console.log('Entry deleted successfully');
      // Refresh the list or remove from UI
    } catch (error) {
      console.error('Error:', error.message);
    }
  }
}
```

**cURL:**
```bash
curl -X DELETE "/admin-api/ip-blacklists/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Accept: application/json"
```

---

## 6. Bulk Update Operations

Perform bulk operations on multiple IP blacklist entries.

### Request

**Method:** `POST`
**URL:** `/admin-api/ip-blacklists/bulk-update`

**Request Body Schema:**
```json
{
  "ids": "array (required, min:1, max:100, integers, existing IDs)",
  "action": "string (required, 'activate'|'deactivate'|'delete')",
  "status": "string (required_if action is activate/deactivate, 'active'|'inactive')"
}
```

**Example Request Bodies:**

*Activate multiple entries:*
```json
{
  "ids": [1, 2, 3, 4, 5],
  "action": "activate",
  "status": "active"
}
```

*Delete multiple entries:*
```json
{
  "ids": [10, 11, 12],
  "action": "delete"
}
```

### Response

**Success (200):**
```json
{
  "message": "Bulk operation completed. Success: 5, Failed: 0",
  "data": {
    "results": {
      "success": 5,
      "failed": 0,
      "errors": []
    }
  }
}
```

**Partial Success (200):**
```json
{
  "message": "Bulk operation completed. Success: 3, Failed: 2",
  "data": {
    "results": {
      "success": 3,
      "failed": 2,
      "errors": [
        "IP blacklist entry with ID 99 not found.",
        "Failed to activate IP blacklist entry with ID 100: Database error"
      ]
    }
  }
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function bulkUpdateIpBlacklists(ids, action, status = null) {
  const data = { ids, action };
  if (status) data.status = status;

  const response = await fetch('/admin-api/ip-blacklists/bulk-update', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Bulk operation failed');
  }

  return await response.json();
}

// Usage examples
async function bulkActivate(selectedIds) {
  try {
    const result = await bulkUpdateIpBlacklists(selectedIds, 'activate', 'active');
    console.log(`Activated ${result.data.results.success} entries`);
    if (result.data.results.failed > 0) {
      console.warn('Some operations failed:', result.data.results.errors);
    }
  } catch (error) {
    console.error('Bulk activate failed:', error.message);
  }
}

async function bulkDelete(selectedIds) {
  if (confirm(`Delete ${selectedIds.length} IP blacklist entries?`)) {
    try {
      const result = await bulkUpdateIpBlacklists(selectedIds, 'delete');
      console.log(`Deleted ${result.data.results.success} entries`);
    } catch (error) {
      console.error('Bulk delete failed:', error.message);
    }
  }
}
```

**cURL:**
```bash
# Bulk activate
curl -X POST "/admin-api/ip-blacklists/bulk-update" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "ids": [1, 2, 3, 4, 5],
    "action": "activate",
    "status": "active"
  }'

# Bulk delete
curl -X POST "/admin-api/ip-blacklists/bulk-update" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "ids": [10, 11, 12],
    "action": "delete"
  }'
```

---

## 7. Check IP Blacklist Status

Check if a specific IP address is currently blacklisted.

### Request

**Method:** `POST`
**URL:** `/admin-api/ip-blacklists/check-ip`

**Request Body Schema:**
```json
{
  "ip_address": "string (required, valid IP address)"
}
```

**Example Request Body:**
```json
{
  "ip_address": "*************"
}
```

### Response

**Success (200):**
```json
{
  "message": "IP blacklist status checked successfully",
  "data": {
    "ip_address": "*************",
    "is_blacklisted": true
  }
}
```

**Invalid IP (422):**
```json
{
  "message": "Invalid IP address format."
}
```

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function checkIpBlacklist(ipAddress) {
  const response = await fetch('/admin-api/ip-blacklists/check-ip', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify({ ip_address: ipAddress })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to check IP status');
  }

  return await response.json();
}

// Usage
async function validateIpAccess(ip) {
  try {
    const result = await checkIpBlacklist(ip);
    if (result.data.is_blacklisted) {
      console.warn(`IP ${ip} is blacklisted!`);
      return false;
    } else {
      console.log(`IP ${ip} is not blacklisted`);
      return true;
    }
  } catch (error) {
    console.error('IP check failed:', error.message);
    return false;
  }
}
```

**cURL:**
```bash
curl -X POST "/admin-api/ip-blacklists/check-ip" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "ip_address": "*************"
  }'
```

---

## 8. Get Statistics

Retrieve comprehensive statistics about IP blacklist entries.

### Request

**Method:** `GET`
**URL:** `/admin-api/ip-blacklists/statistics`

### Response

**Success (200):**
```json
{
  "message": "IP blacklist statistics retrieved successfully",
  "data": {
    "statistics": {
      "total_entries": 150,
      "active_entries": 120,
      "inactive_entries": 30,
      "expired_entries": 15,
      "effective_entries": 105,
      "entries_today": 5,
      "entries_this_week": 23,
      "entries_this_month": 67
    }
  }
}
```

**Field Descriptions:**
- `total_entries`: Total number of IP blacklist entries
- `active_entries`: Entries with status "active"
- `inactive_entries`: Entries with status "inactive"
- `expired_entries`: Entries that have passed their expiration date
- `effective_entries`: Active entries that haven't expired
- `entries_today`: Entries created today
- `entries_this_week`: Entries created this week
- `entries_this_month`: Entries created this month

### Frontend Examples

**JavaScript/Fetch:**
```javascript
async function getIpBlacklistStatistics() {
  const response = await fetch('/admin-api/ip-blacklists/statistics', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Accept': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Usage for dashboard
async function updateDashboard() {
  try {
    const stats = await getIpBlacklistStatistics();
    const data = stats.data.statistics;

    // Update dashboard elements
    document.getElementById('total-entries').textContent = data.total_entries;
    document.getElementById('active-entries').textContent = data.active_entries;
    document.getElementById('effective-entries').textContent = data.effective_entries;
    document.getElementById('entries-today').textContent = data.entries_today;

    // Calculate percentages
    const activePercentage = ((data.active_entries / data.total_entries) * 100).toFixed(1);
    document.getElementById('active-percentage').textContent = `${activePercentage}%`;

  } catch (error) {
    console.error('Failed to load statistics:', error.message);
  }
}
```

**cURL:**
```bash
curl -X GET "/admin-api/ip-blacklists/statistics" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Accept: application/json"
```
