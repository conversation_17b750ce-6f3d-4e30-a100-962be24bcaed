import { formatCurrency } from '@/utils/math.js'

describe('Utils:formatCurrency', () => {
  it('formats valid numbers with 2 decimal places', () => {
    expect(formatCurrency(123.456)).toBe('123.46')
    expect(formatCurrency(100)).toBe('100.00')
    expect(formatCurrency(0)).toBe('0.00')
    expect(formatCurrency(0.1)).toBe('0.10')
    expect(formatCurrency(999.99)).toBe('999.99')
  })

  it('handles string numbers correctly', () => {
    expect(formatCurrency('123.456')).toBe('123.46')
    expect(formatCurrency('100')).toBe('100.00')
    expect(formatCurrency('0')).toBe('0.00')
    expect(formatCurrency('0.1')).toBe('0.10')
  })

  it('handles edge cases gracefully', () => {
    expect(formatCurrency(null)).toBe('0.00')
    expect(formatCurrency(undefined)).toBe('0.00')
    expect(formatCurrency('')).toBe('0.00')
    expect(formatCurrency('invalid')).toBe('0.00')
    expect(formatCurrency(NaN)).toBe('0.00')
    expect(formatCurrency(Infinity)).toBe('0.00')
    expect(formatCurrency(-Infinity)).toBe('0.00')
  })

  it('handles negative numbers correctly', () => {
    expect(formatCurrency(-123.456)).toBe('-123.46')
    expect(formatCurrency(-100)).toBe('-100.00')
    expect(formatCurrency('-123.456')).toBe('-123.46')
  })

  it('handles very large numbers', () => {
    expect(formatCurrency(1234567.89)).toBe('1234567.89')
    expect(formatCurrency(999999999.99)).toBe('999999999.99')
  })

  it('handles very small numbers', () => {
    expect(formatCurrency(0.001)).toBe('0.00')
    expect(formatCurrency(0.005)).toBe('0.01')
    expect(formatCurrency(0.004)).toBe('0.00')
  })
})
