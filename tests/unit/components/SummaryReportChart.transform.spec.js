import { shallowMount, createLocalVue } from '@vue/test-utils'
import SummaryReportChart from '@/views/dashboard/admin/components/SummaryReportChart.vue'
import ElementUI from 'element-ui'

// Mock echarts
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    dispose: jest.fn(),
    resize: jest.fn()
  })),
  use: jest.fn(),
  registerTheme: jest.fn()
}))

// Mock echarts theme
jest.mock('echarts/theme/macarons', () => {})

// Mock the API
jest.mock('@/api/dashboard', () => ({
  getSummaryReport: jest.fn(() => Promise.resolve({
    data: []
  }))
}))

const localVue = createLocalVue()
localVue.use(ElementUI)

describe('SummaryReportChart.vue - Data Transformation', () => {
  let wrapper

  beforeEach(() => {
    wrapper = shallowMount(SummaryReportChart, {
      localVue,
      mocks: {
        $t: (key) => {
          const translations = {
            'dashboard.bet_amount': 'Bet Amount',
            'dashboard.total_profit': 'Total Profit',
            'dashboard.summary_report_chart_title': 'Performance Overview'
          }
          return translations[key] || key
        },
        $nextTick: (callback) => callback && callback()
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy()
    }
  })

  it('transforms API data correctly', () => {
    const apiData = [
      {
        bet_date: '2023-01-01',
        bet_amount: 1000,
        total_profit: 100
      },
      {
        bet_date: '2023-01-02',
        bet_amount: 1500,
        total_profit: -200
      },
      {
        bet_date: '2023-01-03',
        bet_amount: 2000,
        total_profit: 300
      }
    ]

    const result = wrapper.vm.transformApiData(apiData)

    expect(result.dates).toEqual(['Jan 1', 'Jan 2', 'Jan 3'])
    expect(result.series).toHaveLength(2)
    expect(result.series[0].name).toBe('Bet Amount')
    expect(result.series[0].data).toEqual([1000, 1500, 2000])
    expect(result.series[1].name).toBe('Total Profit')
    expect(result.series[1].data).toEqual([100, -200, 300])
  })

  it('handles null values correctly', () => {
    const apiData = [
      {
        bet_date: '2023-01-01',
        bet_amount: null,
        total_profit: null
      },
      {
        bet_date: '2023-01-02',
        bet_amount: 1500,
        total_profit: -200
      }
    ]

    const result = wrapper.vm.transformApiData(apiData)

    expect(result.series[0].data).toEqual([0, 1500])
    expect(result.series[1].data).toEqual([0, -200])
  })

  it('handles empty data correctly', () => {
    const result1 = wrapper.vm.transformApiData([])
    expect(result1.dates).toEqual([])
    expect(result1.series).toHaveLength(2)
    expect(result1.series[0].data).toEqual([])
    expect(result1.series[1].data).toEqual([])

    const result2 = wrapper.vm.transformApiData(null)
    expect(result2.dates).toEqual([])
    expect(result2.series).toEqual([])

    const result3 = wrapper.vm.transformApiData(undefined)
    expect(result3.dates).toEqual([])
    expect(result3.series).toEqual([])
  })

  it('formats currency values correctly', () => {
    expect(wrapper.vm.formatCurrencyValue(1000)).toBe('1,000')
    expect(wrapper.vm.formatCurrencyValue(1000000)).toBe('1,000,000')
    expect(wrapper.vm.formatCurrencyValue(null)).toBe('0')
    expect(wrapper.vm.formatCurrencyValue(undefined)).toBe('0')
    expect(wrapper.vm.formatCurrencyValue(0)).toBe('0')
  })

  it('formats axis values correctly', () => {
    expect(wrapper.vm.formatAxisValue(1000)).toBe('1.0K')
    expect(wrapper.vm.formatAxisValue(1000000)).toBe('1.0M')
    expect(wrapper.vm.formatAxisValue(1500000)).toBe('1.5M')
    expect(wrapper.vm.formatAxisValue(500)).toBe('500')
    expect(wrapper.vm.formatAxisValue(-1000)).toBe('-1.0K')
    expect(wrapper.vm.formatAxisValue(-1000000)).toBe('-1.0M')
  })

  it('generates series config with correct styling', () => {
    wrapper.setData({
      chartData: {
        series: [
          { name: 'Bet Amount', data: [1000, 1500, 2000] },
          { name: 'Total Profit', data: [100, -200, 300] }
        ]
      }
    })

    const seriesConfig = wrapper.vm.generateSeriesConfig()

    expect(seriesConfig).toHaveLength(2)
    
    // Check bet amount series (should be blue)
    expect(seriesConfig[0].name).toBe('Bet Amount')
    expect(seriesConfig[0].lineStyle.color).toBe('#409eff')
    expect(seriesConfig[0].itemStyle.color).toBe('#409eff')
    
    // Check total profit series (should be red)
    expect(seriesConfig[1].name).toBe('Total Profit')
    expect(seriesConfig[1].lineStyle.color).toBe('#f56c6c')
    expect(seriesConfig[1].itemStyle.color).toBe('#f56c6c')
  })
})
