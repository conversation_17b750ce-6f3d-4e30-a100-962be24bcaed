import { shallowMount, createLocalVue } from '@vue/test-utils'
import SummaryReportChart from '@/views/dashboard/admin/components/SummaryReportChart.vue'
import ElementUI from 'element-ui'

// Mock echarts
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    dispose: jest.fn(),
    resize: jest.fn()
  })),
  use: jest.fn(),
  registerTheme: jest.fn()
}))

// Mock echarts theme
jest.mock('echarts/theme/macarons', () => {})

// Mock the API with new response format
jest.mock('@/api/dashboard', () => ({
  getSummaryReport: jest.fn(() => Promise.resolve({
    data: [
      {
        bet_date: '2023-01-01',
        bet_amount: 1000,
        total_profit: 100
      },
      {
        bet_date: '2023-01-02',
        bet_amount: 1500,
        total_profit: -200
      },
      {
        bet_date: '2023-01-03',
        bet_amount: 2000,
        total_profit: 300
      }
    ]
  }))
}))

const localVue = createLocalVue()
localVue.use(ElementUI)

describe('SummaryReportChart.vue - Minimal Test', () => {
  it('can be imported and mounted without ECharts errors', () => {
    expect(() => {
      const wrapper = shallowMount(SummaryReportChart, {
        localVue,
        mocks: {
          $t: (key) => key,
          $nextTick: (callback) => callback && callback()
        }
      })
      
      // Check that component exists
      expect(wrapper.exists()).toBe(true)
      
      // Check that no ECharts-related errors occurred during mounting
      expect(wrapper.vm).toBeTruthy()
      
      // Clean up
      wrapper.destroy()
    }).not.toThrow()
  })

  it('initializes chart successfully', () => {
    const wrapper = shallowMount(SummaryReportChart, {
      localVue,
      mocks: {
        $t: (key) => key,
        $nextTick: (callback) => callback && callback()
      }
    })
    
    // The chart should be initialized (mocked)
    expect(wrapper.vm.chart).toBeTruthy()
    
    wrapper.destroy()
  })
})
