module.exports = {
  root: true,
  parser: 'vue-eslint-parser',
  parserOptions: {
    // parser: 'vue-eslint-parser',
    "ecmaVersion": 2020,
    // parser: 'babel-eslint',
    sourceType: 'module'
  },
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: ['plugin:vue/strongly-recommended',"plugin:vue-libs/recommended" ],

  // add your custom rules here
  // it is base on https://github.com/vuejs/eslint-config-vue
  rules: {
    
    'eqeqeq': ['off', 'always', { 'null': 'ignore' }],
   'camelcase': [0, {
      'properties': 'always'
    }],
    'vue/require-default-prop': 'off',
    'no-unused-vars': [1, {
      'vars': 'all',
      'args': 'none',
      'varsIgnorePattern': '([iI]gnored)|([_].+)|(md5)|parseTime|updateConfig|LangSelect'
    }],
  }
}
