{"name": "larke-admin-frontend", "version": "2.0.0", "description": "A larke-<PERSON><PERSON> frontend", "author": "Deatil <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "lint-fix": "eslint --fix --ext .js,.vue src", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "fix-lang": "eslint --fix --ext .js,.vue ./src/lang/langs", "format_language": "prettier ./src/lang/langs --write"}, "dependencies": {"@toast-ui/editor": "3.1.4", "axios": "^0.18.1", "babel-loader": "^8.4.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^3.6.5", "dayjs": "^1.11.13", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^5.6.0", "element-tree-grid": "0.1.4", "element-ui": "^2.15.14", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-base64": "3.6.0", "js-cookie": "2.2.0", "js-md5": "0.7.3", "jsencrypt": "3.2.1", "jsonlint": "1.6.3", "jszip": "3.2.1", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pinyin": "2.9.0", "prettier": "^3.4.2", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "vue": "^2.7.16", "vue-carousel": "^0.18.0", "vue-count-to": "1.0.13", "vue-echarts": "^6.7.3", "vue-eslint-parser": "^9.4.3", "vue-i18n": "^8.28.2", "vue-i18n-bridge": "^9.14.1", "vue-json-viewer": "2.2.16", "vue-router": "^3.0.2", "vue-splitpane": "1.0.4", "vue-wysiwyg": "^1.7.2", "vuedraggable": "2.20.0", "vuex": "^3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "eslint-plugin-vue-libs": "^4.0.0", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "^2.7.16"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/deatil/larke-admin-frontend/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["2.13.2", "vue", "admin", "dashboard", "element-ui", "boilerplate", "larke-admin", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/deatil/larke-admin-frontend.git"}}