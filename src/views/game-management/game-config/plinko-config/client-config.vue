<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.plinkoClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('plinko.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-list-container">
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('plinko.search_configurations')"
            style="width: 300px; margin-right: 10px;"
            clearable
            @input="handleSearch"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="fetchConfigurations"
            :loading="loading"
          >
            {{ $t('plinko.refresh') }}
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredConfigurations"
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          class="border-gray"
          fit
          highlight-current-row
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column
            prop="id"
            label="Configuration ID"
            align="center"
          />

          <el-table-column
            prop="color"
            :label="$t('plinko.color')"
            align="center"
          >
            <template slot-scope="scope">
              <div
                class="color-indicator"
                :style="{ backgroundColor: scope.row.color }"
              />
              <span style="margin-left: 8px;">{{ scope.row.color }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="position"
            :label="$t('plinko.position')"
            align="center"
          />

          <el-table-column
            prop="rate"
            :label="$t('plinko.rate')"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.rate }}%
            </template>
          </el-table-column>

          <el-table-column
            prop="number"
            :label="$t('plinko.number')"
            align="center"
          />

          <el-table-column
            prop="calculation"
            :label="$t('plinko.calculation')"
            align="center"
          />

          <el-table-column
            :label="$t('plinko.actions')"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tooltip
                :content="$t('plinko.edit_configuration')"
                placement="top"
              >
                <el-button
                  type="primary"
                  icon="el-icon-edit"
                  size="mini"
                  circle
                  @click="editConfiguration(scope.row)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-show="total > 0"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- Edit Configuration Dialog -->
    <el-dialog
      :title="editDialogTitle"
      :visible.sync="editDialogVisible"
      width="600px"
      @close="resetEditForm"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="validationRules"
        label-width="120px"
      >
        <el-form-item
          :label="$t('plinko.color')"
          prop="color"
        >
          <el-select
            v-model="editForm.color"
            :placeholder="$t('plinko.select_color')"
          >
            <el-option
              :label="$t('plinko.blue')"
              value="blue"
            />
            <el-option
              :label="$t('plinko.red')"
              value="red"
            />
            <el-option
              :label="$t('plinko.green')"
              value="green"
            />
            <el-option
              :label="$t('plinko.yellow')"
              value="yellow"
            />
            <el-option
              :label="$t('plinko.purple')"
              value="purple"
            />
            <el-option
              :label="$t('plinko.orange')"
              value="orange"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('plinko.position')"
          prop="position"
        >
          <el-input-number
            v-model="editForm.position"
            :min="0"
            :max="16"
            :disabled="true"
            controls-position="right"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item
          :label="$t('plinko.rate') + ' (%)'"
          prop="rate"
        >
          <el-input-number
            v-model="editForm.rate"
            :min="0.001"
            :max="1"
            :step="0.001"
            :precision="3"
            controls-position="right"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item
          :label="$t('plinko.number')"
          prop="number"
        >
          <el-input-number
            v-model="editForm.number"
            :min="1"
            :max="1000"
            controls-position="right"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item
          :label="$t('plinko.calculation')"
          prop="calculation"
        >
          <el-select
            v-model="editForm.calculation"
            :placeholder="$t('plinko.select_calculation')"
          >
            <el-option
              :label="$t('plinko.multiplier')"
              value="multiplier"
            />
            <el-option
              :label="$t('plinko.addition')"
              value="addition"
            />
            <el-option
              :label="$t('plinko.percentage')"
              value="percentage"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="editDialogVisible = false">
          {{ $t('plinko.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="saveConfiguration"
          :loading="saving"
        >
          {{ isEditing ? $t('plinko.save') : $t('plinko.create') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPlinkoConfigs, updatePlinkoConfig, createPlinkoConfig } from '@/api/plinko'

export default {
  name: 'PlinkoClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      searchQuery: '',
      configurations: [],
      filteredConfigurations: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      clientId: null,
      clientName: '',

      // Edit dialog
      editDialogVisible: false,
      isEditing: false,
      editForm: {
        id: null,
        color: '',
        position: 0,
        rate: 0.01,
        number: 1,
        calculation: 'multiplier'
      },
      editRules: {}
    }
  },
  computed: {
    editDialogTitle () {
      return this.isEditing ? this.$t('plinko.edit_configuration') : this.$t('plinko.create_configuration')
    },

    validationRules () {
      return {
        color: [
          { required: true, message: this.$t('plinko.color_required'), trigger: 'change' }
        ],
        position: [
          { required: true, message: this.$t('plinko.position_required'), trigger: 'blur' }
        ],
        rate: [
          { required: true, message: this.$t('plinko.rate_required'), trigger: 'blur' }
        ],
        number: [
          { required: true, message: this.$t('plinko.number_required'), trigger: 'blur' }
        ],
        calculation: [
          { required: true, message: this.$t('plinko.calculation_required'), trigger: 'change' }
        ]
      }
    },

    searchFilteredConfigs () {
      if (!this.searchQuery) {
        return this.configurations
      }
      return this.configurations.filter(config =>
        config.color.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        config.calculation.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        config.id.toString().includes(this.searchQuery)
      )
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfigurations()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/plinko-config')
    },

    async fetchConfigurations () {
      this.loading = true
      try {
        const params = {
          client_id: this.clientId,
          page: this.currentPage,
          per_page: this.pageSize
        }

        const response = await getPlinkoConfigs(params)
        this.configurations = response.data || []
        this.total = response.data.total || 0
        this.updateFilteredList()
      } catch (error) {
        console.error('Error fetching configurations:', error)
        this.$message.error(this.$t('plinko.fetch_configurations_failed'))
        this.configurations = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    handleSearch () {
      this.updateFilteredList()
    },

    updateFilteredList () {
      this.filteredConfigurations = this.searchFilteredConfigs
    },

    editConfiguration (config) {
      this.isEditing = true
      this.editForm = {
        id: config.id,
        color: config.color,
        position: config.position,
        rate: config.rate,
        number: config.number,
        calculation: config.calculation
      }
      this.editDialogVisible = true
    },

    async saveConfiguration () {
      try {
        await this.$refs.editForm.validate()

        this.saving = true

        const data = {
          color: this.editForm.color,
          position: this.editForm.position,
          rate: this.editForm.rate,
          number: this.editForm.number,
          calculation: this.editForm.calculation,
          client_id: this.clientId
        }

        if (this.isEditing) {
          await updatePlinkoConfig(this.editForm.id, data)
          this.$message.success(this.$t('plinko.configuration_updated_successfully'))
        } else {
          await createPlinkoConfig(data)
          this.$message.success(this.$t('plinko.configuration_created_successfully'))
        }

        this.editDialogVisible = false
        this.fetchConfigurations()
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error(this.$t('plinko.failed_to_save_configuration'))
      } finally {
        this.saving = false
      }
    },

    resetEditForm () {
      this.editForm = {
        id: null,
        color: '',
        position: 0,
        rate: 0.01,
        number: 1,
        calculation: 'multiplier'
      }
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields()
      }
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchConfigurations()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchConfigurations()
    }
  }
}
</script>

<style scoped>
.config-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}

.color-indicator {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid #dcdfe6;
  vertical-align: middle;
}

.dialog-footer {
  text-align: right;
}
</style>
