<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.diceConfig') }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('dice.back_to_game_config') }}
        </el-button>
      </div>

      <div class="client-list-container">
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('dice.search_clients')"
            style="width: 300px; margin-right: 10px;"
            clearable
            @input="handleSearch"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="fetchClientList"
            :loading="loading"
          >
            {{ $t('common.refresh') }}
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredClientList"
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          class="border-gray"
          fit
          highlight-current-row
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column
            prop="id"
            :label="$t('dice.client_id')"
            width="100"
            align="center"
          />

          <el-table-column
            prop="name"
            :label="$t('dice.client_name')"
            min-width="200"
          />

          <el-table-column
            :label="$t('common.action')"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-setting"
                @click="viewClientConfigs(scope.row)"
              >
                {{ $t('common.configure') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-show="total > 0"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getClientList } from '@/api/dice'

export default {
  name: 'DiceConfig',
  data () {
    return {
      loading: false,
      searchQuery: '',
      clientList: [],
      filteredClientList: [],
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  computed: {
    // Filter clients based on search query
    searchFilteredClients () {
      if (!this.searchQuery) {
        return this.clientList
      }
      return this.clientList.filter(client =>
        client.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        client.id.toString().includes(this.searchQuery)
      )
    }
  },
  created () {
    this.fetchClientList()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/game-config/overview')
    },

    async fetchClientList () {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          per_page: this.pageSize
        }

        const response = await getClientList(params)
        this.clientList = response.data.data || []
        this.total = response.data.total || 0
        this.updateFilteredList()
      } catch (error) {
        console.error('Error fetching client list:', error)
        this.$message.error(this.$t('dice.fetch_client_list_failed'))
        this.clientList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    handleSearch () {
      this.updateFilteredList()
    },

    updateFilteredList () {
      this.filteredClientList = this.searchFilteredClients
    },

    viewClientConfigs (client) {
      // Navigate to client-specific configuration page
      this.$router.push({
        name: 'DiceClientConfig',
        params: { clientId: client.id },
        query: { clientName: client.name }
      })
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchClientList()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchClientList()
    }
  }
}
</script>

<style scoped>
.client-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
