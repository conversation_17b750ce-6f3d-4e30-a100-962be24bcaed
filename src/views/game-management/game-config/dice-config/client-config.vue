<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.diceClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('dice.back_to_dice_config') }}
        </el-button>
      </div>

      <div class="config-container">
        <!-- Configuration Mode Toggle -->
        <div class="mode-toggle-section">
          <span class="mode-label">{{ $t('dice.setup') }}</span>
          <el-radio-group
            v-model="configMode"
            @change="handleModeChange"
            class="mode-radio-group"
          >
            <el-radio label="auto">
              {{ $t('dice.auto') }}
            </el-radio>
            <el-radio label="manual">
              {{ $t('dice.manual') }}
            </el-radio>
          </el-radio-group>
        </div>

        <!-- Loading State -->
        <div
          v-loading="loading"
          class="config-content"
        >
          <el-form
            ref="configForm"
            :model="formData"
            :rules="formRules"
            class="config-form"
          >
            <!-- Max Odds Field - Only shown in Auto mode -->
            <div
              v-if="configMode === 'auto'"
              class="max-odds-section"
            >
              <div class="max-odds-field">
                <span class="field-label">{{ $t('dice.max_odds') }}</span>
                <el-input-number
                  v-model="formData.maxOdds"
                  :min="1"
                  :max="100"
                  :step="0.1"
                  :precision="1"
                  controls-position="right"
                  class="max-odds-input"
                />
              </div>
            </div>

            <!-- Rolling Up and Rolling Down Tables - Shown in both modes -->
            <div class="tables-section">
              <div class="tables-container">
                <!-- Rolling Up Table -->
                <div class="table-section">
                  <h3 class="table-title">
                    {{ $t('dice.rolling_up') }}
                  </h3>
                  <el-table
                    :data="paginatedRollingUp"
                    class="config-table"
                    size="small"
                    height="400"
                  >
                    <el-table-column
                      prop="prediction"
                      :label="$t('dice.prediction')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-input-number
                          v-model="scope.row.prediction"
                          :min="4"
                          :max="98"
                          controls-position="right"
                          size="mini"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="winrate"
                      :label="$t('dice.winrate')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <span class="readonly-winrate">{{ scope.row.winrate }}%</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="multiplier"
                      :label="$t('common.multiplier')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-input-number
                          v-model="scope.row.multiplier"
                          :min="1"
                          :max="1000"
                          :step="0.0001"
                          :precision="4"
                          controls-position="right"
                          size="mini"
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    @current-change="handleUpPageChange"
                    :current-page="upCurrentPage"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="formData.rollingUp.length"
                    class="table-pagination"
                  />
                </div>

                <!-- Rolling Down Table -->
                <div class="table-section">
                  <h3 class="table-title">
                    {{ $t('dice.rolling_down') }}
                  </h3>
                  <el-table
                    :data="paginatedRollingDown"
                    class="config-table"
                    size="small"
                    height="400"
                  >
                    <el-table-column
                      prop="prediction"
                      :label="$t('dice.prediction')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-input-number
                          v-model="scope.row.prediction"
                          :min="1"
                          :max="95"
                          controls-position="right"
                          size="mini"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="winrate"
                      :label="$t('dice.winrate')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <span class="readonly-winrate">{{ scope.row.winrate }}%</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="multiplier"
                      :label="$t('common.multiplier')"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <el-input-number
                          v-model="scope.row.multiplier"
                          :min="1"
                          :max="1000"
                          :step="0.0001"
                          :precision="4"
                          controls-position="right"
                          size="mini"
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    @current-change="handleDownPageChange"
                    :current-page="downCurrentPage"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="formData.rollingDown.length"
                    class="table-pagination"
                  />
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                {{ configMode === 'auto' ? $t('dice.apply_auto_configuration') : $t('dice.save_manual_configuration') }}
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                {{ $t('common.reset') }}
              </el-button>
              <el-button
                v-if="configMode === 'manual'"
                @click="loadDefaultValues"
                icon="el-icon-download"
              >
                {{ $t('dice.load_defaults') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDiceConfigs, createDiceConfig, updateDiceConfig } from '@/api/dice'

export default {
  name: 'DiceClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configMode: 'auto', // 'auto' or 'manual'
      configId: null, // Store configuration ID for updates

      // Pagination
      upCurrentPage: 1,
      downCurrentPage: 1,
      pageSize: 20,

      // Unified form data
      formData: {
        maxOdds: 97.6,
        rollingUp: [],
        rollingDown: []
      }
    }
  },
  computed: {
    // Dynamic form rules based on mode
    formRules () {
      const rules = {}

      // Add maxOdds validation only for auto mode
      if (this.configMode === 'auto') {
        rules.maxOdds = [
          { required: true, message: this.$t('dice.max_odds_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: this.$t('dice.max_odds_range'), trigger: 'blur' }
        ]
      }

      return rules
    },

    // Paginated data for Rolling Up table
    paginatedRollingUp () {
      const start = (this.upCurrentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.formData.rollingUp.slice(start, end)
    },

    // Paginated data for Rolling Down table
    paginatedRollingDown () {
      const start = (this.downCurrentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.formData.rollingDown.slice(start, end)
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    // Initialize with empty arrays first, then fetch from API
    this.formData.rollingUp = []
    this.formData.rollingDown = []
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/dice-config')
    },

    async fetchConfiguration () {
      this.loading = true

      try {
        const params = {
          client_id: this.clientId
        }

        const response = await getDiceConfigs(params)

        // Handle different possible response structures
        let configData = null

        if (response.data) {
          // Check if response.data is an array (multiple configs)
          if (Array.isArray(response.data)) {
            if (response.data.length > 0) {
              configData = response.data[0] // Take the first configuration
            }
            // eslint-disable-next-line brace-style
          }
          // Check if response.data.data exists (nested data structure)
          else if (response.data.data) {
            if (Array.isArray(response.data.data)) {
              if (response.data.data.length > 0) {
                configData = response.data.data[0]
              }
            } else {
              configData = response.data.data
            }
            // eslint-disable-next-line brace-style
          }
          // Direct data object
          else {
            configData = response.data
          }
        }

        if (configData) {
          this.loadConfigurationData(configData)
        } else {
          this.initializeDefaultData()
          this.$message.info(this.$t('dice.no_config_found_using_defaults'))
        }
      } catch (error) {
        this.initializeDefaultData()
        this.$message.warning(this.$t('dice.failed_to_load_config_using_defaults'))
      } finally {
        this.loading = false
      }
    },

    loadConfigurationData (data) {
      // Store configuration ID for updates
      this.configId = data.id

      // Set the mode based on setup field or fallback to mode field
      let detectedMode = 'auto' // default
      if (data.setup !== undefined) {
        detectedMode = data.setup === '1' || data.setup === 1 ? 'auto' : 'manual'
      } else if (data.mode) {
        detectedMode = data.mode
      }
      this.configMode = detectedMode

      // Extract max odds value
      const maxOddsValue = data.max_odds || data.maxOdds || 97.6

      // Extract rolling data
      const rollingUpData = data.up || data.rollingUp
      const rollingDownData = data.down || data.rollingDown

      // Load unified form data - map API response format
      this.formData = {
        maxOdds: maxOddsValue,
        rollingUp: rollingUpData && Array.isArray(rollingUpData) && rollingUpData.length > 0
          ? rollingUpData
          : this.getDefaultRollingUp(),
        rollingDown: rollingDownData && Array.isArray(rollingDownData) && rollingDownData.length > 0
          ? rollingDownData
          : this.getDefaultRollingDown()
      }
    },

    handleModeChange (mode) {
      this.$message.info(this.$t('dice.switched_to_mode', { mode: mode === 'auto' ? this.$t('dice.auto') : this.$t('dice.manual') }))

      // Clear form validation when switching modes
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate()
        }
      })
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          setup: this.configMode === 'auto' ? '1' : '0',
          up: this.formData.rollingUp,
          down: this.formData.rollingDown
        }

        // Add max_odds only for auto mode (note the underscore)
        if (this.configMode === 'auto') {
          data.max_odds = this.formData.maxOdds
        }

        // Use update if we have an existing configuration, otherwise create
        if (this.configId) {
          await updateDiceConfig(this.configId, data)
          this.$message.success(this.$t('dice.config_updated_successfully'))
        } else {
          const response = await createDiceConfig(data)
          if (response.data && response.data.id) {
            this.configId = response.data.id
          }
          this.$message.success(this.$t('dice.config_created_successfully'))
        }
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error(this.$t('dice.failed_to_save_config'))
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.formData = {
        maxOdds: 97.6,
        rollingUp: this.getDefaultRollingUp(),
        rollingDown: this.getDefaultRollingDown()
      }

      if (this.$refs.configForm) {
        this.$refs.configForm.resetFields()
      }

      this.$message.success(this.$t('dice.form_reset_to_defaults'))
    },

    loadDefaultValues () {
      this.formData.rollingUp = this.getDefaultRollingUp()
      this.formData.rollingDown = this.getDefaultRollingDown()
      this.$message.success(this.$t('dice.default_values_loaded'))
    },

    // Initialize default data arrays
    initializeDefaultData () {
      this.formData.rollingUp = this.getDefaultRollingUp()
      this.formData.rollingDown = this.getDefaultRollingDown()
    },

    // Pagination handlers
    handleUpPageChange (page) {
      this.upCurrentPage = page
    },

    handleDownPageChange (page) {
      this.downCurrentPage = page
    },

    // Generate default Rolling Up data (predictions 4-98)
    getDefaultRollingUp () {
      const data = []
      for (let prediction = 4; prediction <= 98; prediction++) {
        const winrate = 99 - prediction
        const multiplier = parseFloat((99 / winrate).toFixed(4))
        data.push({
          prediction,
          winrate,
          multiplier
        })
      }
      return data
    },

    // Generate default Rolling Down data (predictions 1-95)
    getDefaultRollingDown () {
      const data = []
      for (let prediction = 1; prediction <= 95; prediction++) {
        const winrate = prediction
        const multiplier = parseFloat((99 / winrate).toFixed(4))
        data.push({
          prediction,
          winrate,
          multiplier
        })
      }
      return data
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.mode-toggle-section {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.mode-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.mode-radio-group {
  margin-left: 10px;
}

.config-content {
  min-height: 400px;
}

.config-form {
  padding: 0;
}

/* Max Odds Section - Only in Auto Mode */
.max-odds-section {
  margin-bottom: 30px;
}

.max-odds-field {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.field-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  min-width: 80px;
}

.max-odds-input {
  width: 120px;
}

/* Tables Section - Shown in both modes */
.tables-section {
  margin-bottom: 30px;
}

.tables-container {
  display: flex;
  gap: 30px;
  justify-content: space-between;
}

.table-section {
  flex: 1;
  min-width: 0;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.config-table {
  width: 100%;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.table-pagination {
  margin-top: 15px;
  text-align: center;
}

.readonly-winrate {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .mode-toggle-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .tables-container {
    flex-direction: column;
    gap: 20px;
  }

  .max-odds-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* Animation for mode switching */
.max-odds-section, .tables-section {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
