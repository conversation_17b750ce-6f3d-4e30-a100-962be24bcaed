<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.massKenoClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('massKeno.back_to_client_list') }}
        </el-button>
      </div>

      <div>
        <el-button
          style="margin-bottom: 12px"
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          {{ $t('massKeno.save_configuration') }}
        </el-button>
        <div style="display: flex; align-items: center; margin-bottom: 12px">
          <span style="text-align: right">{{ $t('massKeno.max_prize') }}</span>
          <el-input-number
            v-model="max_prize"
            style="margin-left: 10px"
          />
        </div>
        <div class="body_mass">
          <!-- Bonus -->
          <div class="left">
            <div style="margin-bottom: 12px">
              {{ $t('massKeno.bonus') }}
            </div>
            <el-collapse v-model="activeNames">
              <el-collapse-item
                :title="String(item.number)"
                v-for="(item, index) in bonus"
                :key="index"
              >
                <div style="display: flex; align-items: center; gap: 12px">
                  <span style="width: 100px; text-align: right">{{ $t('massKeno.number') }}</span>
                  <el-input
                    v-model="item.number"
                    clearable
                  />
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-top: 10px;
                  "
                >
                  <span style="width: 100px; text-align: right">{{ $t('massKeno.probability') }}</span>
                  <el-input
                    v-model="item.probability"
                    clearable
                  />
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <div class="left">
            <div style="margin-bottom: 12px">
              {{ $t('massKeno.payouts') }}
            </div>
            <el-collapse v-model="activeNames">
              <el-collapse-item
                :title="String(item.label)"
                v-for="(item, index) in payouts"
                :key="index"
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    margin-bottom: 12px;
                  "
                  v-for="(item2, index2) in item.cheren"
                  :key="index2"
                >
                  <span style="width: 100px; text-align: right">
                    {{ item2.label }}
                  </span>
                  <el-input
                    v-model="item2.value"
                    clearable
                  />
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import {
  getMassKenoConfigs,
  updateMassKenoConfig
} from '@/api/mass-keno'
// Get global properties
const { proxy } = getCurrentInstance()
const clientId = proxy.$route.params.clientId
const clientName = proxy.$route.query.clientName
// Reactive data
const activeNames = ref([])
const activeNames2 = ref([])
const detailLoading = ref(false)
const submitLoading = ref(false)
const rules = ref({})
const form = ref({})
const config = ref({})
const payouts = ref([])
const bonus = ref([])
const max_prize = ref('')

// Methods
const submit = () => {
  submitLoading.value = true

  const obj = {
    max_prize: max_prize.value,
    bonus: bonus.value
  }

  const jsonData = {}
  payouts.value.forEach((item) => {
    const label = item.label.toString()
    jsonData[label] = {}
    item.cheren.forEach((cherenItem) => {
      jsonData[label][cherenItem.label] = cherenItem.value
    })
  })
  obj.payout = jsonData

  updateMassKenoConfig(clientId, {
    config: JSON.stringify(obj)
  })
    .then((response) => {
      proxy.$message({
        message: proxy.$t('massKeno.config_updated_successfully'),
        type: 'success',
        duration: 1 * 1000
      })
    })
    .catch((error) => {
      console.error('Error saving configuration:', error)
      const errorMessage = error.response?.data?.message || proxy.$t('massKeno.failed_to_save_config')
      proxy.$message({
        message: errorMessage,
        type: 'error',
        duration: 3 * 1000
      })
    })
    .finally(() => {
      submitLoading.value = false
    })
}

const goBack = () => {
  proxy.$router.push('/game-management/mass-keno-config')
}

const fetchData = (id) => {
  detailLoading.value = true

  getMassKenoConfigs({ client_id: id })
    .then((response) => {
      try {
        const dataConfig = response.data.config
        config.value = dataConfig
        bonus.value = dataConfig.bonus
        max_prize.value = dataConfig.max_prize

        const resultArray = []

        for (const key in config.value.payout) {
          const cherenArray = []
          for (const innerKey in config.value.payout[key]) {
            cherenArray.push({
              label: innerKey,
              value: config.value.payout[key][innerKey]
            })
          }
          resultArray.push({ label: parseInt(key), cheren: cherenArray })
        }
        payouts.value = resultArray
      } catch (error) {
        console.error('Error parsing configuration data:', error)
        proxy.$message({
          message: proxy.$t('massKeno.failed_to_parse_config'),
          type: 'warning',
          duration: 3 * 1000
        })
      }
    })
    .catch((error) => {
      console.error('Error fetching configuration:', error)
      proxy.$message({
        message: proxy.$t('massKeno.failed_to_load_config'),
        type: 'error',
        duration: 3 * 1000
      })
    })
    .finally(() => {
      detailLoading.value = false
    })
}

// Lifecycle
onMounted(() => {
  fetchData(clientId)
})
</script>

<style scoped>
.body_mass {
  display: flex;
  gap: 20px;
}
.left {
  flex: 1;
  width: 100%;
  border: 1px solid #dfe6ec;
  padding: 12px;
}
</style>
