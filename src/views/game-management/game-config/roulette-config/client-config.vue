<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.rouletteConfig') }} {{ $t('roulette.game_configuration') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('roulette.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <div v-loading="loading">
          <el-form
            ref="configForm"
            :model="formData"
            :rules="validationRules"
            label-width="150px"
            class="config-form"
          >
            <!-- Basic Settings -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.interval')"
                  prop="interval"
                >
                  <el-input-number
                    v-model="formData.interval"
                    :min="1"
                    :max="3600"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.enter_interval_seconds')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.waiting_time')"
                  prop="waitingTime"
                >
                  <el-input-number
                    v-model="formData.waitingTime"
                    :min="1"
                    :max="300"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.enter_waiting_time_seconds')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Time Settings -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.start_time')"
                  prop="startTime"
                >
                  <el-time-picker
                    v-model="formData.startTime"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    :placeholder="$t('roulette.select_start_time')"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.end_time')"
                  prop="endTime"
                >
                  <el-time-picker
                    v-model="formData.endTime"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    :placeholder="$t('roulette.select_end_time')"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Betting Limits -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.minimum_bet')"
                  prop="minimumBet"
                >
                  <el-input-number
                    v-model="formData.minimumBet"
                    :min="0"
                    :max="999999999"
                    :precision="9"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.enter_minimum_bet_amount')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('roulette.maximum_bet')"
                  prop="maximumBet"
                >
                  <el-input-number
                    v-model="formData.maximumBet"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.enter_maximum_bet_amount')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Choice Configurations -->
            <el-divider content-position="left">
              {{ $t('roulette.choice_configurations') }}
            </el-divider>

            <!-- Green Choice -->
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.green_probability')"
                  prop="greenProbability"
                >
                  <el-input-number
                    v-model="formData.greenProbability"
                    :min="0"
                    :max="100"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.green_probability_placeholder')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.green_multiplier')"
                  prop="greenMultiplier"
                >
                  <el-input-number
                    v-model="formData.greenMultiplier"
                    :min="0"
                    :max="1000"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.green_multiplier_placeholder')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Purple Choice -->
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.purple_probability')"
                  prop="purpleProbability"
                >
                  <el-input-number
                    v-model="formData.purpleProbability"
                    :min="0"
                    :max="100"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.purple_probability_placeholder')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.purple_multiplier')"
                  prop="purpleMultiplier"
                >
                  <el-input-number
                    v-model="formData.purpleMultiplier"
                    :min="0"
                    :max="1000"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.purple_multiplier_placeholder')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Chest Choice -->
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.chest_probability')"
                  prop="chestProbability"
                >
                  <el-input-number
                    v-model="formData.chestProbability"
                    :min="0"
                    :max="100"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.chest_probability_placeholder')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item
                  :label="$t('roulette.chest_multiplier')"
                  prop="chestMultiplier"
                >
                  <el-input-number
                    v-model="formData.chestMultiplier"
                    :min="0"
                    :max="1000"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('roulette.chest_multiplier_placeholder')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                {{ $t('roulette.save_configuration') }}
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                {{ $t('roulette.reset') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getRouletteConfigs, updateRouletteConfig } from '@/api/roulette'

export default {
  name: 'RouletteClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configId: null, // Store configuration ID for updates

      // Form data
      formData: {
        interval: 10,
        waitingTime: 5,
        startTime: '00:00:00',
        endTime: '23:59:59',
        minimumBet: 0.000000001,
        maximumBet: 999999,
        greenProbability: 49,
        greenMultiplier: 2,
        purpleProbability: 50,
        purpleMultiplier: 2,
        chestProbability: 1,
        chestMultiplier: 14
      }
    }
  },
  computed: {
    validationRules () {
      return {
        interval: [
          { required: true, message: this.$t('roulette.interval_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 3600, message: this.$t('roulette.interval_range'), trigger: 'blur' }
        ],
        waitingTime: [
          { required: true, message: this.$t('roulette.waiting_time_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 300, message: this.$t('roulette.waiting_time_range'), trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: this.$t('roulette.start_time_required'), trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: this.$t('roulette.end_time_required'), trigger: 'blur' }
        ],
        minimumBet: [
          { required: true, message: this.$t('roulette.minimum_bet_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('roulette.minimum_bet_range'), trigger: 'blur' }
        ],
        maximumBet: [
          { required: true, message: this.$t('roulette.maximum_bet_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('roulette.maximum_bet_range'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= this.formData.minimumBet) {
                callback(new Error(this.$t('roulette.maximum_bet_greater_than_minimum')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        greenProbability: [
          { required: true, message: this.$t('roulette.green_probability_required'), trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: this.$t('roulette.probability_range'), trigger: 'blur' }
        ],
        greenMultiplier: [
          { required: true, message: this.$t('roulette.green_multiplier_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('roulette.multiplier_range'), trigger: 'blur' }
        ],
        purpleProbability: [
          { required: true, message: this.$t('roulette.purple_probability_required'), trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: this.$t('roulette.probability_range'), trigger: 'blur' }
        ],
        purpleMultiplier: [
          { required: true, message: this.$t('roulette.purple_multiplier_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('roulette.multiplier_range'), trigger: 'blur' }
        ],
        chestProbability: [
          { required: true, message: this.$t('roulette.chest_probability_required'), trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: this.$t('roulette.probability_range'), trigger: 'blur' }
        ],
        chestMultiplier: [
          { required: true, message: this.$t('roulette.chest_multiplier_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('roulette.multiplier_range'), trigger: 'blur' }
        ],
        callbackUrl: [
          { required: true, message: this.$t('roulette.callback_url_required'), trigger: 'blur' },
          { type: 'url', message: this.$t('roulette.callback_url_invalid'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/roulette-config')
    },

    async fetchConfiguration () {
      this.loading = true

      try {
        const params = {
          client_id: this.clientId
        }
        const response = await getRouletteConfigs(params)

        // Handle different possible response structures
        let configData = null
        if (response.data) {
          if (Array.isArray(response.data)) {
            configData = response.data.length > 0 ? response.data[0] : null
          } else if (response.data.data && Array.isArray(response.data.data)) {
            configData = response.data.data.length > 0 ? response.data.data[0] : null
          } else {
            configData = response.data
          }
        }

        if (configData) {
          this.populateForm(configData)
        } else {
          this.setDefaultValues()
        }
      } catch (error) {
        console.error('Error fetching configuration:', error)
        this.$message.warning(this.$t('roulette.no_config_found_using_defaults'))
        this.setDefaultValues()
      } finally {
        this.loading = false
      }
    },

    populateForm (data) {
      // Store configuration ID for updates
      this.configId = data.id

      // Parse the JSON config string
      let config = {}
      try {
        if (typeof data.config === 'string') {
          config = JSON.parse(data.config)
        } else if (typeof data.config === 'object') {
          config = data.config
        }
      } catch (error) {
        console.error('Error parsing config JSON:', error)
        this.$message.error(this.$t('roulette.error_parsing_config'))
        this.setDefaultValues()
        return
      }

      // Map parsed config to form data
      this.formData = {
        interval: config.interval || 10,
        waitingTime: config.waiting_time || 5,
        startTime: config.start_time || '00:00:00',
        endTime: config.end_time || '23:59:59',
        minimumBet: config.minimum_bet || 0.000000001,
        maximumBet: config.maximum_bet || 999999,
        greenProbability: config.choices?.green?.probability || 49,
        greenMultiplier: config.choices?.green?.multiplier || 2,
        purpleProbability: config.choices?.purple?.probability || 50,
        purpleMultiplier: config.choices?.purple?.multiplier || 2,
        chestProbability: config.choices?.chest?.probability || 1,
        chestMultiplier: config.choices?.chest?.multiplier || 14
      }
    },

    setDefaultValues () {
      this.formData = {
        interval: 10,
        waitingTime: 5,
        startTime: '00:00:00',
        endTime: '23:59:59',
        minimumBet: 0.000000001,
        maximumBet: 999999,
        greenProbability: 49,
        greenMultiplier: 2,
        purpleProbability: 50,
        purpleMultiplier: 2,
        chestProbability: 1,
        chestMultiplier: 14
      }
      this.configId = null
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Construct the JSON config object
        const configObject = {
          interval: this.formData.interval,
          waiting_time: this.formData.waitingTime,
          choices: {
            green: {
              probability: this.formData.greenProbability,
              multiplier: this.formData.greenMultiplier
            },
            purple: {
              probability: this.formData.purpleProbability,
              multiplier: this.formData.purpleMultiplier
            },
            chest: {
              probability: this.formData.chestProbability,
              multiplier: this.formData.chestMultiplier
            }
          },
          start_time: this.formData.startTime,
          end_time: this.formData.endTime,
          minimum_bet: this.formData.minimumBet,
          maximum_bet: this.formData.maximumBet,
          callback_urls: {
            result: this.formData.callbackUrl
          }
        }

        // Prepare data according to API specification
        const data = {
          config: JSON.stringify(configObject),
          client_id: this.clientId
        }

        // Use update if we have an existing configuration
        if (this.configId) {
          await updateRouletteConfig(this.configId, data)
          this.$message.success(this.$t('roulette.config_updated_successfully'))
        } else {
          this.$message.error(this.$t('roulette.no_config_id_found'))
        }
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error(this.$t('roulette.failed_to_save_config'))
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.setDefaultValues()
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate()
        }
      })
      this.$message.info(this.$t('roulette.form_reset_to_defaults'))
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 1000px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}

.el-divider {
  margin: 30px 0 20px 0;
}
</style>
