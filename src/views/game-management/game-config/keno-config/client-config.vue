<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>Keno Game Configuration - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> Back to Client List
        </el-button>
      </div>

      <div class="config-container">
        <div v-loading="loading">
          <el-form
            ref="configForm"
            :model="formData"
            :rules="formRules"
            label-width="150px"
            class="config-form"
          >
            <!-- Basic Settings -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="Interval"
                  prop="interval"
                >
                  <el-input-number
                    v-model="formData.interval"
                    :min="1"
                    :max="3600"
                    controls-position="right"
                    style="width: 100%;"
                    placeholder="Enter interval in seconds"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  label="Status"
                  prop="status"
                >
                  <el-switch
                    v-model="formData.status"
                    active-text="Active"
                    inactive-text="Inactive"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Time Settings -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="Open Time"
                  prop="openTime"
                >
                  <el-time-picker
                    v-model="formData.openTime"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    placeholder="Select open time"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  label="Close Time"
                  prop="closeTime"
                >
                  <el-time-picker
                    v-model="formData.closeTime"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    placeholder="Select close time"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                Save Configuration
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                Reset
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getKenoConfigs, updateKenoConfig } from '@/api/keno'

export default {
  name: 'KenoClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configId: null, // Store configuration ID for updates

      // Form data
      formData: {
        interval: 60,
        status: 1, // 1 = active, 0 = inactive
        openTime: '00:00:00',
        closeTime: '23:59:59'
      }
    }
  },
  computed: {
    formRules () {
      return {
        interval: [
          { required: true, message: 'Interval is required', trigger: 'blur' },
          { type: 'number', min: 1, max: 3600, message: 'Interval must be between 1 and 3600 seconds', trigger: 'blur' }
        ],
        status: [
          { required: true, message: 'Status is required', trigger: 'change' }
        ],
        openTime: [
          { required: true, message: 'Open time is required', trigger: 'blur' }
        ],
        closeTime: [
          { required: true, message: 'Close time is required', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && this.formData.openTime && value <= this.formData.openTime) {
                callback(new Error('Close time must be after open time'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/keno-config')
    },

    async fetchConfiguration () {
      this.loading = true

      try {
        const params = {
          client_id: this.clientId
        }
        const response = await getKenoConfigs(params)

        // Handle different possible response structures
        let configData = null
        if (response.data) {
          if (Array.isArray(response.data)) {
            configData = response.data.length > 0 ? response.data[0] : null
          } else if (response.data.data && Array.isArray(response.data.data)) {
            configData = response.data.data.length > 0 ? response.data.data[0] : null
          } else {
            configData = response.data
          }
        }

        if (configData) {
          this.populateForm(configData)
        } else {
          this.setDefaultValues()
        }
      } catch (error) {
        console.error('Error fetching configuration:', error)
        this.$message.warning('No existing configuration found. Using default values.')
        this.setDefaultValues()
      } finally {
        this.loading = false
      }
    },

    populateForm (data) {
      // Store configuration ID for updates
      this.configId = data.id

      // Map data to form fields
      this.formData = {
        interval: data.interval || 60,
        status: data.status !== undefined ? data.status : 1, // 1 = active, 0 = inactive
        openTime: data.open_time || '00:00:00',
        closeTime: data.close_time || '23:59:59'
      }
    },

    setDefaultValues () {
      this.formData = {
        interval: 60,
        status: 1, // 1 = active, 0 = inactive
        openTime: '00:00:00',
        closeTime: '23:59:59'
      }
      this.configId = null
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          interval: this.formData.interval,
          status: this.formData.status,
          open_time: this.formData.openTime,
          close_time: this.formData.closeTime,
          client_id: this.clientId
        }

        // Use update if we have an existing configuration
        if (this.configId) {
          await updateKenoConfig(this.configId, data)
          this.$message.success('Configuration updated successfully!')
        } else {
          this.$message.error('No configuration ID found. Cannot update.')
        }
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error('Failed to save configuration')
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.setDefaultValues()
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate()
        }
      })
      this.$message.info('Form reset to default values')
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
