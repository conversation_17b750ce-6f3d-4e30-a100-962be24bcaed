<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.crashClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('crash.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <div
          v-loading="loading"
          class="config-form"
        >
          <el-form
            ref="configForm"
            :model="formData"
            :rules="formRules"
            label-width="200px"
            label-position="left"
          >
            <!-- Betting Period Display -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('crash.betting_period_display')"
                  prop="bettingPeriodDisplay"
                >
                  <el-input-number
                    v-model="formData.bettingPeriodDisplay"
                    :min="1"
                    :max="300"
                    :step="1"
                    style="width: 100%;"
                    :placeholder="$t('crash.enter_betting_period_display')"
                  />
                  <div class="field-description">
                    {{ $t('crash.betting_period_display_desc') }}
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('crash.crash_animation')"
                  prop="crashAnimation"
                >
                  <el-input-number
                    v-model="formData.crashAnimation"
                    :min="1"
                    :max="60"
                    :step="1"
                    style="width: 100%;"
                    :placeholder="$t('crash.enter_crash_animation')"
                  />
                  <div class="field-description">
                    {{ $t('crash.crash_animation_desc') }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Exponential Value -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('crash.exponential_value')"
                  prop="exponentialValue"
                >
                  <el-input-number
                    v-model="formData.exponentialValue"
                    :min="1"
                    :max="100"
                    :step="1"
                    style="width: 100%;"
                    :placeholder="$t('crash.enter_exponential_value')"
                  />
                  <div class="field-description">
                    {{ $t('crash.exponential_value_desc') }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Weighted Random Float Range -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('crash.min_weighted_random_float')"
                  prop="minWeightedRandomFloat"
                >
                  <el-input-number
                    v-model="formData.minWeightedRandomFloat"
                    :min="0.01"
                    :max="99.99"
                    :step="0.01"
                    :precision="2"
                    style="width: 100%;"
                    :placeholder="$t('crash.enter_min_weighted_random_float')"
                  />
                  <div class="field-description">
                    {{ $t('crash.min_weighted_random_float_desc') }}
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('crash.max_weighted_random_float')"
                  prop="maxWeightedRandomFloat"
                >
                  <el-input-number
                    v-model="formData.maxWeightedRandomFloat"
                    :min="0.01"
                    :max="99.99"
                    :step="0.01"
                    :precision="2"
                    style="width: 100%;"
                    :placeholder="$t('crash.enter_max_weighted_random_float')"
                  />
                  <div class="field-description">
                    {{ $t('crash.max_weighted_random_float_desc') }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                {{ $t('crash.save_configuration') }}
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                {{ $t('common.reset') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getCrashConfigs, updateCrashConfig } from '@/api/crash'

export default {
  name: 'CrashClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configId: null, // Store configuration ID for updates

      // Form data
      formData: {
        bettingPeriodDisplay: 30,
        crashAnimation: 5,
        exponentialValue: 10,
        minWeightedRandomFloat: 1.00,
        maxWeightedRandomFloat: 70.00
      }
    }
  },
  computed: {
    formRules () {
      return {
        bettingPeriodDisplay: [
          { required: true, message: this.$t('crash.betting_period_display_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 300, message: this.$t('crash.betting_period_display_range'), trigger: 'blur' }
        ],
        crashAnimation: [
          { required: true, message: this.$t('crash.crash_animation_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 60, message: this.$t('crash.crash_animation_range'), trigger: 'blur' }
        ],
        exponentialValue: [
          { required: true, message: this.$t('crash.exponential_value_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: this.$t('crash.exponential_value_range'), trigger: 'blur' }
        ],
        minWeightedRandomFloat: [
          { required: true, message: this.$t('crash.min_weighted_random_float_required'), trigger: 'blur' },
          { type: 'number', min: 0.01, max: 99.99, message: this.$t('crash.min_weighted_random_float_range'), trigger: 'blur' }
        ],
        maxWeightedRandomFloat: [
          { required: true, message: this.$t('crash.max_weighted_random_float_required'), trigger: 'blur' },
          { type: 'number', min: 0.01, max: 99.99, message: this.$t('crash.max_weighted_random_float_range'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && this.formData.minWeightedRandomFloat && value <= this.formData.minWeightedRandomFloat) {
                callback(new Error(this.$t('crash.max_value_greater_than_min')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/crash-config')
    },

    async fetchConfiguration () {
      this.loading = true

      try {
        const params = {
          client_id: this.clientId
        }
        const response = await getCrashConfigs(params)

        // Handle different possible response structures
        let configData = null
        if (response.data) {
          if (Array.isArray(response.data)) {
            configData = response.data.length > 0 ? response.data[0] : null
          } else if (response.data.data && Array.isArray(response.data.data)) {
            configData = response.data.data.length > 0 ? response.data.data[0] : null
          } else {
            configData = response.data
          }
        }

        if (configData) {
          this.populateForm(configData)
        } else {
          this.setDefaultValues()
        }
      } catch (error) {
        console.error('Error fetching configuration:', error)
        this.$message.warning(this.$t('crash.no_config_found_using_defaults'))
        this.setDefaultValues()
      } finally {
        this.loading = false
      }
    },

    populateForm (data) {
      // Store configuration ID for updates
      this.configId = data.id

      // Map data to form fields
      this.formData = {
        bettingPeriodDisplay: data.betting_period_display || 30,
        crashAnimation: data.crash_animation || 5,
        exponentialValue: data.exponential_value || 10,
        minWeightedRandomFloat: data.min_weighted_random_float || 1.00,
        maxWeightedRandomFloat: data.max_weighted_random_float || 70.00
      }
    },

    setDefaultValues () {
      this.formData = {
        bettingPeriodDisplay: 30,
        crashAnimation: 5,
        exponentialValue: 10,
        minWeightedRandomFloat: 1.00,
        maxWeightedRandomFloat: 70.00
      }
      this.configId = null
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          betting_period_display: this.formData.bettingPeriodDisplay,
          crash_animation: this.formData.crashAnimation,
          exponential_value: this.formData.exponentialValue,
          min_weighted_random_float: this.formData.minWeightedRandomFloat,
          max_weighted_random_float: this.formData.maxWeightedRandomFloat,
          client_id: this.clientId
        }

        // Use update if we have an existing configuration
        if (this.configId) {
          await updateCrashConfig(this.configId, data)
          this.$message.success(this.$t('crash.config_updated_successfully'))
        } else {
          this.$message.error(this.$t('crash.no_config_id_cannot_update'))
        }
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error(this.$t('crash.failed_to_save_config'))
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.setDefaultValues()
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate()
        }
      })
      this.$message.info(this.$t('crash.form_reset_to_defaults'))
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}

.field-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
