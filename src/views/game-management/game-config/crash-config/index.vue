<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.crashConfig') }} - {{ $t('crash.client_list') }}</span>
      </div>

      <div class="client-list-container">
        <!-- Search and Filter -->
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('crash.search_clients')"
            style="width: 300px; margin-right: 10px;"
            clearable
            @input="handleSearch"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="fetchClientList"
          >
            {{ $t('common.search') }}
          </el-button>
        </div>

        <!-- Client List Table -->
        <el-table
          v-loading="loading"
          :data="clientList"
          border
          class="border-gray"
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            :label="$t('crash.client_id')"
            width="100"
            align="center"
          />
          <el-table-column
            prop="name"
            :label="$t('crash.client_name')"
            min-width="200"
          />
          <el-table-column
            :label="$t('common.action')"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-setting"
                @click="viewClientConfigs(scope.row)"
              >
                {{ $t('common.configure') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getClientList } from '@/api/crash'

export default {
  name: 'CrashConfig',
  data () {
    return {
      loading: false,
      clientList: [],
      searchQuery: '',
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  created () {
    this.fetchClientList()
  },
  methods: {
    async fetchClientList () {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }

        if (this.searchQuery.trim()) {
          params.search = this.searchQuery.trim()
        }

        const response = await getClientList(params)

        if (response.data) {
          this.clientList = response.data.data || response.data
          this.total = response.data.total || this.clientList.length
        }
      } catch (error) {
        console.error('Error fetching client list:', error)
        this.$message.error(this.$t('crash.fetch_client_list_failed'))
      } finally {
        this.loading = false
      }
    },

    handleSearch () {
      this.currentPage = 1
      this.fetchClientList()
    },

    viewClientConfigs (client) {
      // Navigate to client-specific configuration page
      this.$router.push({
        name: 'CrashClientConfig',
        params: { clientId: client.id },
        query: { clientName: client.name }
      })
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchClientList()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchClientList()
    }
  }
}
</script>

<style scoped>
.client-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
