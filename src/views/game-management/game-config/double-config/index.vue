<template>
  <div class="app-container">
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.doubleConfig') }} - {{ $t('double.client_list') }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
          icon="el-icon-arrow-left"
        >
          {{ $t('double.back_to_game_config') }}
        </el-button>
      </div>

      <div class="client-list-container">
        <!-- Search Filter -->
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('double.search_clients')"
            style="width: 300px;"
            @input="handleSearch"
            clearable
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
        </div>

        <!-- Client List Table -->
        <el-table
          v-loading="loading"
          :data="filteredClientList"
          border
          fit
          highlight-current-row
          class="border-gray"
        >
          <el-table-column
            :label="$t('double.client_id')"
            prop="id"
            width="100"
            align="center"
          />
          <el-table-column
            :label="$t('double.client_name')"
            prop="name"
            min-width="200"
          />
          <el-table-column
            :label="$t('common.action')"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="viewClientConfigs(scope.row)"
                icon="el-icon-setting"
              >
                {{ $t('common.configure') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            v-show="total > 0"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getClientList } from '@/api/double'

export default {
  name: 'DoubleConfig',
  data () {
    return {
      loading: false,
      clientList: [],
      filteredClientList: [],
      searchQuery: '',
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  computed: {
    searchFilteredClients () {
      if (!this.searchQuery) {
        return this.clientList
      }
      return this.clientList.filter(client =>
        client.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        client.id.toString().includes(this.searchQuery)
      )
    }
  },
  created () {
    this.fetchClientList()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/game-config')
    },

    async fetchClientList () {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          per_page: this.pageSize
        }

        const response = await getClientList(params)
        this.clientList = response.data.data || []
        this.total = response.data.total || 0
        this.updateFilteredList()
      } catch (error) {
        console.error('Error fetching client list:', error)
        this.$message.error(this.$t('double.fetch_client_list_failed'))
        this.clientList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    handleSearch () {
      this.updateFilteredList()
    },

    updateFilteredList () {
      this.filteredClientList = this.searchFilteredClients
    },

    viewClientConfigs (client) {
      // Navigate to client-specific configuration page
      this.$router.push({
        name: 'DoubleClientConfig',
        params: { clientId: client.id },
        query: { clientName: client.name }
      })
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchClientList()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchClientList()
    }
  }
}
</script>

<style scoped>
.client-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
