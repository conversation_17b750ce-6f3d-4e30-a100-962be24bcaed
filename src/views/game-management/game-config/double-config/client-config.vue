<template>
  <div class="app-container">
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.doubleClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
          icon="el-icon-arrow-left"
        >
          {{ $t('double.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <div v-loading="loading">
          <el-form
            ref="configForm"
            :model="formData"
            :rules="formRules"
            label-width="150px"
            class="config-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('double.red_prize')"
                  prop="redPrize"
                >
                  <el-input-number
                    v-model="formData.redPrize"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('double.enter_red_prize')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('double.black_prize')"
                  prop="blackPrize"
                >
                  <el-input-number
                    v-model="formData.blackPrize"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('double.enter_black_prize')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('double.special_prize')"
                  prop="specialPrize"
                >
                  <el-input-number
                    v-model="formData.specialPrize"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('double.enter_special_prize')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('double.min_amount')"
                  prop="minAmount"
                >
                  <el-input-number
                    v-model="formData.minAmount"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('double.enter_min_amount')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('double.max_amount')"
                  prop="maxAmount"
                >
                  <el-input-number
                    v-model="formData.maxAmount"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('double.enter_max_amount')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                {{ $t('double.save_configuration') }}
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                {{ $t('common.reset') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDoubleConfigs, updateDoubleConfig } from '@/api/double'

export default {
  name: 'DoubleClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configId: null, // Store configuration ID for updates

      // Form data
      formData: {
        redPrize: 2,
        blackPrize: 2,
        specialPrize: 14,
        minAmount: 1,
        maxAmount: 10000
      }
    }
  },
  computed: {
    formRules () {
      return {
        redPrize: [
          { required: true, message: this.$t('double.red_prize_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('double.red_prize_range'), trigger: 'blur' }
        ],
        blackPrize: [
          { required: true, message: this.$t('double.black_prize_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('double.black_prize_range'), trigger: 'blur' }
        ],
        specialPrize: [
          { required: true, message: this.$t('double.special_prize_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('double.special_prize_range'), trigger: 'blur' }
        ],
        minAmount: [
          { required: true, message: this.$t('double.min_amount_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('double.min_amount_range'), trigger: 'blur' }
        ],
        maxAmount: [
          { required: true, message: this.$t('double.max_amount_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('double.max_amount_range'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= this.formData.minAmount) {
                callback(new Error(this.$t('double.max_amount_greater_than_min')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/double-config')
    },

    async fetchConfiguration () {
      this.loading = true
      try {
        const params = {
          client_id: this.clientId
        }
        const response = await getDoubleConfigs(params)

        // Handle different possible response structures
        let configData = null
        if (response.data) {
          if (Array.isArray(response.data)) {
            configData = response.data.length > 0 ? response.data[0] : null
          } else if (response.data.data && Array.isArray(response.data.data)) {
            configData = response.data.data.length > 0 ? response.data.data[0] : null
          } else {
            configData = response.data
          }
        }

        if (configData) {
          this.populateForm(configData)
        } else {
          this.setDefaultValues()
        }
      } catch (error) {
        this.$message.warning(this.$t('double.no_config_found_using_defaults'))
        this.setDefaultValues()
      } finally {
        this.loading = false
      }
    },

    populateForm (data) {
      // Store configuration ID for updates
      this.configId = data.id

      // Map API response to form data
      this.formData = {
        redPrize: data.red_prize || 2,
        blackPrize: data.black_prize || 2,
        specialPrize: data.special_prize || 14,
        minAmount: data.min_amount || 1,
        maxAmount: data.max_amount || 10000
      }
    },

    setDefaultValues () {
      this.formData = {
        redPrize: 2,
        blackPrize: 2,
        specialPrize: 14,
        minAmount: 1,
        maxAmount: 10000
      }
      this.configId = null
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          red_prize: this.formData.redPrize,
          black_prize: this.formData.blackPrize,
          special_prize: this.formData.specialPrize,
          min_amount: this.formData.minAmount,
          max_amount: this.formData.maxAmount,
          client_id: this.clientId
        }

        // Use update if we have an existing configuration, otherwise create
        if (this.configId) {
          await updateDoubleConfig(this.configId, data)
          this.$message.success(this.$t('double.config_updated_successfully'))
        } else {
          this.$message.error(this.$t('double.failed_to_save_config'))
        }
      } catch (error) {
        this.$message.error(this.$t('double.failed_to_save_config'))
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.$refs.configForm.resetFields()
      this.setDefaultValues()
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
