<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.gameConfig') }}</span>
      </div>

      <div class="game-config-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.diceConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure dice game settings, including betting limits, odds, and game rules.</p>
                <el-button
                  type="primary"
                  @click="navigateToDiceConfig"
                  icon="el-icon-setting"
                >
                  Configure Dice Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.plinkoConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Plinko game settings, including board layout, multipliers, and drop mechanics.</p>
                <el-button
                  type="primary"
                  @click="navigateToPlinkoConfig"
                  icon="el-icon-setting"
                >
                  Configure Plinko Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.europeanRouletteConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure European Roulette game settings, including minimum and maximum betting amounts.</p>
                <el-button
                  type="primary"
                  @click="navigateToEuropeanRouletteConfig"
                  icon="el-icon-setting"
                >
                  Configure European Roulette
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.doubleConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Double game settings, including prize multipliers and betting limits.</p>
                <el-button
                  type="primary"
                  @click="navigateToDoubleConfig"
                  icon="el-icon-setting"
                >
                  Configure Double Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.rouletteConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Roulette game settings, including intervals, choices, probabilities, and betting limits.</p>
                <el-button
                  type="primary"
                  @click="navigateToRouletteConfig"
                  icon="el-icon-setting"
                >
                  Configure Roulette Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.kenoConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Keno game settings, including intervals, status, and time schedules.</p>
                <el-button
                  type="primary"
                  @click="navigateToKenoConfig"
                  icon="el-icon-setting"
                >
                  Configure Keno Game
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.crashConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Crash game settings, including betting periods, animations, and weighted random values.</p>
                <el-button
                  type="primary"
                  @click="navigateToCrashConfig"
                  icon="el-icon-setting"
                >
                  Configure Crash Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.ringConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Ring game settings, including complex multiplier structures and probability distributions.</p>
                <el-button
                  type="primary"
                  @click="navigateToRingConfig"
                  icon="el-icon-setting"
                >
                  Configure Ring Game
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- Third Row -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.minesConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Mines game settings, including multiplier values and game parameters.</p>
                <el-button
                  type="primary"
                  @click="navigateToMinesConfig"
                  icon="el-icon-setting"
                >
                  Configure Mines Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.fruitSlotsConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Fruit Slots game settings, including game name and configuration parameters.</p>
                <el-button
                  type="primary"
                  @click="navigateToFruitSlotsConfig"
                  icon="el-icon-setting"
                >
                  Configure Fruit Slots Game
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.massKenoConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Massachusetts Keno game settings, including draw intervals, betting limits, and operating hours.</p>
                <el-button
                  type="primary"
                  @click="navigateToMassKenoConfig"
                  icon="el-icon-setting"
                >
                  Configure Massachusetts Keno Game
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-card
              class="config-card"
              shadow="hover"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>{{ $t('route.oddstraderConfig') }}</span>
              </div>
              <div class="config-content">
                <p>Configure Oddstrader game settings, including currency, interval, and units configuration.</p>
                <el-button
                  type="primary"
                  @click="navigateToOddstraderConfig"
                  icon="el-icon-setting"
                >
                  Configure Oddstrader Game
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'GameConfig',
  data () {
    return {}
  },
  methods: {
    navigateToDiceConfig () {
      this.$router.push('/game-management/dice-config')
    },
    navigateToPlinkoConfig () {
      this.$router.push('/game-management/plinko-config')
    },
    navigateToEuropeanRouletteConfig () {
      this.$router.push('/game-management/european-roulette-config')
    },
    navigateToDoubleConfig () {
      this.$router.push('/game-management/double-config')
    },
    navigateToRouletteConfig () {
      this.$router.push('/game-management/roulette-config')
    },
    navigateToKenoConfig () {
      this.$router.push('/game-management/keno-config')
    },
    navigateToCrashConfig () {
      this.$router.push('/game-management/crash-config')
    },
    navigateToRingConfig () {
      this.$router.push('/game-management/ring-config')
    },
    navigateToMinesConfig () {
      this.$router.push('/game-management/mines-config')
    },
    navigateToFruitSlotsConfig () {
      this.$router.push('/game-management/fruit-slots-config')
    },
    navigateToMassKenoConfig () {
      this.$router.push('/game-management/mass-keno-config')
    },
    navigateToOddstraderConfig () {
      this.$router.push('/game-management/oddstrader-config')
    }
  }
}
</script>

<style scoped>
.game-config-container {
  padding: 20px 0;
}

.config-card {
  margin-bottom: 20px;
  min-height: 200px;
}

.config-content {
  text-align: center;
  padding: 20px;
}

.config-content p {
  margin-bottom: 20px;
  color: #666;
  line-height: 1.6;
}
</style>
