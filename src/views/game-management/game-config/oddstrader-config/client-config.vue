<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.oddstraderConfig') }} {{ $t('oddstrader.configuration') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
          icon="el-icon-arrow-left"
        >
          {{ $t('oddstrader.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <!-- Search and Add Button -->
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('oddstrader.search_configurations')"
            style="width: 250px;"
            class="filter-item"
            @keyup.enter.native="handleSearch"
          />
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleSearch"
          >
            {{ $t('oddstrader.search') }}
          </el-button>
        </div>

        <!-- Configuration Table -->
        <el-table
          v-loading="loading"
          :data="filteredConfigurations"
          border
          class="border-gray"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            label="ID"
            align="center"
          />
          <el-table-column
            prop="units"
            :label="$t('oddstrader.unit')"
            align="center"
          />
          <el-table-column
            prop="interval"
            :label="$t('oddstrader.interval')"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.interval }} {{ $t('oddstrader.secs') }}
            </template>
          </el-table-column>
          <el-table-column
            prop="currency"
            :label="$t('oddstrader.currency')"
            align="center"
          />
          <el-table-column
            :label="$t('oddstrader.tool')"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="editConfiguration(scope.row)"
              >
                {{ $t('oddstrader.edit') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- Edit/Create Dialog -->
    <el-dialog
      :title="isEditing ? $t('oddstrader.edit_configuration') : $t('oddstrader.create_configuration')"
      :visible.sync="editDialogVisible"
      width="600px"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="validationRules"
        label-width="120px"
      >
        <el-form-item
          :label="$t('oddstrader.currency')"
          prop="currency"
        >
          <el-select
            v-model="editForm.currency"
            :placeholder="$t('oddstrader.select_currency')"
            style="width: 100%;"
          >
            <el-option
              v-for="currency in currencyOptions"
              :key="currency"
              :label="currency.label"
              :value="currency.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('oddstrader.interval')"
          prop="interval"
        >
          <el-select
            v-model="editForm.interval"
            :placeholder="$t('oddstrader.select_interval')"
            style="width: 100%;"
          >
            <el-option
              v-for="interval in intervalOptions"
              :key="interval"
              :label="`${interval.label}`"
              :value="interval.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('oddstrader.units')"
          prop="units"
        >
          <el-input-number
            v-model="editForm.units"
            :min="1"
            :max="999999"
            controls-position="right"
            style="width: 100%;"
            :placeholder="$t('oddstrader.enter_number_of_units')"
          />
        </el-form-item>
      </el-form>

      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="editDialogVisible = false">
          {{ $t('oddstrader.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="saveConfiguration"
          :loading="saving"
        >
          {{ isEditing ? $t('oddstrader.update') : $t('oddstrader.create') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOddstraderConfigs, updateOddstraderConfig, createOddstraderConfig } from '@/api/oddstrader'

export default {
  name: 'OddstraderClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      searchQuery: '',

      // Configuration list data
      configurations: [],
      filteredConfigurations: [],

      // Dropdown options from API
      currencyOptions: [],
      intervalOptions: [],

      // Pagination
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // Edit dialog
      editDialogVisible: false,
      isEditing: false,
      editForm: {
        id: null,
        currency: '',
        interval: null,
        units: 1000
      }

    }
  },
  computed: {
    searchFilteredConfigs () {
      if (!this.searchQuery) {
        return this.configurations
      }
      return this.configurations.filter(config =>
        config.currency.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        config.units.toString().includes(this.searchQuery) ||
        config.interval.toString().includes(this.searchQuery)
      )
    },
    validationRules () {
      return {
        currency: [
          { required: true, message: this.$t('oddstrader.currency_required'), trigger: 'change' }
        ],
        interval: [
          { required: true, message: this.$t('oddstrader.interval_required'), trigger: 'change' }
        ],
        units: [
          { required: true, message: this.$t('oddstrader.units_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 999999, message: this.$t('oddstrader.units_range'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfigurations()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/oddstrader-config')
    },

    async fetchConfigurations () {
      this.loading = true
      try {
        const params = {
          client_id: this.clientId,
          page: this.currentPage,
          per_page: this.pageSize
        }

        const response = await getOddstraderConfigs(params)

        // Store configurations
        this.configurations = response.data.list || []
        this.total = response.data.total || this.configurations.length

        // Store dropdown options from API response
        if (response.data.currencies && Array.isArray(response.data.currencies)) {
          this.currencyOptions = response.data.currencies
        }

        if (response.data.intervals && Array.isArray(response.data.intervals)) {
          this.intervalOptions = response.data.intervals
        }

        // Set default values for form if options are available
        if (this.currencyOptions.length > 0 && !this.editForm.currency) {
          this.editForm.currency = this.currencyOptions[0]
        }

        if (this.intervalOptions.length > 0 && !this.editForm.interval) {
          this.editForm.interval = this.intervalOptions[0]
        }

        this.updateFilteredList()
      } catch (error) {
        console.error('Error fetching configurations:', error)
        this.$message.error(this.$t('oddstrader.fetch_configurations_failed'))
        this.configurations = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    handleSearch () {
      this.updateFilteredList()
    },

    updateFilteredList () {
      this.filteredConfigurations = this.searchFilteredConfigs
    },

    showCreateDialog () {
      this.isEditing = false
      this.editForm = {
        id: null,
        currency: this.currencyOptions.length > 0 ? this.currencyOptions[0] : '',
        interval: this.intervalOptions.length > 0 ? this.intervalOptions[0] : null,
        units: 1000
      }
      this.editDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate()
        }
      })
    },

    editConfiguration (config) {
      this.isEditing = true
      this.editForm = {
        id: config.id,
        currency: config.currency,
        interval: config.interval,
        units: config.units
      }
      this.editDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate()
        }
      })
    },

    async saveConfiguration () {
      try {
        await this.$refs.editForm.validate()

        this.saving = true

        const data = {
          currency: this.editForm.currency,
          interval: this.editForm.interval,
          units: this.editForm.units,
          client_id: this.clientId
        }

        if (this.isEditing) {
          await updateOddstraderConfig(this.editForm.id, data)
          this.$message.success(this.$t('oddstrader.config_updated_successfully'))
        } else {
          await createOddstraderConfig(data)
          this.$message.success(this.$t('oddstrader.config_created_successfully'))
        }

        this.editDialogVisible = false
        this.fetchConfigurations()
      } catch (error) {
        console.error('Error saving configuration:', error)
        this.$message.error(this.$t('oddstrader.failed_to_save_config'))
      } finally {
        this.saving = false
      }
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchConfigurations()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchConfigurations()
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
