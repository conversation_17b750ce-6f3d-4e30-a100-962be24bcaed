<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.fruitSlotsClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('common.go_back') }}
        </el-button>
      </div>

      <div class="filter-container">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t("common.refresh") }}
        </el-button>
      </div>

      <el-tabs
        v-model="tabs"
        type="border-card"
      >
        <el-tab-pane
          name="tab1"
          :label="$t('common.fruit')"
        >
          <Fruit v-if="tabs === 'tab1'" />
        </el-tab-pane>
        <el-tab-pane
          name="tab2"
          :label="$t('common.gameIndex')"
        >
          <GameSetting v-if="tabs === 'tab2'" />
        </el-tab-pane>
        <el-tab-pane
          name="tab3"
          :label="$t('common.lucky_draw')"
        >
          <LuckyDraw v-if="tabs === 'tab3'" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import Fruit from './fruit.vue'
import GameSetting from './gameIndex.vue'
import LuckyDraw from './luckyDraw.vue'

// Reactive data
const tabs = ref('tab1')
const { proxy } = getCurrentInstance()
const clientName = proxy.$route.query.clientName

const goBack = () => {
  proxy.$router.back()
}

const handleFilter = () => {
  tabs.value = 'tab1'
}

</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin-right: 10px;
}
</style>
