<template>
  <el-card>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="id"
        label="ID"
        width="120"
      />
      <el-table-column
        align="center"
        prop="name"
        :label="$t('common.color_mark')"
      />

      <el-table-column
        align="center"
        prop="updated_at"
        :label="$t('common.possibility')"
      >
        <template #default="scope">
          <span
            class="text-edit"
            @click="rateEdit(scope.row)"
          >{{
            scope.row.probability
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="
        display: flex;
        margin-top: 20px;
        width: 100%;
        justify-content: center;
      "
    >
      <el-pagination
        background
        layout="prev, pager, next, sizes"
        v-show="total > 0"
        :total="total"
        :page="listQuery.page"
        :limit="listQuery.limit"
        @pagination="getTagsFunc"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="showRate"
      :title="$t('common.edit')"
    >
      <el-form
        ref="formRef"
        :model="selctItem"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          :label="$t('common.possibility')"
          prop="probability"
        >
          <el-input
            v-model.trim="selctItem.probability"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submit"
          >
            {{ $t("common.submit") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-card>

  <!-- <el-dialog :title="$t('编辑')" v-model="showEdit">
      <Edit :tag_id="tag_id" />
    </el-dialog> -->
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { Message } from 'element-ui'
const ElMessage = Message

import { getLuckyDraws, updateLuckyDraw } from '@/api/fruit-slots'

// Get current instance to access $t
const { proxy } = getCurrentInstance()

// Reactive data
const showRate = ref(false)
const listQuery = reactive({
  tag_name: '',
  page: 1,
  limit: 10
})

// Computed rules to access $t method
const rules = computed(() => ({
  probability: [
    { required: true, message: '请填写概率', trigger: 'blur' }
  ]
}))

const listLoading = ref(false)
const total = ref(0)
const list = ref(null)
const submitLoading = ref(false)
const selctItem = ref({})
const tag_id = ref('')
const showEdit = ref(false)
const formRef = ref(null)

// Methods
const rateEdit = (item) => {
  selctItem.value = Object.assign({}, item)
  showRate.value = true
}

const submit = () => {
  submitLoading.value = true

  formRef.value.validate((valid) => {
    if (!valid) {
      submitLoading.value = false
      return false
    }

    updateLuckyDraw(selctItem.value.id, {
      probability: selctItem.value.probability
    })
      .then((response) => {
        submitLoading.value = false
        ElMessage({
          message: proxy.$t('common.edit_successfully'),
          type: 'success'
        })
        showRate.value = false
        getTagsFunc()
      })
      .finally(() => {
        submitLoading.value = false
      })
  })
}

const handleSizeChange = (val) => {
  listQuery.limit = val
  listQuery.page = 1
  getTagsFunc()
}

const handleCurrentChange = (num) => {
  listQuery.page = num
  getTagsFunc()
}

const getTagsFunc = () => {
  listLoading.value = true
  getLuckyDraws({
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  })
    .then((response) => {
      list.value = response.data.list
      total.value = response.data.total
      listLoading.value = false
    })
    .finally(() => {
      listLoading.value = false
    })
}

const handleFilter = () => {
  listQuery.page = 1
  getTagsFunc()
}

const handleRefresh = () => {
  listQuery.page = 1
  listQuery.tag_name = ''
  getTagsFunc()
}

const handleClick = (item) => {
  tag_id.value = item.id
  showEdit.value = true
}

// Lifecycle
onMounted(() => {
  getTagsFunc()
})

// Expose methods for template
defineExpose({
  handleCurrentChange,
  handleFilter,
  handleRefresh,
  handleClick
})
</script>

<style scoped>
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
.text-edit {
  cursor: pointer;
  color: #409eff;
}
.text-edit:hover {
  text-decoration: underline;
}
</style>
