<template>
  <el-card>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="id"
        label="ID"
        width="120"
      />
      <el-table-column
        align="center"
        prop="tag_name"
        :label="$t('common.index_position')"
      >
        <template #default="scope">
          <el-select
            :value="scope.row.index_position"
            style="margin-right: 10px"
            class="filter-item"
            @change="submit($event, scope.row, 1)"
          >
            <el-option
              v-for="item in ranges"
              :key="item.value"
              class="filter-item"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="created_at"
        :label="$t('common.fruit')"
      >
        <template #default="scope">
          <el-select
            :value="scope.row.fruit_id"
            style="margin-right: 10px"
            class="filter-item"
            @change="submit($event, scope.row, 2)"
          >
            <el-option
              v-for="item in fruits"
              :key="item.value"
              class="filter-item"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="
        display: flex;
        margin-top: 20px;
        width: 100%;
        justify-content: center;
      "
    >
      <el-pagination
        background
        layout="prev, pager, next, sizes"
        v-show="total > 0"
        :total="total"
        :page="listQuery.page"
        :limit="listQuery.limit"
        @pagination="getTagsFunc"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { Message } from 'element-ui'
const ElMessage = Message
import {
  getGameSettings,
  updateGameSettings
} from '@/api/fruit-slots'

// Get current instance to access $t
const { proxy } = getCurrentInstance()

// Reactive data
const visible = ref(false)
const fruits = ref([])
const ranges = ref([])
const showEdit = ref(false)
const listQuery = reactive({
  page: 1,
  limit: 10,
  tag_name: ''
})
const listLoading = ref(false)
const total = ref(0)
const list = ref(null)
const tag_id = ref('')
const submitLoading = ref(false)
const showRate = ref(false)

// Methods
const submit = (id, item, type) => {
  submitLoading.value = true
  const obj = {}
  if (type === 1) {
    obj.index_position = id
  } else {
    obj.fruit_id = id
  }

  updateGameSettings(item.id, obj)
    .then((response) => {
      submitLoading.value = false
      ElMessage({
        message: proxy.$t('common.edit_successfully'),
        type: 'success'
      })
      showRate.value = false
      getTagsFunc()
    })
    .finally(() => {
      submitLoading.value = false
    })
}

const handleSizeChange = (val) => {
  listQuery.limit = val
  listQuery.page = 1
  getTagsFunc()
}

const handleCurrentChange = (num) => {
  listQuery.page = num
  getTagsFunc()
}

const getTagsFunc = () => {
  listLoading.value = true
  getGameSettings({
    tag_name: listQuery.tag_name,
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  })
    .then((response) => {
      list.value = response.data.list
      total.value = response.data.total
      fruits.value = response.data.fruits
      ranges.value = response.data.range
      listLoading.value = false
    })
    .finally(() => {
      listLoading.value = false
    })
}

const handleFilter = () => {
  listQuery.page = 1
  getTagsFunc()
}

const handleRefresh = () => {
  listQuery.page = 1
  listQuery.tag_name = ''
  getTagsFunc()
}

const handleClick = (item) => {
  tag_id.value = item.id
  showEdit.value = true
}

// Lifecycle
onMounted(() => {
  getTagsFunc()
})

// Expose methods for template
defineExpose({
  handleCurrentChange,
  handleFilter,
  handleRefresh,
  handleClick
})
</script>

<style scoped>
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
