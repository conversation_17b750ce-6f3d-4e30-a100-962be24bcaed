<template>
  <el-card>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="id"
        label="ID"
        width="120"
      />
      <el-table-column
        align="center"
        prop="name"
        :label="$t('common.color_mark')"
      >
        <template #default="scope">
          <span
            class="text-edit"
            @click="showDialog(scope.row, 1)"
          >{{
            scope.row.name
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="multiplier"
        :label="$t('common.multiplier')"
      >
        <template #default="scope">
          <span
            class="text-edit"
            @click="showDialog(scope.row, 2)"
          >{{
            scope.row.multiplier
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="probability"
        :label="$t('common.possibility')"
      >
        <template #default="scope">
          <span
            class="text-edit"
            @click="showDialog(scope.row, 3)"
          >{{
            scope.row.probability
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="
        display: flex;
        margin-top: 20px;
        width: 100%;
        justify-content: center;
      "
    >
      <el-pagination
        background
        layout="prev, pager, next, sizes"
        v-show="total > 0"
        :total="total"
        :page="listQuery.page"
        :limit="listQuery.limit"
        @pagination="getTagsFunc"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="showModel"
      :title="[$t('common.edit'), $t('common.edit'), $t('common.edit')][tag - 1]"
    >
      <el-form
        ref="formRef"
        :model="selctItem"
        label-width="100px"
      >
        <el-form-item
          :label="[$t('common.color_mark'), $t('common.multiplier'), $t('common.possibility')][tag - 1]"
        >
          <el-input
            v-if="tag === 1"
            v-model="selctItem.name"
          />
          <el-input
            v-if="tag === 2"
            v-model="selctItem.multiplier"
          />
          <el-input
            v-if="tag === 3"
            v-model="selctItem.probability"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submit"
          >
            {{ $t("common.submit") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-card>
  <!-- <el-dialog :title="$t('编辑')" v-model="showEdit">
      <Edit :tag_id="tag_id" />
    </el-dialog> -->
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { Message } from 'element-ui'
const ElMessage = Message
import {
  getFruits,
  updateFruit
} from '@/api/fruit-slots'

// Get current instance to access $t
const { proxy } = getCurrentInstance()

// Reactive data
const showEdit = ref(false)
const listQuery = reactive({
  page: 1,
  limit: 10
})
const listLoading = ref(false)
const total = ref(0)
const list = ref(null)
const tag_id = ref('')
const showModel = ref(false)
const submitLoading = ref(false)
const selctItem = ref({})
const tag = ref(0)
const formRef = ref(null)

// Methods
const submit = () => {
  submitLoading.value = true

  formRef.value.validate((valid) => {
    if (!valid) {
      submitLoading.value = false
      return false
    }

    const obj = {}
    if (tag.value === 1) {
      obj.name = selctItem.value.name
    } else if (tag.value === 2) {
      obj.multiplier = selctItem.value.multiplier
    } else if (tag.value === 3) {
      obj.probability = selctItem.value.probability
    }

    updateFruit(selctItem.value.id, obj)
      .then((response) => {
        submitLoading.value = false
        ElMessage({
          message: proxy.$t('common.edit_successfully'),
          type: 'success'
        })
        showModel.value = false
        getTagsFunc()
      })
      .finally(() => {
        submitLoading.value = false
      })
  })
}

const showDialog = (item, row) => {
  tag.value = row
  selctItem.value = Object.assign({}, item)
  showModel.value = true
}

const handleSizeChange = (val) => {
  listQuery.limit = val
  listQuery.page = 1
  getTagsFunc()
}

const handleCurrentChange = (num) => {
  listQuery.page = num
  getTagsFunc()
}

const getTagsFunc = () => {
  listLoading.value = true
  getFruits({
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  })
    .then((response) => {
      list.value = response.data.list
      total.value = response.data.total
      listLoading.value = false
    })
    .finally(() => {
      listLoading.value = false
    })
}

const handleFilter = () => {
  listQuery.page = 1
  getTagsFunc()
}

const handleRefresh = () => {
  listQuery.page = 1
  listQuery.tag_name = ''
  getTagsFunc()
}

const handleClick = (item) => {
  tag_id.value = item.id
  showEdit.value = true
}

// Lifecycle
onMounted(() => {
  getTagsFunc()
})

// Expose methods for template
defineExpose({
  handleCurrentChange,
  handleFilter,
  handleRefresh,
  handleClick
})
</script>

<style scoped>
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
.text-edit {
  cursor: pointer;
  color: #409eff;
}
.text-edit:hover {
  text-decoration: underline;
}
</style>
