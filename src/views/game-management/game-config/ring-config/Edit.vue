<template>
  <div>
    <el-button
      style="margin-bottom: 15px"
      type="primary"
      @click="submit"
      :loading="submitLoading"
    >
      {{ $t('common.submit') }}
    </el-button>

    <el-collapse
      v-model="activeNames"
      @change="handleChange"
    >
      <el-collapse-item
        :title="item.label"
        :name="index"
        v-for="(item, index) in multipliers"
        :key="index"
      >
        <el-collapse
          v-model="activeNames1"
          @change="handleChange"
          style="margin-left: 30px"
        >
          <el-collapse-item
            :title="key1.label"
            :name="index1"
            v-for="(key1, index1) in item.chernd"
            :key="index1"
          >
            <div style="margin-left: 30px">
              <div class="top">
                <div class="top_content">
                  {{ $t('common.possibility') }}
                </div>
                <div class="top_content">
                  {{ $t('common.multiplier') }}
                </div>
                <div class="top_option">
                  {{ $t('common.operation') }}
                </div>
              </div>

              <div
                class="top"
                v-for="(key2, index2) in key1.chernd"
                :key="index2"
              >
                <div class="top_content">
                  <el-input
                    v-model="key2.label"
                    style="width: 80%"
                    placeholder="Please fill in the possibility"
                  />
                </div>
                <div class="top_content">
                  <el-input
                    v-model="key2.value"
                    style="width: 80%"
                    placeholder="Please enter the multiplier"
                  />
                </div>
                <div
                  class="top_option text-edit"
                  @click="deleteItem(index2, key1.chernd)"
                >
                  {{ $t("admin.table_delete") }}
                </div>
              </div>

              <el-button
                style="margin-top: 15px"
                type="primary"
                @click="addItem(key1.chernd)"
              >
                {{ $t('common.add') }}
              </el-button>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n-bridge'
import { MessageBox, Message } from 'element-ui'
const ElMessageBox = MessageBox
const ElMessage = Message
import { updateRingConfig } from '@/api/ring'

// Props
const props = defineProps({
  item_id: {
    type: String,
    default: ''
  },
  multiplierData: null,
  onClose: {
    type: Function,
    default: null
  }
})

// Composables
const { t: $t } = useI18n()

// Reactive data
const submitLoading = ref(false)
const activeNames = ref([])
const activeNames1 = ref([])
const multipliers = ref([])

// Lifecycle
onMounted(() => {
  loadData()
})

// Methods
const handleChange = () => {
  // Handle collapse change if needed
}

const deleteItem = (index, array) => {
  array.splice(index, 1)
}

const addItem = (array) => {
  array.push({
    label: '',
    value: ''
  })
}

const submit = () => {
  submitLoading.value = true
  const jsonData = {}

  try {
    multipliers.value.forEach((item) => {
      const key = item.label
      jsonData[key] = {}
      item.chernd.forEach((subItem) => {
        const subKey = subItem.label
        jsonData[key][subKey] = {}
        subItem.chernd.forEach((subSubItem) => {
          const subSubKey = subSubItem.label
          jsonData[key][subKey][subSubKey] = subSubItem.value
        })
      })
    })
  } catch (error) {
    console.error('Error processing multipliers data:', error)
  }

  updateRingConfig(props.item_id, {
    multiplier: JSON.stringify(jsonData)
  })
    .then((response) => {
      submitLoading.value = false
      ElMessage({
        message: $t('common.edit_successfully'),
        type: 'success',
        duration: 1 * 1000,
        onClose () {
          if (props.onClose) {
            props.onClose()
          }
        }
      })
    })
    .catch((error) => {
      console.error('Error updating configuration:', error)
      submitLoading.value = false
      ElMessage({
        message: $t('common.edit_failed'),
        type: 'error',
        duration: 2 * 1000
      })
    })
    .finally(() => {
      submitLoading.value = false
    })
}

const loadData = () => {
  const result = Object.entries(
    props.multiplierData
  ).map(([key, value]) => {
    return {
      label: key,
      chernd: Object.entries(value).map(([subKey, subValue]) => {
        return {
          label: subKey,
          chernd: Object.entries(subValue).map(
            ([subSubKey, subSubValue]) => {
              return {
                label: subSubKey,
                value: subSubValue
              }
            }
          )
        }
      })
    }
  })
  multipliers.value = result
}

// Expose methods that might need to be accessed from parent
defineExpose({
  submit
})
</script>

<style scoped>
.top {
  display: flex;
  align-items: center;
  border: 0.3px solid #e6ebf5;
}

.top_content {
  height: 40px;
  width: 40%;
  line-height: 40px;
  text-align: center;
  border-left: 0.3px solid #e6ebf5;
}

.top_option {
  height: 40px;
  width: 20%;
  line-height: 40px;
  text-align: center;
  border-left: 0.3px solid #e6ebf5;
  cursor: pointer;
}

.top_option:hover {
  background-color: #f5f7fa;
}

.text-edit {
  color: #f56c6c;
  font-weight: 500;
}
</style>
