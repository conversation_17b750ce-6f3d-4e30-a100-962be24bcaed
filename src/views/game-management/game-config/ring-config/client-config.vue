<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.ringClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('common.go_back') }}
        </el-button>
      </div>

      <Edit
        v-if="!!list"
        :item_id="clientId"
        :multiplier-data="list"
        :on-close="onClose"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n-bridge'
import Edit from './Edit.vue'
import { getRingConfigs } from '@/api/ring'
import { getCurrentInstance } from 'vue'

// Composables
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const clientId = proxy.$route.params.clientId
const clientName = proxy.$route.query.clientName
// Reactive data
const showEdit = ref(false)
const listLoading = ref(false)
const total = ref(0)
const list = ref(null)

// Lifecycle
onMounted(() => {
  getListFunc()
})

const goBack = () => {
  proxy.$router.back()
}

const getListFunc = () => {
  listLoading.value = true
  getRingConfigs({
    client_id: clientId
  })
    .then((response) => {
      list.value = response.data.multiplier
      total.value = 10
      listLoading.value = false
    })
    .finally(() => {
      listLoading.value = false
    })
}

const handleRefresh = () => {
  getListFunc()
}

const onClose = () => {
  showEdit.value = false
  getListFunc()
}

// Expose methods and data that might need to be accessed from parent components
defineExpose({
  getListFunc,
  handleRefresh
})
</script>

<style scoped>
.pagination-container {
  padding: 5px 2px;
}

.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
