<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.ringClientConfig') }}</span>
      </div>

      <div class="client-list-container">
        <!-- Search and Filter -->
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('common.name')"
            style="width: 300px; margin-right: 10px;"
            clearable
            @keyup.enter.native="handleSearch"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="fetchClientList"
          >
            {{ $t('common.search') }}
          </el-button>
        </div>

        <!-- Client List Table -->
        <el-table
          v-loading="loading"
          :data="clientList"
          border
          class="border-gray"
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            label="Client ID"
            width="100"
            align="center"
          />
          <el-table-column
            prop="name"
            :label="$t('common.name')"
            min-width="200"
          />
          <el-table-column
            :label="$t('common.action')"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-setting"
                @click="viewClientConfigs(scope.row)"
              >
                {{ $t('common.configure') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { Message } from 'element-ui'
const ElMessage = Message
import { getClientList } from '@/api/ring'

const { proxy } = getCurrentInstance()
const loading = ref(false)
const clientList = ref([])
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const fetchClientList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }

    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    const response = await getClientList(params)

    if (response.data) {
      clientList.value = response.data.data || response.data
      total.value = response.data.total || clientList.value.length
    }
  } catch (error) {
    console.error('Error fetching client list:', error)
    ElMessage.error('Failed to fetch client list')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchClientList()
}

const viewClientConfigs = (client) => {
  proxy.$router.push({
    name: 'RingClientConfig',
    params: { clientId: client.id },
    query: { clientName: client.name }
  })
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchClientList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchClientList()
}

onMounted(() => {
  fetchClientList()
})
</script>

<style scoped>
.client-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
