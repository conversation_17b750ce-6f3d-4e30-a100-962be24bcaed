<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.europeanRouletteClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('europeanRoulette.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <div v-loading="loading">
          <el-form
            ref="configForm"
            :model="formData"
            :rules="formRules"
            label-width="150px"
            class="config-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  :label="$t('europeanRoulette.min_amount')"
                  prop="minAmount"
                >
                  <el-input-number
                    v-model="formData.minAmount"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('europeanRoulette.enter_min_amount')"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  :label="$t('europeanRoulette.max_amount')"
                  prop="maxAmount"
                >
                  <el-input-number
                    v-model="formData.maxAmount"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%;"
                    :placeholder="$t('europeanRoulette.enter_max_amount')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Form Actions -->
            <div class="form-actions">
              <el-button
                type="primary"
                @click="saveConfiguration"
                :loading="saving"
                icon="el-icon-check"
              >
                {{ $t('europeanRoulette.save_configuration') }}
              </el-button>
              <el-button
                @click="resetForm"
                icon="el-icon-refresh"
              >
                {{ $t('common.reset') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getEuropeanRouletteConfigs, createEuropeanRouletteConfig, updateEuropeanRouletteConfig } from '@/api/european-roulette'

export default {
  name: 'EuropeanRouletteClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',
      configId: null, // Store configuration ID for updates

      // Form data
      formData: {
        minAmount: 1,
        maxAmount: 9999999
      }
    }
  },
  computed: {
    formRules () {
      return {
        minAmount: [
          { required: true, message: this.$t('europeanRoulette.min_amount_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('europeanRoulette.min_amount_range'), trigger: 'blur' }
        ],
        maxAmount: [
          { required: true, message: this.$t('europeanRoulette.max_amount_required'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.$t('europeanRoulette.max_amount_range'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= this.formData.minAmount) {
                callback(new Error(this.$t('europeanRoulette.max_amount_greater_than_min')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfiguration()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/european-roulette-config')
    },

    async fetchConfiguration () {
      this.loading = true

      try {
        const params = {
          client_id: this.clientId
        }
        const response = await getEuropeanRouletteConfigs(params)

        // Handle different possible response structures
        let configData = null
        if (response.data) {
          if (Array.isArray(response.data)) {
            configData = response.data.length > 0 ? response.data[0] : null
          } else if (response.data.data && Array.isArray(response.data.data)) {
            configData = response.data.data.length > 0 ? response.data.data[0] : null
          } else {
            configData = response.data
          }
        }

        if (configData) {
          this.populateForm(configData)
        } else {
          this.setDefaultValues()
        }
      } catch (error) {
        this.$message.warning(this.$t('europeanRoulette.no_config_found_using_defaults'))
        this.setDefaultValues()
      } finally {
        this.loading = false
      }
    },

    populateForm (data) {
      // Store configuration ID for updates
      this.configId = data.id || null

      // Extract min and max amounts
      this.formData.minAmount = data.min_amount || data.minAmount || 1
      this.formData.maxAmount = data.max_amount || data.maxAmount || 9999999
    },

    setDefaultValues () {
      this.formData = {
        minAmount: 1,
        maxAmount: 9999999
      }
      this.configId = null
    },

    async saveConfiguration () {
      try {
        await this.$refs.configForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          min_amount: this.formData.minAmount,
          max_amount: this.formData.maxAmount,
          client_id: this.clientId
        }

        // Use update if we have an existing configuration, otherwise create
        if (this.configId) {
          await updateEuropeanRouletteConfig(this.configId, data)
          this.$message.success(this.$t('europeanRoulette.config_updated_successfully'))
        } else {
          this.$message.error(this.$t('europeanRoulette.failed_to_save_config'))
        }
      } catch (error) {
        console.error('Error saving configuration:', error)
        const errorMessage = error.response?.data?.message || this.$t('europeanRoulette.failed_to_save_config')
        this.$message.error(errorMessage)
      } finally {
        this.saving = false
      }
    },

    resetForm () {
      this.setDefaultValues()
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate()
        }
      })
      this.$message.info(this.$t('europeanRoulette.form_reset_to_defaults'))
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.config-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
