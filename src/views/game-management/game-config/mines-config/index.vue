<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.minesConfig') }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
        >
          <i class="el-icon-back" /> {{ $t('mines.back_to_game_config') }}
        </el-button>
      </div>

      <div class="client-list-container">
        <!-- Search and Filter -->
        <div class="filter-container">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('mines.search_clients')"
            style="width: 300px; margin-right: 10px;"
            clearable
            @input="handleSearch"
          >
            <i
              slot="prefix"
              class="el-input__icon el-icon-search"
            />
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="fetchClientList"
            :loading="loading"
          >
            {{ $t('common.refresh') }}
          </el-button>
        </div>

        <!-- Client List Table -->
        <el-table
          v-loading="loading"
          :data="clientList"
          border
          class="border-gray"
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            :label="$t('mines.client_id')"
            width="100"
            align="center"
          />
          <el-table-column
            prop="name"
            :label="$t('mines.client_name')"
            min-width="200"
          />
          <el-table-column
            :label="$t('common.action')"
            width="200"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-setting"
                @click="viewClientConfigs(scope.row)"
              >
                {{ $t('common.configure') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getClientList } from '@/api/mines'

export default {
  name: 'MinesConfig',
  data () {
    return {
      loading: false,
      clientList: [],
      searchQuery: '',
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  created () {
    this.fetchClientList()
  },
  methods: {
    async fetchClientList () {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          per_page: this.pageSize
        }

        if (this.searchQuery) {
          params.search = this.searchQuery
        }

        const response = await getClientList(params)

        if (response && response.data) {
          this.clientList = response.data.data || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        console.error('Error fetching client list:', error)
        this.$message.error(this.$t('mines.fetch_client_list_failed'))
      } finally {
        this.loading = false
      }
    },

    goBack () {
      this.$router.push('/game-management/game-config')
    },

    handleSearch () {
      this.currentPage = 1
      this.fetchClientList()
    },

    viewClientConfigs (client) {
      // Navigate to client-specific configuration page
      this.$router.push({
        name: 'MinesClientConfig',
        params: { clientId: client.id },
        query: { clientName: client.name }
      })
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchClientList()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchClientList()
    }
  }
}
</script>

<style scoped>
.client-list-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}
</style>
