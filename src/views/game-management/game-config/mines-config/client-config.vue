<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.minesClientConfig') }} - {{ clientName }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="goBack"
          icon="el-icon-back"
        >
          <i class="el-icon-back" /> {{ $t('mines.back_to_client_list') }}
        </el-button>
      </div>

      <div class="config-container">
        <!-- Filter Section -->
        <div class="filter-container">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model="filters.serialNumber"
                :placeholder="$t('mines.enter_serial_number')"
                clearable
                @input="handleFilterChange"
              >
                <template slot="prepend">
                  {{ $t('mines.serial_number') }}
                </template>
              </el-input>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model="filters.numberOfMines"
                :placeholder="$t('mines.enter_number_of_mines')"
                clearable
                @input="handleFilterChange"
              >
                <template slot="prepend">
                  {{ $t('mines.number_of_mines') }}
                </template>
              </el-input>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model="filters.numberOfHits"
                :placeholder="$t('mines.enter_number_of_hits')"
                clearable
                @input="handleFilterChange"
              >
                <template slot="prepend">
                  {{ $t('mines.number_of_hits') }}
                </template>
              </el-input>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model="filters.magnification"
                :placeholder="$t('mines.enter_magnification')"
                clearable
                @input="handleFilterChange"
              >
                <template slot="prepend">
                  {{ $t('mines.magnification') }}
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="applyFilters"
              >
                {{ $t('common.search') }}
              </el-button>
              <el-button
                icon="el-icon-refresh"
                @click="resetFilters"
                style="margin-left: 10px;"
              >
                {{ $t('common.reset') }}
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- Configuration Table -->
        <el-table
          v-loading="loading"
          :data="configurationList"
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          class="border-gray"
          fit
          highlight-current-row
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column
            prop="id"
            :label="$t('mines.configuration_id')"
            width="150"
            align="center"
          />

          <el-table-column
            prop="multiplier"
            :label="$t('mines.multiplier')"
            align="center"
            min-width="200"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                class="multiplier-link"
                @click="editMultiplier(scope.row)"
              >
                {{ scope.row.multiplier }}
              </el-button>
            </template>
          </el-table-column>

          <el-table-column
            prop="hit_count"
            :label="$t('mines.hit_count')"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.hit_count !== null && scope.row.hit_count !== undefined ? scope.row.hit_count : 0 }}
            </template>
          </el-table-column>

          <el-table-column
            prop="mine_count"
            :label="$t('mines.mine_count')"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.mine_count !== null && scope.row.mine_count !== undefined ? scope.row.mine_count : 0 }}
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('common.action')"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tooltip
                :content="$t('mines.edit_multiplier')"
                placement="top"
              >
                <el-button
                  type="primary"
                  icon="el-icon-edit"
                  size="mini"
                  circle
                  @click="editMultiplier(scope.row)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!-- Edit Multiplier Dialog -->
        <el-dialog
          :title="$t('mines.edit_multiplier')"
          :visible.sync="editDialogVisible"
          width="500px"
          :before-close="cancelEdit"
        >
          <el-form
            ref="editForm"
            :model="editForm"
            :rules="editFormRules"
            label-width="120px"
          >
            <el-form-item
              :label="$t('mines.multiplier')"
              prop="multiplier"
            >
              <el-input-number
                v-model="editForm.multiplier"
                :min="1"
                :max="1000"
                :precision="2"
                :step="0.1"
                controls-position="right"
                style="width: 100%;"
                :placeholder="$t('mines.enter_multiplier_value')"
              />
              <div class="field-description">
                {{ $t('mines.multiplier_description') }}
              </div>
            </el-form-item>
          </el-form>

          <span
            slot="footer"
            class="dialog-footer"
          >
            <el-button @click="cancelEdit">{{ $t('common.cancel') }}</el-button>
            <el-button
              type="primary"
              :loading="saving"
              @click="saveMultiplier"
            >
              {{ $t('common.save') }}
            </el-button>
          </span>
        </el-dialog>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getMinesConfigs, updateMinesConfig } from '@/api/mines'

export default {
  name: 'MinesClientConfig',
  data () {
    return {
      loading: false,
      saving: false,
      clientId: null,
      clientName: '',

      // Configuration list data
      configurationList: [],

      // Pagination data (server-side)
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // Filter data
      filters: {
        serialNumber: '',
        numberOfMines: '',
        numberOfHits: '',
        magnification: ''
      },

      // Edit dialog
      editDialogVisible: false,
      editForm: {
        id: null,
        multiplier: 2.0
      },

      // Form validation rules
      editFormRules: {
        multiplier: [
          { required: true, message: this.$t('mines.multiplier_required'), trigger: 'blur' },
          { type: 'number', min: 1, max: 1000, message: this.$t('mines.multiplier_range'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.clientId = this.$route.params.clientId
    this.clientName = this.$route.query.clientName || `Client ${this.clientId}`
    this.fetchConfigurations()
  },
  methods: {
    goBack () {
      this.$router.push('/game-management/mines-config')
    },

    async fetchConfigurations () {
      this.loading = true

      try {
        // Prepare API parameters with pagination and filters
        const params = {
          client_id: this.clientId,
          page: this.currentPage,
          per_page: this.pageSize
        }

        // Add filter parameters if they exist
        if (this.filters.serialNumber && this.filters.serialNumber.trim() !== '') {
          params.serial_number = this.filters.serialNumber.trim()
        }
        if (this.filters.numberOfMines && this.filters.numberOfMines.trim() !== '') {
          params.mine_count = this.filters.numberOfMines.trim()
        }
        if (this.filters.numberOfHits && this.filters.numberOfHits.trim() !== '') {
          params.hit_count = this.filters.numberOfHits.trim()
        }
        if (this.filters.magnification && this.filters.magnification.trim() !== '') {
          params.multiplier = this.filters.magnification.trim()
        }

        const response = await getMinesConfigs(params)

        if (response && response.data) {
          // Handle Laravel pagination format
          if (response.data.data && Array.isArray(response.data.data)) {
            // Laravel pagination response
            this.configurationList = response.data.data
            this.total = response.data.total || 0
            this.currentPage = response.data.current_page || 1
            this.pageSize = response.data.per_page || 20
          } else if (Array.isArray(response.data)) {
            // Direct array response (fallback)
            this.configurationList = response.data
            this.total = response.data.length
          } else {
            // Single object response (fallback)
            this.configurationList = [response.data]
            this.total = 1
          }
        } else {
          this.configurationList = []
          this.total = 0
        }
      } catch (error) {
        console.error('Error fetching configurations:', error)
        this.$message.warning(this.$t('mines.no_configurations_found'))
        this.configurationList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    editMultiplier (config) {
      this.editForm = {
        id: config.id,
        multiplier: config.multiplier || 2.0
      }
      this.editDialogVisible = true
    },

    async saveMultiplier () {
      try {
        await this.$refs.editForm.validate()

        this.saving = true

        // Prepare data according to API specification
        const data = {
          multiplier: this.editForm.multiplier,
          client_id: this.clientId
        }

        await updateMinesConfig(this.editForm.id, data)
        this.$message.success(this.$t('mines.multiplier_updated_successfully'))

        // Close dialog and refresh data
        this.editDialogVisible = false
        this.fetchConfigurations()
      } catch (error) {
        console.error('Error saving multiplier:', error)
        const errorMessage = error.response?.data?.message || this.$t('mines.failed_to_save_multiplier')
        this.$message.error(errorMessage)
      } finally {
        this.saving = false
      }
    },

    applyFilters () {
      // Reset to first page when filters change
      this.currentPage = 1

      // Fetch data with filters from server
      this.fetchConfigurations()
    },

    resetFilters () {
      this.filters = {
        serialNumber: '',
        numberOfMines: '',
        numberOfHits: '',
        magnification: ''
      }
      this.currentPage = 1
      this.fetchConfigurations()
      this.$message.info(this.$t('mines.filters_reset'))
    },

    handleFilterChange () {
      // Optional: Apply filters in real-time as user types
      // Uncomment the line below for real-time filtering
      // this.applyFilters()
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1 // Reset to first page when page size changes
      this.fetchConfigurations()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchConfigurations()
    },

    cancelEdit () {
      this.editDialogVisible = false
      this.editForm = {
        id: null,
        multiplier: 2.0
      }
      // Clear form validation
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate()
        }
      })
    },

    formatDate (dateString) {
      if (!dateString) return 'N/A'
      try {
        return new Date(dateString).toLocaleString()
      } catch (error) {
        return 'Invalid Date'
      }
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px 0;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.filter-container .el-input-group__prepend {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
  font-weight: 500;
  min-width: 120px;
  text-align: center;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.multiplier-link {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.multiplier-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.field-description {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
}

.el-form-item {
  margin-bottom: 25px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}
</style>
