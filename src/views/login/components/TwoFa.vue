<template>
  <div class="login-container">
    <div class="login-main">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <div class="title-container">
          <h3 class="title">
            {{ $t('login.two_fa') }}
          </h3>
          <lang-select class="set-language" />
        </div>
        <div v-if="qr">
          <div
            style="display: flex; justify-content: center; width: 100%; margin-bottom: 20px;"
            v-html="qr"
          />
        </div>
        <el-form-item prop="two_fa">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            ref="two_fa"
            typ="text"
            :controls="false"
            v-model="loginForm.two_fa"
            :placeholder="$t('login.two_fa')"
            name="two_fa"
            tabindex="1"
            autocomplete="off"
          />
        </el-form-item>

        <el-button
          :loading="loading"
          type="primary"
          style="width:100%;margin-bottom:35px;"
          @click.native.prevent="handleTwoFa"
        >
          {{ $t('login.logIn') }}
        </el-button>
      </el-form>

      <div class="copyright">
        Copyright @ 2024 <a
          target="_blank"
          href="http://github.com/deatil/larke-admin"
        >Larke-admin</a>
      </div>
    </div>
  </div>
</template>

<script>
import { get2Fa, verify2Fa } from '@/api/admin'
import LangSelect from '@/components/LangSelect'

export default {
  name: 'Login',
  components: { LangSelect },
  data () {
    return {
      loginForm: {
        two_fa: ''
      },
      loginRules: {
        two_fa: [{ required: true, trigger: 'blur', message: this.$t('login.two_fa_required') }]
      },
      qr: null,
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created () {
    // window.addEventListener('storage', this.afterQRScan)

  },
  mounted () {
    const registered_two_fa = this.$store.getters.registered_two_fa
    console.log(registered_two_fa)
    if (registered_two_fa == 'false' || !registered_two_fa) {
      this.requestQR()
    }
    if (this.loginForm.two_fa === '') {
      this.$refs.two_fa.focus()
    }
  },
  destroyed () {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    async requestQR () {
      const result = await get2Fa()
      if (result.success == true) {
        this.qr = result.data.qr
      }
    },
    checkCapslock (e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    refreshCaptcha () {
      this.$store.dispatch('user/captcha')
        .then(response => {
          const headers = response.headers
          const res = response.data

          const captchaKey = headers['larke-admin-captcha-id']
          const captchaImg = res.data.captcha

          this.loginForm.captchaKey = captchaKey
          this.captchaImg = captchaImg
        })
        .catch(_err => {
          return false
        })
    },
    handleTwoFa () {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const result = await verify2Fa({ code: this.loginForm.two_fa })

            if (result.data == true) {
              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
            }
          } catch (e) {

          }
          this.loading = false
          // this.$store.dispatch('user/login', this.loginForm)
          //   .then(response => {
          //     console.log(response)
          //     this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
          //     this.loading = false
          //   })
          //   .catch(_err => {
          //     this.refreshCaptcha()

          //     this.loading = false
          //   })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery (query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }

  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background: url(../../../assets/larke/loginbg.png) 0% 0% / cover no-repeat;
  position: static;
  overflow: hidden;

  .login-main {
    width: 428px;
    position: relative;
    margin: 0 auto;
    margin-top: 80px;
    margin-bottom: 50px;
    background: #526070;
    border-radius: 5px;

    .copyright {
        color: #fff;
        width: 100%;
        position: absolute;
        text-align: center;
        line-height: 30px;
        padding: 10px 0;
        text-shadow: #000 0.1em 0.1em 0.1em;
        font-size: 14px;
    }
    .copyright a, .copyright span {
        color: #fff;
    }
  }

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 35px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0px;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  .captcha-input {
    width: 65%;
  }
  .captcha-img {
    width: 100px;
    height: 39px;
    vertical-align: middle;
    display: inline-block;
    position: absolute;
    top: 5px;
    right: 10px;
  }
  .captcha-img img {
    width: 100px;
    cursor: pointer;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

</style>
