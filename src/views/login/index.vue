<template>
  <div class="login-container">
    <div class="login-main">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <div class="title-container">
          <h3 class="title">
            {{ $t('login.title') }}
          </h3>
          <lang-select class="set-language" />
        </div>

        <el-form-item prop="username">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            ref="username"
            v-model="loginForm.username"
            :placeholder="$t('login.username')"
            name="username"
            type="text"
            tabindex="1"
            autocomplete="off"
          />
        </el-form-item>

        <el-tooltip
          v-model="capsTooltip"
          content="Caps lock is On"
          placement="right"
          manual
        >
          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              :placeholder="$t('login.password')"
              name="password"
              tabindex="2"
              autocomplete="off"
              @keyup.native="checkCapslock"
              @blur="capsTooltip = false"
              @keyup.enter.native="handleLogin"
            />
            <span
              class="show-pwd"
              @click="showPwd"
            >
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-tooltip>

        <el-form-item prop="captcha">
          <span class="svg-container">
            <i class="el-icon-picture-outline" />
          </span>
          <el-input
            ref="captcha"
            v-model="loginForm.captcha"
            :placeholder="$t('login.captcha')"
            name="captcha"
            type="text"
            tabindex="3"
            autocomplete="off"
            class="captcha-input"
          />

          <el-tooltip
            effect="light"
            :content="$t('login.refreshCaptchaTip')"
            placement="top"
          >
            <span
              class="captcha-img"
              @click="refreshCaptcha"
            >
              <img
                :src="captchaImg"
                :title="$t('login.refreshCaptcha')"
              >
            </span>
          </el-tooltip>
        </el-form-item>

        <el-button
          :loading="loading"
          type="primary"
          style="width:100%;margin-bottom:35px;"
          @click.native.prevent="handleLogin"
        >
          {{ $t('login.logIn') }}
        </el-button>
      </el-form>

      <div class="copyright">
        Copyright @ 2024 <a
          target="_blank"
          href="http://github.com/deatil/larke-admin"
        >Larke-admin</a>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import LangSelect from '@/components/LangSelect'

export default {
  name: 'Login',
  components: { LangSelect },
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error(this.$t('login.rules_username_required')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error(this.$t('login.rules_password_required')))
      } else {
        callback()
      }
    }
    const validateCaptcha = (rule, value, callback) => {
      if (value.length != 4) {
        callback(new Error(this.$t('login.rules_captcha_required')))
      } else {
        callback()
      }
    }
    return {
      captchaImg: require('@/assets/larke/captcha.png'),
      pubkey: '',
      loginForm: {
        username: '',
        password: '',
        captcha: '',
        captchaKey: '',

        passkeyId: '',
        passkey: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        captcha: [{ required: true, trigger: 'blur', validator: validateCaptcha }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created () {
    // window.addEventListener('storage', this.afterQRScan)

    this.getPasskey()

    this.refreshCaptcha()
  },
  mounted () {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    } else if (this.loginForm.captcha === '') {
      this.$refs.captcha.focus()
    }
  },
  destroyed () {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock (e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd () {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    getPasskey () {
      this.$store.dispatch('user/passkey')
        .then(response => {
          const headers = response.headers
          const res = response.data

          this.loginForm.passkeyId = headers['larke-admin-passkey-id']
          this.loginForm.passkey = res.data.key
        })
        .catch(_err => {
          return false
        })
    },
    refreshCaptcha () {
      this.$store.dispatch('user/captcha')
        .then(response => {
          const headers = response.headers
          const res = response.data

          const captchaKey = headers['larke-admin-captcha-id']
          const captchaImg = res.data.captcha

          this.loginForm.captchaKey = captchaKey
          this.captchaImg = captchaImg
        })
        .catch(_err => {
          return false
        })
    },
    handleLogin () {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm)
            .then(response => {
              if (response.verified_two_fa == true) {
                this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              } else {
                this.$router.push({ path: '/two-fa', query: this.otherQuery })
              }
              this.loading = false
            })
            .catch(_err => {
              this.refreshCaptcha()

              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery (query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }

  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background: url(../../assets/larke/loginbg.png) 0% 0% / cover no-repeat;
  position: static;
  overflow: hidden;

  .login-main {
    width: 428px;
    position: relative;
    margin: 0 auto;
    margin-top: 80px;
    margin-bottom: 50px;
    background: #526070;
    border-radius: 5px;

    .copyright {
        color: #fff;
        width: 100%;
        position: absolute;
        text-align: center;
        line-height: 30px;
        padding: 10px 0;
        text-shadow: #000 0.1em 0.1em 0.1em;
        font-size: 14px;
    }
    .copyright a, .copyright span {
        color: #fff;
    }
  }

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 35px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0px;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  .captcha-input {
    width: 65%;
  }
  .captcha-img {
    width: 100px;
    height: 39px;
    vertical-align: middle;
    display: inline-block;
    position: absolute;
    top: 5px;
    right: 10px;
  }
  .captcha-img img {
    width: 100px;
    cursor: pointer;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

</style>
