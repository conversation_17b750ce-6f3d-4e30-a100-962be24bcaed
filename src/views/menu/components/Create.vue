<template>
  <el-form
    ref="menuCreateform"
    :model="data"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item
      :label="$t('menu.form_parent')"
      prop="pid"
    >
      <el-select
        v-model="data.pid"
        :placeholder="$t('menu.form_parent_select')"
        clearable
        filterable
        :filter-method="parentFilter"
        @change="pidChange"
      >
        <el-option
          v-for="option in parentOptions"
          :key="option.key"
          :label="option.display_name | entityToString"
          :value="option.key"
        />
      </el-select>

      <el-button
        :type="!rule.visible ? 'primary' : 'danger'"
        class="fast-select"
        @click="ruleFastSelect"
      >
        {{ !rule.visible ? $t('menu.form_fast_select') : $t('menu.form_close_select') }}
      </el-button>
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_fast_select')"
      v-if="rule.visible"
    >
      <el-select
        :placeholder="$t('menu.form_select_rules')"
        clearable
        filterable
        :filter-method="ruleFilter"
        v-model="rule.id"
        @change="ruleChange"
      >
        <el-option
          v-for="option in rule.ruleOptions"
          :key="option.key"
          :label="option.display_name | entityToString"
          :value="option.key"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_title')"
      prop="title"
    >
      <el-input
        v-model.trim="data.title"
        :placeholder="$t('menu.form_enter_title')"
      />
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_slug')"
      prop="slug"
    >
      <el-input
        v-model.trim="data.slug"
        :placeholder="$t('menu.form_enter_slug')"
      />
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_url')"
      prop="url"
    >
      <el-tooltip
        effect="dark"
        :content="$t('menu.form_url_tooltip')"
        placement="top"
      >
        <el-input
          v-model.trim="data.url"
          :placeholder="$t('menu.form_enter_url')"
        >
          <template slot="prepend">
            <i class="el-icon-link" />
          </template>
        </el-input>
      </el-tooltip>
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_method')"
      prop="method"
    >
      <el-select v-model="data.method">
        <el-option
          v-for="option in methodOptions"
          :key="option.key"
          :label="option.display_name"
          :value="option.key"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      :label="$t('menu.form_sort')"
      prop="sort"
    >
      <el-input
        v-model.trim="data.sort"
        :placeholder="$t('menu.form_enter_sort')"
      />
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        :loading="loading"
        @click="submit"
      >
        {{ $t('menu.form_save') }}
      </el-button>

      <el-button
        type="warning"
        @click="reset"
      >
        {{ $t('menu.form_clear') }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {
  getMenuChildren,
  createMenu
} from '@/api/menu'
import { getRuleChildrenList } from '@/api/authRule'

export default {
  name: 'MenuCreate',
  components: { },
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      loading: false,
      data: {
        pid: '',
        title: '',
        slug: '',
        url: '',
        method: 'GET',
        sort: 100
      },
      rules: {
        pid: [
          { required: true, message: this.$t('menu.rules_pid_required'), trigger: 'change' }
        ],
        title: [
          { required: true, message: this.$t('menu.rules_title_required'), trigger: 'blur' }
        ],
        slug: [
          { required: true, message: this.$t('menu.rules_slug_required'), trigger: 'blur' }
        ],
        url: [
          { required: true, message: this.$t('menu.rules_url_required'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.$t('menu.rules_sort_required'), trigger: 'blur' }
        ]
      },
      methodOptions: [
        { key: 'GET', display_name: 'GET' },
        { key: 'HEAD', display_name: 'HEAD' },
        { key: 'POST', display_name: 'POST' },
        { key: 'PUT', display_name: 'PUT' },
        { key: 'DELETE', display_name: 'DELETE' },
        { key: 'PATCH', display_name: 'PATCH' },
        { key: 'OPTIONS', display_name: 'OPTIONS' }
      ],
      parentOptions: [
        { key: '0', display_name: this.$t('menu.form_top_menu') }
      ],
      parentFilterOptions: [],
      rule: {
        visible: false,
        id: '',
        ruleList: [],
        ruleOptions: [],
        ruleFilterOptions: []
      }
    }
  },
  created () {
    this.initData()
    this.getRules()
  },
  methods: {
    initData () {
      const all = new Promise((resolve, reject) => {
        getMenuChildren({
          id: 0,
          type: 'list'
        }).then(res => {
          resolve(res.data)
        }).catch(_err => {
          reject(_err)
        })
      })

      Promise.all([all])
        .then(([all]) => {
          all.list.forEach(item => {
            this.parentOptions.push({
              key: item.id,
              display_name: item.spacer + ' ' + item.title + '【' + item.method + '】'
            })
          })

          this.parentFilterOptions = this.parentOptions
        })
        .catch(() => {

        })
    },
    parentFilter (val) {
      this.data.pid = val
      if (val) {
        this.parentOptions = this.parentFilterOptions.filter(item => {
          if (!!~item.display_name.indexOf(val) ||
            !!~item.display_name.toUpperCase().indexOf(val.toUpperCase())
          ) {
            return true
          }
        })
      } else {
        this.parentOptions = this.parentFilterOptions
      }
    },
    pidChange (val) {
      if (val) {
        this.parentOptions = this.parentFilterOptions
      }
    },

    ruleFastSelect () {
      this.rule.visible = !this.rule.visible
    },
    getRules () {
      return new Promise((resolve, reject) => {
        getRuleChildrenList({
          id: 0,
          type: 'list'
        }).then(res => {
          this.rule.ruleList = []
          this.rule.ruleOptions = []
          this.rule.ruleFilterOptions = []

          res.data.list.forEach(item => {
            this.rule.ruleList.push(item)
            this.rule.ruleOptions.push({
              key: item.id,
              display_name: item.spacer + ' ' + item.title + '【' + item.method + '】'
            })
          })

          this.rule.ruleFilterOptions = this.rule.ruleOptions

          resolve(res.data)
        }).catch(_err => {
          reject(_err)
        })
      })
    },
    ruleFilter (val) {
      if (val) {
        this.rule.ruleOptions = this.rule.ruleFilterOptions.filter(item => {
          if (!!~item.display_name.indexOf(val) ||
            !!~item.display_name.toUpperCase().indexOf(val.toUpperCase())
          ) {
            return true
          }
        })
      } else {
        this.rule.ruleOptions = this.rule.ruleFilterOptions
      }
    },
    ruleChange (val) {
      if (val) {
        this.rule.ruleOptions = this.rule.ruleFilterOptions
        this.rules

        const clickItem = this.rule.ruleList.filter(item => {
          if (val == item.id) {
            return true
          }
        })
        const item = clickItem[0]

        this.data.title = item.title
        this.data.slug = item.slug
        this.data.url = item.url
        this.data.method = item.method
      }
    },

    reset () {
      this.rule.id = ''
      this.$refs.menuCreateform.resetFields()
    },
    submit () {
      const thiz = this

      this.loading = true

      this.$refs.menuCreateform.validate(valid => {
        if (!valid) {
          this.loading = false

          return false
        }

        createMenu(this.data).then(response => {
          thiz.loading = false

          this.$message({
            message: this.$t('menu.form_create_success'),
            type: 'success',
            duration: 2 * 1000,
            onClose () {
              if (thiz.$refs.menuCreateform !== undefined) {
                thiz.$refs.menuCreateform.resetFields()
              }
              thiz.item.dialogVisible = false
            }
          })
        }).catch(_err => {
          thiz.loading = false
        })
      })
    }
  }
}
</script>

<style scoped>
.fast-select {
  margin-left: 15px;
}
</style>
