<template>
  <el-card class="chart-wrapper">
    <div
      slot="header"
      class="chart-header"
    >
      <span class="chart-title">{{
        $t('dashboard.summary_report_title')
      }}</span>
      <div class="chart-controls">
        <el-select
          v-model="state.selectedRange"
          :placeholder="$t('dashboard.select_time_range')"
          size="small"
          style="width: 120px"
          @change="handleRangeChange"
        >
          <el-option
            v-for="option in state.rangeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>
    </div>

    <div
      v-loading="state.loading"
      :element-loading-text="$t('dashboard.loading_chart_data')"
      class="chart-container"
      :style="{ height: height, width: width }"
    >
      <VChart
        v-if="!state.loading && !state.error && !state.isEmpty"
        :option="chartOptions"
        autoresize
        :style="{ height: '100%', width: '100%' }"
      />
    </div>

    <div
      v-if="state.error"
      class="error-message"
    >
      <el-alert
        :title="$t('dashboard.chart_error_title')"
        :description="state.errorMessage"
        type="error"
        show-icon
        :closable="false"
      />
    </div>

    <div
      v-if="!state.loading && !state.error && state.isEmpty"
      class="empty-message"
    >
      <el-alert
        :title="$t('dashboard.no_data_title')"
        :description="$t('dashboard.no_data_description')"
        type="info"
        show-icon
        :closable="false"
      />
    </div>
  </el-card>
</template>

<script setup>
import { getSummaryReport } from '@/api/dashboard'
import { LineChart } from 'echarts/charts'
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed, onMounted, reactive, watch } from 'vue'
import VChart from 'vue-echarts'
import { useI18n } from 'vue-i18n-bridge'

// Register ECharts components
use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  DatasetComponent,
  GridComponent,
  LineChart,
  CanvasRenderer,
  TransformComponent
])

// Props
const props = defineProps({
  className: {
    type: String,
    default: 'summary-report-chart'
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  }
})

// Composition API setup
const { t } = useI18n()

// Reactive state
const state = reactive({
  loading: false,
  error: false,
  errorMessage: '',
  isEmpty: false,
  selectedRange: 7,
  chartData: null,
  rangeOptions: [],
  rawApiData: null // Store raw API data for re-processing on language change
})

// Computed properties
const currentLanguage = computed(() => {
  // Access i18n locale through the composable
  return t.locale || 'en'
})
// Functions
const fetchData = async () => {
  state.loading = true
  state.error = false
  state.isEmpty = false

  try {
    const response = await getSummaryReport({
      range: state.selectedRange
    })

    // Store raw API data for re-processing on language change
    state.rawApiData = response.data

    // Transform the new API response format
    state.chartData = transformApiData(response.data)

    // Check if data is empty
    state.isEmpty =
      !state.chartData ||
      !state.chartData.dates ||
      state.chartData.dates.length === 0
  } catch (error) {
    state.error = true
    state.errorMessage =
      (error.response &&
        error.response.data &&
        error.response.data.message) ||
      t('dashboard.chart_error_message')
    console.error('Error fetching summary report data:', error)
  } finally {
    state.loading = false
  }
}

const transformApiData = (apiData) => {
  // Handle the new API response format
  if (!apiData || !Array.isArray(apiData)) {
    return {
      dates: [],
      series: []
    }
  }

  // Extract dates and format them with current locale
  const dates = apiData.map((item) => {
    const date = new Date(item.bet_date)
    // Use current application locale for date formatting
    const currentLocale = currentLanguage.value || 'en'
    // Map application locale to browser locale format
    const localeMap = {
      en: 'en-US',
      zh: 'zh-CN'
    }
    const browserLocale = localeMap[currentLocale] || 'en-US'

    return date.toLocaleDateString(browserLocale, {
      month: 'short',
      day: 'numeric'
    })
  })

  // Extract bet amounts (handle null values)
  const betAmounts = apiData.map((item) => {
    return item.bet_amount || 0
  })

  // Extract total profits (handle null values)
  const totalProfits = apiData.map((item) => {
    return item.total_profit || 0
  })

  return {
    dates: dates,
    series: [
      {
        name: t('dashboard.bet_amount'),
        data: betAmounts
      },
      {
        name: t('dashboard.total_profit'),
        data: totalProfits
      }
    ]
  }
}

// Chart options computed property
const chartOptions = computed(() => {
  if (!state.chartData) {
    return {}
  }

  return {
    title: {
      text: t('dashboard.summary_report_chart_title'),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params) => {
        let result = params[0].name + '<br/>'
        params.forEach((param) => {
          const value = param.value
          const formattedValue = formatCurrencyValue(value)
          result += `${param.marker} ${param.seriesName}: ${formattedValue}<br/>`
        })
        return result
      }
    },
    legend: {
      data:
        (state.chartData.series &&
          state.chartData.series.map((s) => s.name)) ||
        [],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: state.chartData.dates || [],
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return formatAxisValue(value)
        }
      }
    },
    series: generateSeriesConfig()
  }
})

const generateSeriesConfig = () => {
  if (!state.chartData.series) {
    return []
  }

  return state.chartData.series.map((seriesData, index) => {
    // Use index-based approach to ensure consistent colors across languages
    // Index 0: Bet Amount (blue), Index 1: Total Profit (green)
    const isProfit = index === 1

    return {
      name: seriesData.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: isProfit ? '#67c23a' : '#409eff'
      },
      itemStyle: {
        color: isProfit ? '#67c23a' : '#409eff'
      },
      areaStyle: {
        opacity: 0.1,
        color: isProfit ? '#67c23a' : '#409eff'
      },
      data: seriesData.data || []
    }
  })
}

const formatCurrencyValue = (value) => {
  if (value === null || value === undefined) {
    return '0'
  }
  // Use current locale for number formatting
  const currentLocale = currentLanguage.value || 'en'
  const localeMap = {
    en: 'en-US',
    zh: 'zh-CN'
  }
  const browserLocale = localeMap[currentLocale] || 'en-US'

  return value.toLocaleString(browserLocale)
}

const formatAxisValue = (value) => {
  if (Math.abs(value) >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (Math.abs(value) >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

const handleRangeChange = () => {
  fetchData()
}

// Update range options with current language translations
const updateRangeOptions = () => {
  state.rangeOptions = [
    {
      value: 7,
      label: t('dashboard.last_7_days')
    },
    {
      value: 14,
      label: t('dashboard.last_14_days')
    },
    {
      value: 30,
      label: t('dashboard.last_30_days')
    }
  ]
}

// Handle language change by re-processing existing data
const handleLanguageChange = () => {
  console.log('Handling language change in SummaryReportChart')

  // Update range options with new translations
  updateRangeOptions()

  // If we have raw API data, re-process it with new language
  if (state.rawApiData) {
    console.log('Re-processing chart data for new language')
    state.chartData = transformApiData(state.rawApiData)
  }
}

// Watch for language changes and refresh chart
watch(currentLanguage, (newLang, oldLang) => {
  if (oldLang && newLang !== oldLang) {
    console.log('Language changed from', oldLang, 'to', newLang)
    handleLanguageChange()
  }
})

// Initialize component
onMounted(() => {
  updateRangeOptions()
  fetchData()
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  margin-bottom: 32px;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .chart-controls {
      display: flex;
      align-items: center;
    }
  }

  .chart-container {
    min-height: 400px;
  }

  .error-message,
  .empty-message {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .chart-wrapper {
    .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .chart-container {
      min-height: 300px;
    }
  }
}
</style>
