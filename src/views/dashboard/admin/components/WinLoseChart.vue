<template>
  <ChartWrapper
    :loading="state.loading"
    :error="state.error"
    :error-message="state.errorMessage"
    :is-empty="state.isEmpty"
    :chart-title="$t('dashboard.win_lost.title')"
  >
    <template #chartStatistics>
      <el-row
        :gutter="20"
        style="width: 100%;"
      >
        <el-col :span="6">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              :value="state.today"
              :title="$t('dashboard.today')"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              :value="state.last_seven_days"
              :title="$t('dashboard.last_seven_days')"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              :value="state.last_twenty_one_days"
              :title="$t('dashboard.last_twenty_one_days')"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              :value="state.last_thirty_days"
              :title="$t('dashboard.last_thirty_days')"
            />
          </div>
        </el-col>
      </el-row>
    </template>
    <template #chart>
      <VChart
        :option="state.chartOptions"
        autoresize
      />
    </template>
  </ChartWrapper>
</template>

<script setup>
import { getAverateWinLostReport } from '@/api/dashboard'
import { LineChart } from 'echarts/charts'
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed, onMounted, reactive } from 'vue'
import VChart from 'vue-echarts'
import { useI18n } from 'vue-i18n-bridge'
import { ChartWrapper } from '@/components/Charts'
use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  DatasetComponent,
  GridComponent,
  LineChart,
  CanvasRenderer,
  TransformComponent
])
const { t } = useI18n()
const state = reactive({
  chart: '',
  today: 0,
  last_seven_days: 0,
  last_twenty_one_days: 0,
  last_thirty_days: 0,
  chartData: [

  ],
  chartOptions: {}
})
const fetchData = async () => {
  // const result = await getRetentionReport()
  const result = await getAverateWinLostReport()
  state.chartData = result.data.chart_data
  state.today = result.data.today
  state.last_seven_days = result.data.last_seven_days
  state.last_twenty_one_days = result.data.last_twenty_one_days
  state.last_thirty_days = result.data.last_thirty_days
}
onMounted(() =>
  fetchData()
)

state.chartOptions = computed(() => {
  return {
    title: {
      text: t('dashboard.win_lost.title'),
      left: '10%',
      top: 0,
      textAlign: 'center'
    },
    legend: {
      show: true
    },
    tooltip: {
      trigger: 'axis'
      // formatter: (params) => {
      //   let result = params[0].name + '<br/>'
      //   params.forEach(param => {
      //     result += `${param.marker} ${param.seriesName}: ${param.value.bet_amount}<br/>`
      //   })
      //   return result
      // }
    },
    dataset: [{
      source: state.chartData,
      dimensions: ['bet_date', 'total_winning_amount', 'total_winning_amount', 'average_win_lose']
    }
    ],
    xAxis: {
      type: 'category',
      name: 'Date',
      value: 'bet_date',
      axisTick: {
        alignWithLabel: true
      },
      boundaryGap: false,
      axisLabel: {
        rotate: 0,
        hideOverlap: false,
        fontSize: 11,
        margin: 13
      },
      nameTextStyle: {
        fontSize: 12
      }
    },
    yAxis: {
      name: t('dashboard.bet.bet_amount'),
      type: 'value',
      data: {
        value: 'average_win_lose'
      }
    },
    series: [
      {
        name: t('dashboard.bet.bet_amount'),
        type: 'line',
        lineStyle: {
          color: '#ee6666'
        },
        color: '#ee6666'
      }
    ]
  }
}

)

</script>
