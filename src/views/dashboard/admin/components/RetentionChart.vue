<template>
  <ChartWrapper
    :loading="state.loading"
    :error="state.error"
    :error-message="state.errorMessage"
    :is-empty="state.isEmpty"
    :chart-title="$t('dashboard.retention_report.title')"
  >
    <template #chart>
      <VChart
        :option="state.chartOptions"
        autoresize
      />
    </template>
  </ChartWrapper>
</template>

<script setup>
import { getRetentionReport } from '@/api/dashboard'
import { LineChart } from 'echarts/charts'
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed, onMounted, reactive } from 'vue'
import VChart from 'vue-echarts'
import { useI18n } from 'vue-i18n-bridge'
import { ChartWrapper } from '@/components/Charts'

use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  Too<PERSON><PERSON>Component,
  <PERSON>setComponent,
  <PERSON><PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
  TransformComponent
])
const { t } = useI18n()
const state = reactive({
  loading: false,
  error: false,
  errorMessage: '',
  isEmpty: false,
  chart: '',
  chartData: [

  ],
  chartOptions: {}
})
const fetchData = () => {
  state.loading = true
  getRetentionReport().then(response => {
    state.loading = false
    state.chartData = response.data
  }).catch(error => {
    state.loading = false
    state.error = true
    state.errorMessage = error.message
    console.error('Error fetching retention report data:', error)
  })
}
onMounted(() =>
  fetchData()
)

state.chartOptions = computed(() => {
  return {
    // title: {
    //   text: t('dashboard.retention_report.title'),
    //   left: '10%',
    //   top: 0,
    //   textAlign: 'center'
    // },
    legend: {
      show: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${param.value.retention_percentage}%<br/>`
        })
        return result
      }
    },
    dataset: [{
      source: state.chartData,
      dimensions: ['total_retent_player', 'retention_percentage', 'retention_day', 'total_player', 'date']
    },
    {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 3 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    },
    {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 7 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    }, {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 14 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    }, {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 21 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    },
    {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 30 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    },
    {
      transform: [{
        type: 'filter',
        config: { dimension: 'retention_day', value: 7 }
      }, {
        type: 'sort',
        config: { dimension: 'date', order: 'asc' }
      }]
    }

    ],
    xAxis: {
      type: 'category',
      name: 'Date',
      value: 'date',
      axisTick: {
        alignWithLabel: true
      },
      boundaryGap: false,
      axisLabel: {
        rotate: 0,
        hideOverlap: false,
        fontSize: 11,
        margin: 13
      },
      nameTextStyle: {
        fontSize: 12
      }
    },
    yAxis: {
      name: 'Percentage',
      type: 'value',
      data: {
        value: 'retention_percentage'
      }
    },
    series: [
      {
        name: t('dashboard.retention_report.3days'),
        type: 'line',
        datasetIndex: 1,
        dimensions: ['date', 'retention_percentage', 'retention_day']
      },
      {
        name: t('dashboard.retention_report.7days'),
        type: 'line',
        datasetIndex: 2,
        dimensions: ['date', 'retention_percentage', 'retention_day']
      },
      {
        name: t('dashboard.retention_report.14days'),
        type: 'line',
        datasetIndex: 3,
        dimensions: ['date', 'retention_percentage', 'retention_day']
      },

      {
        name: t('dashboard.retention_report.21days'),
        type: 'line',
        datasetIndex: 4,
        dimensions: ['date', 'retention_percentage', 'retention_day']
      },
      {
        name: t('dashboard.retention_report.30days'),
        type: 'line',
        datasetIndex: 5,
        dimensions: ['date', 'retention_percentage', 'retention_day']
      }
    ]
  }
}

)

</script>
