<template>
  <div style="width: 100%; height: 300px">
    <VChart
      :option="state.chartOptions"
      autoresize
    />
  </div>
</template>

<script setup>
import { LineChart } from 'echarts/charts'
import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed, nextTick, reactive, watch } from 'vue'
import VChart from 'vue-echarts'
import { useI18n } from 'vue-i18n-bridge'

use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  DatasetComponent,
  GridComponent,
  LineChart,
  CanvasRenderer
])
const props = defineProps(['stats'])
const { t } = useI18n()
const state = reactive({
  chart: '',
  chartData: {
    date: '',
    hourly_data: []
  },
  chartOptions: {}
})

watch(() => props.stats, async () => {
  state.chartData = {
    ...props.stats
  }
  await nextTick()
}, { deep: true })

state.chartOptions = computed(() => {
  return {
    title: {
      text: t('dashboard.register.title'),
      subtext: state.chartData.date,
      left: '45%',
      top: 0,
      textAlign: 'center'
    },
    legend: {
      show: false
    },
    tooltip: {
      trigger: 'axis'
    },
    dataset: {
      dimensions: ['hour', 'member_count'],
      source: state.chartData.hourly_data
    },
    xAxis: {
      type: 'category',
      name: 'Hours',
      axisTick: {
        alignWithLabel: true
      },
      boundaryGap: false,
      axisLabel: {
        rotate: 0,
        hideOverlap: false,
        fontSize: 11,
        margin: 13
      },
      nameTextStyle: {
        fontSize: 12
      }
    },
    yAxis: {
      name: 'Percentage',
      type: 'value'
    },
    series: [
      {
        name: t('dashboard.hourly_register'),
        type: 'line'
      }
    ]
  }
}

)

</script>
