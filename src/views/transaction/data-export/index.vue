<template>
  <div class="developing-container">
    <h1>Under Development</h1>
    <p>This page is currently under development.</p>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Developing component mounted')
})
</script>

<style lang="scss" scoped>
.developing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}
</style>
