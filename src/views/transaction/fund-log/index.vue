<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('fund_logs.header_title') }}</span>
      </div>

      <!-- Filter Section -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.client_id"
          :placeholder="$t('fund_logs.client_id_placeholder')"
          clearable
          style="width: 150px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-input
          v-model="listQuery.player_id"
          :placeholder="$t('fund_logs.player_id_placeholder')"
          clearable
          style="width: 150px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-input
          v-model="listQuery.player_username"
          :placeholder="$t('fund_logs.player_username_placeholder')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-select
          v-model="listQuery.type"
          :placeholder="$t('fund_logs.type_placeholder')"
          clearable
          class="filter-item"
          style="width: 200px;margin-right: 10px;"
        >
          <el-option
            v-for="type in filterOptions.types"
            :key="type"
            :label="$t(`fund_logs.types.${type}`)"
            :value="type"
          />
        </el-select>

        <el-select
          v-model="listQuery.currency"
          :placeholder="$t('fund_logs.currency_placeholder')"
          clearable
          class="filter-item"
          style="width: 180px;margin-right: 10px;"
        >
          <el-option
            v-for="currency in filterOptions.currencies"
            :key="currency"
            :label="currency"
            :value="currency"
          />
        </el-select>

        <el-input
          v-model="listQuery.amount_min"
          :placeholder="$t('fund_logs.amount_min_placeholder')"
          clearable
          style="width: 180px;margin-right: 10px;"
          class="filter-item"
          type="number"
          @keyup.enter.native="handleFilter"
        />

        <el-input
          v-model="listQuery.amount_max"
          :placeholder="$t('fund_logs.amount_max_placeholder')"
          clearable
          style="width: 180px;margin-right: 10px;"
          class="filter-item"
          type="number"
          @keyup.enter.native="handleFilter"
        />

        <el-date-picker
          v-model="listQuery.created_at_from"
          type="datetime"
          :placeholder="$t('fund_logs.created_at_from_placeholder')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />

        <el-date-picker
          v-model="listQuery.created_at_to"
          type="datetime"
          :placeholder="$t('fund_logs.created_at_to_placeholder')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('fund_logs.search_btn') }}
        </el-button>

        <el-button
          class="filter-item"
          type="default"
          icon="el-icon-refresh"
          @click="handleReset"
        >
          {{ $t('fund_logs.reset_btn') }}
        </el-button>
      </div>

      <!-- Table Section -->
      <div class="table-container">
        <el-table
          v-loading="listLoading"
          :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          :data="list"
          class="border-gray fund-logs-table"
          highlight-current-row
          style="width: 100%; min-width: 1150px;"
          @sort-change="handleSortChange"
        >
          <el-table-column
            :label="$t('fund_logs.id')"
            prop="id"
            width="80"
            sortable="custom"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.client_id')"
            prop="client_id"
            width="110"
            sortable="custom"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.client_id }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.player_username')"
            prop="player_username"
            sortable="custom"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.player_username }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.type')"
            prop="type"
            width="100"
            sortable="custom"
          >
            <template slot-scope="scope">
              <el-tag
                :type="getTransactionTypeTagType(scope.row.type)"
                size="small"
              >
                {{ $t(`fund_logs.types.${scope.row.type}`) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.amount')"
            prop="amount"
            sortable="custom"
            align="right"
          >
            <template slot-scope="scope">
              <span :class="getAmountClass(scope.row.type)">
                {{ formatAmount(scope.row.amount) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.balance_before')"
            prop="balance_before"
            sortable="custom"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatAmount(scope.row.balance_before) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.balance_after')"
            prop="balance_after"
            sortable="custom"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatAmount(scope.row.balance_after) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('fund_logs.currency')"
            prop="currency"
            align="center"
          >
            <template slot-scope="scope">
              <span> {{ scope.row.currency }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.description')"
            prop="description"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.description }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('fund_logs.created_at')"
            prop="created_at"
            sortable="custom"
          >
            <template slot-scope="scope">
              <span>
                <i class="el-icon-time" />&nbsp;
                {{ appDatetime(scope.row.created_at) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.per_page"
        @pagination="getListData"
      />
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { getFundLogs, getFundLogsFilterOptions } from '@/api/fundLogs'
import BackToTop from '@/components/BackToTop'
import Pagination from '@/components/Pagination'
import { appDatetime } from '@/filters'
import { Message } from 'element-ui'
import { useI18n } from 'vue-i18n-bridge'
import '@/styles/back-to-top.scss'

const ElMessage = Message
const { t } = useI18n()

// Reactive data
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const filterOptions = ref({
  types: [],
  currencies: []
})

const listQuery = reactive({
  client_id: '',
  player_id: '',
  player_username: '',
  type: '',
  currency: '',
  amount_min: '',
  amount_max: '',
  date_from: '',
  date_to: '',
  created_at_from: '',
  created_at_to: '',
  sort_field: 'created_at',
  sort_order: 'desc',
  page: 1,
  per_page: 10
})

// Methods
const getListData = async () => {
  listLoading.value = true

  try {
    // Build query parameters
    const params = {
      page: listQuery.page,
      per_page: listQuery.per_page,
      sort_field: listQuery.sort_field,
      sort_order: listQuery.sort_order
    }

    // Add filter parameters if they exist
    if (listQuery.client_id) params.client_id = listQuery.client_id
    if (listQuery.player_id) params.player_id = listQuery.player_id
    if (listQuery.player_username) params.player_username = listQuery.player_username
    if (listQuery.type) params.type = listQuery.type
    if (listQuery.currency) params.currency = listQuery.currency
    if (listQuery.amount_min) params.amount_min = listQuery.amount_min
    if (listQuery.amount_max) params.amount_max = listQuery.amount_max
    if (listQuery.date_from) params.date_from = listQuery.date_from
    if (listQuery.date_to) params.date_to = listQuery.date_to
    if (listQuery.created_at_from) params.created_at_from = listQuery.created_at_from
    if (listQuery.created_at_to) params.created_at_to = listQuery.created_at_to

    const response = await getFundLogs(params)
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  } catch (error) {
    console.error('Error fetching fund logs:', error)
    ElMessage({
      message: error.response?.data?.message || t('fund_logs.fetch_error'),
      type: 'error',
      duration: 5000
    })
    listLoading.value = false
  }
}

const getFilterOptionsData = async () => {
  try {
    const response = await getFundLogsFilterOptions()
    filterOptions.value = response.data
  } catch (error) {
    console.error('Error fetching filter options:', error)
    ElMessage({
      message: error.response?.data?.message || t('fund_logs.fetch_filter_options_error'),
      type: 'error',
      duration: 5000
    })
  }
}

const handleFilter = () => {
  // Validate amount range
  if (listQuery.amount_min && listQuery.amount_max) {
    if (parseFloat(listQuery.amount_max) < parseFloat(listQuery.amount_min)) {
      ElMessage({
        message: t('fund_logs.amount_range_error'),
        type: 'error',
        duration: 5000
      })
      return
    }
  }

  // Validate date range
  if (listQuery.date_from && listQuery.date_to) {
    if (new Date(listQuery.date_to) < new Date(listQuery.date_from)) {
      ElMessage({
        message: t('fund_logs.date_range_error'),
        type: 'error',
        duration: 5000
      })
      return
    }
  }

  // Validate created_at range
  if (listQuery.created_at_from && listQuery.created_at_to) {
    if (new Date(listQuery.created_at_to) < new Date(listQuery.created_at_from)) {
      ElMessage({
        message: t('fund_logs.date_range_error'),
        type: 'error',
        duration: 5000
      })
      return
    }
  }

  listQuery.page = 1
  getListData()
}

const handleReset = () => {
  // Reset all filter fields
  listQuery.client_id = ''
  listQuery.player_id = ''
  listQuery.player_username = ''
  listQuery.type = ''
  listQuery.currency = ''
  listQuery.amount_min = ''
  listQuery.amount_max = ''
  listQuery.date_from = ''
  listQuery.date_to = ''
  listQuery.created_at_from = ''
  listQuery.created_at_to = ''
  listQuery.sort_field = 'created_at'
  listQuery.sort_order = 'desc'
  listQuery.page = 1
  getListData()
}

const handleSortChange = ({ column, prop, order }) => {
  if (prop) {
    listQuery.sort_field = prop
    listQuery.sort_order = order === 'ascending' ? 'asc' : 'desc'
    listQuery.page = 1
    getListData()
  }
}

// Utility functions
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return parseFloat(amount).toFixed(2)
}

const getTransactionTypeTagType = (type) => {
  const typeMap = {
    'bet': 'warning',
    'won': 'success'
  }
  return typeMap[type] || 'info'
}

const getAmountClass = (type) => {
  return type === 'bet' ? 'amount-negative' : 'amount-positive'
}

// Lifecycle
onMounted(() => {
  getFilterOptionsData()
  getListData()
})
</script>

<style scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed;
}

.filter-container {
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

/* Table container with horizontal scroll */
.table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  margin-bottom: 20px;
}

/* Custom scrollbar styling */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure table maintains minimum width for all columns */
.fund-logs-table {
  min-width: 1150px;
}

/* Fix header text wrapping and styling */
.fund-logs-table .el-table__header-wrapper .el-table__header .has-gutter th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fund-logs-table .el-table__header th {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 12px 0;
  font-weight: 600;
  font-size: 13px;
}

.fund-logs-table .el-table__header th .cell {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 8px;
  line-height: 1.2;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.amount-negative {
  color: #f56c6c;
  font-weight: bold;
}

.amount-positive {
  color: #67c23a;
  font-weight: bold;
}

/* Responsive design for mobile devices */
@media (max-width: 1200px) {
  .filter-container .filter-item {
    margin-bottom: 15px;
  }

  /* Ensure horizontal scroll is always available on smaller screens */
  .table-container {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .fund-logs-table {
    min-width: 1150px; /* Maintain minimum width for proper column display */
  }
}

@media (max-width: 768px) {
  .filter-container .filter-item {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 15px;
  }

  .filter-container .el-input,
  .filter-container .el-select,
  .filter-container .el-date-picker {
    width: 100% !important;
  }

  .filter-container .el-button {
    width: 48% !important;
    margin-right: 2% !important;
  }

  .filter-container .el-button:nth-last-child(1) {
    margin-right: 0 !important;
  }

  /* Enhanced mobile scrolling */
  .table-container {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Make scrollbar more visible on mobile */
  .table-container::-webkit-scrollbar {
    height: 12px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #909399;
    border-radius: 6px;
  }
}

@media (max-width: 480px) {
  .filter-container .el-button {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }

  /* Ensure table is always scrollable on very small screens */
  .table-container {
    margin-left: -10px;
    margin-right: -10px;
    padding: 0 10px;
  }
}
</style>
