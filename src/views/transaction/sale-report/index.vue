<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.menu.transaction.sale_report') }}</span>
      </div>
      <table-data
        :data="state.tableData"
        :current-page="state.currentPage"
        :total="state.total"
        :loading="state.loading"
        @change="handleChangePage"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-table
              :data="props.row.regional_reports"
            >
              <el-table-column
                :label="$t('transaction.sale_report.regional_agent_name')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.regional_agent_name }}</span>
                </template>
              </el-table-column>

              <el-table-column
                min-width="120px"
                :label="$t('transaction.sale_report.total_players')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.total_players }}</span>
                </template>
              </el-table-column>

              <el-table-column
                min-width="100px"
                :label="$t('transaction.sale_report.valid_players')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.valid_players }}</span>
                </template>
              </el-table-column>

              <el-table-column
                min-width="100px"
                :label="$t('transaction.sale_report.bet_amount')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.bet_amount | appCurrency }}</span>
                </template>
              </el-table-column>

              <el-table-column
                min-width="100px"
                :label="$t('transaction.sale_report.profit_amount')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.profit_amount | appCurrency }}</span>
                </template>
              </el-table-column>
              <el-table-column
                min-width="100px"
                :label="$t('transaction.sale_report.bet_times')"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.bet_times }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          width="150px"
          :label="$t('transaction.sale_report.general_agent_name')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.general_agent_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="120px"
          :label="$t('transaction.sale_report.total_players')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.total_players }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('transaction.sale_report.valid_players')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.valid_players }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('transaction.sale_report.bet_amount')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bet_amount | appCurrency }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('transaction.sale_report.profit_amount')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.profit_amount | appCurrency }}</span>
          </template>
        </el-table-column>
        <el-table-column
          min-width="100px"
          :label="$t('transaction.sale_report.bet_times')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bet_times }}</span>
          </template>
        </el-table-column>
      </table-data>
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import TableData from '@/components/TableData/index.vue'
import {
  getSaleReport
} from '@/api/transaction'
import BackToTop from '@/components/BackToTop'
const state = reactive({
  tableData: [],
  loading: false,
  filters: {
    page: 1,
    pageSize: 10
  },
  currentPage: 1,
  total: 0
})

const fetchData = () => {
  state.loading = true
  getSaleReport(state.filters).then(response => {
    state.tableData = response.data.table_data
    state.total = response.data.total
    state.loading = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    state.loading = false
  })
}
const handleChangePage = (page) => {
  state.filters = {
    ...state.filters,
    ...page
  }
  fetchData()
  // Fetch data for the new page
}
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed; /* required for BackToTop to work correctly */
}
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
