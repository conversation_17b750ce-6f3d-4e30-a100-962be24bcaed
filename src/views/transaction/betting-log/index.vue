<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.menu.transaction.betting_log') }}</span>
      </div>
      <div class="filter-container">
        <el-select
          v-if="userType == 'system'"
          v-model="state.filters.general_agent_uuid"
          :placeholder="$t('bet.general_agent')"
          clearable
          class="filter-item"
          style="width: 180px;margin-right: 10px;"
        >
          <el-option
            v-for="generalAgent in selectState.generalAgentList"
            :key="generalAgent.id"
            :label="generalAgent.nickname"
            :value="generalAgent.id"
          />
        </el-select>
        <el-select
          v-if="userType == 'system' || userType == 'general'"
          v-model="state.filters.regional_agent_uuid"
          :placeholder="$t('bet.regional_agent')"
          clearable
          class="filter-item"
          style="width: 180px;margin-right: 10px;"
        >
          <el-option
            v-for="regionalAgentList in selectState.regionalAgentList"
            :key="regionalAgentList.id"
            :label="regionalAgentList.nickname"
            :value="regionalAgentList.id"
          />
        </el-select>
        <el-select
          v-if="userType == 'system' || userType == 'general' || userType == 'regional'"
          v-model="state.filters.local_agent_uuid"
          :placeholder="$t('bet.local_agent')"
          clearable
          filterable
          class="filter-item"
          style="width: 180px;margin-right: 10px;"
        >
          <el-option
            v-for="localAgent in selectState.localAgentList"
            :key="localAgent.id"
            :label="localAgent.nickname"
            :value="localAgent.id"
          />
        </el-select>
        <el-input
          v-model="state.filters.player_id"
          :placeholder="$t('bet.player_id')"
          clearable
          style="width: 180px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="state.filters.player_name"
          :placeholder="$t('bet.player_name')"
          clearable
          style="width: 180px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>
      </div>
      <table-data
        :data="state.tableData"
        :current-page="state.currentPage"
        :total="state.total"
        :loading="state.loading"
        @change="handleChangePage"
      >
        <el-table-column
          width="150px"
          :label="$t('common.player_name')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.player_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="120px"
          :label="$t('common.bet_id')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bet_id }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('common.bet_amount')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bet_amount }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('common.winning_amount')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.winning_amount }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('common.winning_status')"
        >
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.winning_status == 'won' ? 'success' : 'danger'"
              size="mini"
            >
              {{ $t('bet.status.' + scope.row.winning_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          width="180px"
          align="left"
          :label="$t('admin.table_create_time')"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.created_at) }}
            </span>
          </template>
        </el-table-column>
      </table-data>
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { nextTick, onMounted, reactive } from 'vue'
import TableData from '@/components/TableData/index.vue'
import {
  getBettingLogsTransaction
} from '@/api/transaction'
import BackToTop from '@/components/BackToTop'
import { appDatetime } from '@/filters'
import { getUserType, isSystemUser } from '@/utils/permission'
import { getListAgentByType } from '@/api/admin'
const state = reactive({
  tableData: [],
  loading: false,
  filters: {
    general_agent_uuid: null,
    regional_agent_uuid: null,
    local_agent_uuid: null,
    player_id: null,
    player_name: null,
    page: 1,
    pageSize: 10
  },
  currentPage: 1,
  total: 0
})

const selectState = reactive({
  localAgentList: [],
  regionalAgentList: [],
  generalAgentList: []
})

const userType = getUserType()
const handleFilter = async () => {
  if (state.filters.general_agent_uuid == '') {
    state.filters.general_agent_uuid = null
  }
  if (state.filters.regional_agent_uuid == '') {
    state.filters.regional_agent_uuid = null
  }
  if (state.filters.local_agent_uuid == '') {
    state.filters.local_agent_uuid = null
  }
  if (state.filters.player_id == '') {
    state.filters.player_id = null
  }
  if (state.filters.player_name == '') {
    state.filters.player_name = null
  }
  await nextTick()
  fetchData()
}
const fetchData = () => {
  state.loading = true
  getBettingLogsTransaction(state.filters).then(response => {
    state.tableData = response.data.data
    state.total = response.data.total
    state.loading = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    state.loading = false
  })
}
const handleChangePage = async (page) => {
  state.filters = {
    ...state.filters,
    ...page
  }
  await nextTick()
  fetchData()
  // Fetch data for the new page
}

const fetchAgentType = () => {
  const fetchAgentTypes = {
    system: ['general', 'regional', 'local'],
    general: ['regional', 'local'],
    regional: ['local']
  }

  const types = isSystemUser() ? fetchAgentTypes.system
    : userType === 'general' ? fetchAgentTypes.general
      : userType === 'regional' ? fetchAgentTypes.regional : []

  const agentListMap = {
    general: 'generalAgentList',
    regional: 'regionalAgentList',
    local: 'localAgentList'
  }

  types.forEach(type => {
    getListAgentByType(type)
      .then(response => {
        selectState[agentListMap[type]] = response.data
      })
      .catch(error => {
        console.error(`Error fetching ${type} agent list:`, error)
      })
  })
}
onMounted(() => {
  fetchData()
  fetchAgentType()
})
</script>

<style lang="scss" scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed; /* required for BackToTop to work correctly */
}
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
