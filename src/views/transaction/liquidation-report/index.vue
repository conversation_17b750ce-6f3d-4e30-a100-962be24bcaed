<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
        style="display: flex; align-items: center; justify-content: space-between;"
      >
        <span>{{ $t('route.menu.transaction.liquidation_report') }}</span>
        <div style="display: flex; align-items: center; gap: 8px;">
          <el-button
            :type="actionMode === 'V1' ? 'primary' : 'default'"
            size="mini"
            @click="actionMode = 'V1'"
          >
            V1
          </el-button>
          <el-button
            :type="actionMode === 'V2' ? 'primary' : 'default'"
            size="mini"
            @click="actionMode = 'V2'"
          >
            V2
          </el-button>
        </div>
      </div>

      <el-table
        :data="list"
        row-key="id"
        class="border-gray"
        style="width: 100%; margin-top: 15px;"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpand"
        border
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="level-indent level-2">
              <el-table
                :data="props.row.regional_reports"
                class="inner-table"
                style="width: 100%;"
                :show-header="true"
                row-key="id"
                :expand-row-keys="props.row.expandedRows || []"
                @expand-change="(row, expandedRowsArr) => handleInnerExpand(props.row, row, expandedRowsArr)"
                border
              >
                <el-table-column type="expand">
                  <template #default="innerProps">
                    <div class="level-indent level-3">
                      <el-table
                        :data="innerProps.row.local_agent_reports"
                        class="inner-table"
                        style="width: 100%;"
                        :show-header="true"
                        row-key="id"
                        border
                      >
                        <el-table-column
                          v-if="actionMode === 'V1'"
                          :label="t('common.action')"
                          :width="100"
                        >
                          <template #default="scope">
                            <el-dropdown @command="(command) => handleAction(command, scope.row)">
                              <el-button
                                type="text"
                                size="small"
                              >
                                {{ t('common.more') }}<el-icon class="el-icon--right">
                                  <arrow-down />
                                </el-icon>
                              </el-button>
                              <template #dropdown>
                                <el-dropdown-menu>
                                  <el-dropdown-item command="preview">
                                    {{ t('common.preview') }}
                                  </el-dropdown-item>
                                  <el-dropdown-item command="download">
                                    {{ t('common.download') }}
                                  </el-dropdown-item>
                                  <el-dropdown-item
                                    command="delete"
                                    divided
                                  >
                                    {{ t('common.delete') }}
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </template>
                        </el-table-column>
                        <el-table-column
                          v-if="actionMode === 'V2'"
                          :label="t('common.action')"
                          :width="200"
                        >
                          <template #default="scope">
                            <div class="action-btns-v2">
                              <el-button
                                type="text"
                                size="small"
                                @click="handleAction('preview', scope.row)"
                              >
                                {{ t('common.preview') }}
                              </el-button>
                              <el-button
                                type="text"
                                size="small"
                                @click="handleAction('download', scope.row)"
                              >
                                {{ t('common.download') }}
                              </el-button>
                              <el-button
                                type="text"
                                size="small"
                                style="color: #ff4949"
                                @click="handleAction('delete', scope.row)"
                              >
                                {{ t('common.delete') }}
                              </el-button>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="agent_name"
                          :label="t('route.menu.accounts.local_agents')"
                          width="220"
                        />
                        <el-table-column
                          prop="total_bet_amount"
                          :label="t('transaction.liquidation.total_bet_amount')"
                          width="180"
                        >
                          <template #default="scope">
                            <span :style="{ color: '#13ce66' }">{{ scope.row.total_bet_amount }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="total_profit"
                          :label="t('transaction.liquidation.total_profit')"
                          width="180"
                        >
                          <template #default="scope">
                            <span :style="{ color: scope.row.profit >= 0 ? '#13ce66' : '#ff4949' }">
                              {{ scope.row.total_profit }}
                            </span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="total_winning_amount"
                          :label="t('transaction.liquidation.total_winning_amount')"
                          width="180"
                        >
                          <template #default="scope">
                            <span>{{ scope.row.total_winning_amount }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          prop="report_date"
                          :label="t('transaction.liquidation.report_date')"
                          width="200"
                        >
                          <template #default="scope">
                            <span>
                              {{ appDatetime(scope.row.report_date) }}
                            </span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="actionMode === 'V1'"
                  :label="t('common.action')"
                  :width="100"
                >
                  <template #default="scope">
                    <el-dropdown @command="(command) => handleAction(command, scope.row)">
                      <el-button
                        type="text"
                        size="small"
                      >
                        {{ t('common.more') }}<el-icon class="el-icon--right">
                          <arrow-down />
                        </el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="preview">
                            {{ t('common.preview') }}
                          </el-dropdown-item>
                          <el-dropdown-item command="download">
                            {{ t('common.download') }}
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="delete"
                            divided
                          >
                            {{ t('common.delete') }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="actionMode === 'V2'"
                  :label="t('common.action')"
                  :width="200"
                >
                  <template #default="scope">
                    <div class="action-btns-v2">
                      <el-button
                        type="text"
                        size="small"
                        @click="handleAction('preview', scope.row)"
                      >
                        {{ t('common.preview') }}
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click="handleAction('download', scope.row)"
                      >
                        {{ t('common.download') }}
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        style="color: #ff4949"
                        @click="handleAction('delete', scope.row)"
                      >
                        {{ t('common.delete') }}
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="agent_name"
                  :label="t('route.menu.accounts.local_agents')"
                  width="200"
                />
                <el-table-column
                  prop="total_bet_amount"
                  :label="t('transaction.liquidation.total_bet_amount')"
                  width="180"
                >
                  <template #default="scope">
                    <span :style="{ color: '#13ce66' }">{{ scope.row.total_bet_amount }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="total_winning_amount"
                  :label="t('transaction.liquidation.total_winning_amount')"
                  width="180"
                >
                  <template #default="scope">
                    <span>{{ scope.row.total_winning_amount }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="total_profit"
                  :label="t('transaction.liquidation.total_profit')"
                  width="180"
                >
                  <template #default="scope">
                    <span :style="{ color: scope.row.total_profit >= 0 ? '#13ce66' : '#ff4949' }">
                      {{ scope.row.total_profit }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="rebate_rate"
                  :label="t('transaction.liquidation.rebate_rate')"
                  width="150"
                >
                  <template #default="scope">
                    {{ (scope.row.rebate_rate * 100).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="total_rebate_amount"
                  :label="t('transaction.liquidation.total_rebate_amount')"
                  width="180"
                >
                  <template #default="scope">
                    <span>
                      {{ scope.row.total_rebate_amount }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="report_date"
                  :label="t('transaction.liquidation.report_date')"
                  width="250"
                >
                  <template #default="scope">
                    <span>
                      {{ appDatetime(scope.row.report_date) }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="actionMode === 'V1'"
          :label="t('common.action')"
          :width="100"
        >
          <template #default="scope">
            <el-dropdown @command="(command) => handleAction(command, scope.row)">
              <el-button
                type="text"
                size="small"
              >
                {{ t('common.more') }}<el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="preview">
                    {{ t('common.preview') }}
                  </el-dropdown-item>
                  <el-dropdown-item command="download">
                    {{ t('common.download') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="delete"
                    divided
                  >
                    {{ t('common.delete') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column
          v-if="actionMode === 'V2'"
          :label="t('common.action')"
          :width="200"
        >
          <template #default="scope">
            <div class="action-btns-v2">
              <el-button
                type="text"
                size="small"
                @click="handleAction('preview', scope.row)"
              >
                {{ t('common.preview') }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleAction('download', scope.row)"
              >
                {{ t('common.download') }}
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #ff4949"
                @click="handleAction('delete', scope.row)"
              >
                {{ t('common.delete') }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="report_no"
          :label="t('transaction.liquidation.report_number')"
          width="180"
        />
        <el-table-column
          prop="agent_name"
          :label="t('route.menu.accounts.general_agents')"
          width="290"
        />
        <el-table-column
          prop="total_bet_amount"
          :label="t('transaction.liquidation.total_bet_amount')"
          width="150"
        >
          <template #default="scope">
            <span :style="{ color: '#13ce66' }">{{ scope.row.total_bet_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="total_profit"
          :label="t('transaction.liquidation.total_profit')"
          width="150"
        >
          <template #default="scope">
            <span :style="{ color: scope.row.total_profit >= 0 ? '#13ce66' : '#ff4949' }">
              {{ scope.row.total_profit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="total_rebate_amount"
          :label="t('transaction.liquidation.total_rebate_amount')"
          width="200"
        >
          <template #default="scope">
            <span :style="{ color: '#13ce66' }">{{ scope.row.total_rebate_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          :label="t('transaction.liquidation.remark')"
          width="200"
        >
          <template #default="scope">
            <span>
              {{ scope.row.remark }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="last_operation"
          :label="t('transaction.liquidation.last_operation')"
          width="200"
        >
          <template #default="scope">
            <span>
              {{ appDatetime(scope.row.last_operation) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-show="total > 0"
          :total="total"
          :page-size="listQuery.limit"
          :current-page="listQuery.page"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <el-tooltip
      placement="top"
      content="返回顶部"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import BackToTop from '@/components/BackToTop'
import { appDatetime } from '@/filters'
import {
  getLiquidationReport
} from '@/api/transaction'
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n-bridge'

const { t } = useI18n()
const listQuery = reactive({
  searchword: '',
  page: 1,
  limit: 10
})

const expandedRows = ref([])
const list = ref([])
const listLoading = ref(true)

const total = ref(0)

// Add actionMode for V1/V2 switch
const actionMode = ref('V1')

function handleExpand (row, expandedRowsArr) {
  expandedRows.value = expandedRowsArr.map(r => r.id)
}

function handleInnerExpand (parentRow, row, expandedRowsArr) {
  parentRow.expandedRows = expandedRowsArr.map(r => r.id)
}

function handleSizeChange (val) {
  listQuery.limit = val
  listQuery.page = 1
}

function handleCurrentChange (val) {
  listQuery.page = val
}

async function getListData () {
  listLoading.value = true
  getLiquidationReport().then(response => {
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    listLoading.value = false
  })
}

function handleAction (command, row) {
  // Implement your action logic here
  // For now, just log
  console.log('Action:', command, row)
}

onMounted(() => {
  getListData()
})
</script>

<style lang="scss" scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed;
}
.pagination-container {
  padding: 5px 2px;
}
.inner-table {
  background: #f9f9f9;
  margin: 0 0 0 0;
}
.level-indent {
  &.level-2 {
    margin-left: 30px;
  }
  &.level-3 {
    margin-left: 60px;
  }
}
.action-btns-v2 {
  display: flex;
  flex-direction: row;
  gap: 8px;
  justify-content: flex-start;
  align-items: center;
}
</style>
