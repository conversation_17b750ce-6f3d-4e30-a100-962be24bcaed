<template>
  <div>
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="form"
      :rules="validationRules"
      label-width="120px"
    >
      <el-form-item
        :label="$t('ipBlacklist.form_ip_address')"
        prop="ip_address"
      >
        <el-input
          v-model="form.ip_address"
          :placeholder="$t('ipBlacklist.form_ip_address_placeholder')"
          clearable
        />
      </el-form-item>

      <el-form-item
        :label="$t('ipBlacklist.form_reason')"
        prop="reason"
      >
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="3"
          :placeholder="$t('ipBlacklist.form_reason_placeholder')"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item
        :label="$t('ipBlacklist.form_status')"
        prop="status"
      >
        <el-radio-group v-model="form.status">
          <el-radio label="active">
            {{ $t('ipBlacklist.status_active') }}
          </el-radio>
          <el-radio label="inactive">
            {{ $t('ipBlacklist.status_inactive') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        :label="$t('ipBlacklist.form_expires_at')"
        prop="expires_at"
      >
        <el-date-picker
          v-model="form.expires_at"
          type="datetime"
          :placeholder="$t('ipBlacklist.form_expires_at_placeholder')"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
          clearable
        />
        <div class="form-help-text">
          {{ $t('ipBlacklist.form_expires_at_help') }}
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ $t('ipBlacklist.form_submit') }}
        </el-button>
        <el-button @click="handleCancel">
          {{ $t('ipBlacklist.form_cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ElMessage } from 'element-ui'
import { createIpBlacklist } from '@/api/ipBlacklist'

export default {
  name: 'IpBlacklistCreate',
  props: {
    item: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      loading: false,
      form: {
        ip_address: '',
        reason: '',
        status: 'active',
        expires_at: ''
      },
      rules: {}
    }
  },
  computed: {
    validationRules () {
      return {
        ip_address: [
          { required: true, message: this.$t('ipBlacklist.validation_ip_required'), trigger: 'blur' },
          {
            pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/,
            message: this.$t('ipBlacklist.validation_ip_format'),
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: this.$t('ipBlacklist.validation_status_required'), trigger: 'change' }
        ],
        expires_at: [
          {
            validator: (rule, value, callback) => {
              try {
                if (value && new Date(value) <= new Date()) {
                  callback(new Error(this.$t('ipBlacklist.validation_expires_future')))
                } else {
                  callback()
                }
              } catch (validationError) {
                console.error('Date validation error:', validationError)
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  mounted () {
    // Set rules after component is mounted to ensure $t is available
    this.rules = this.validationRules
  },
  methods: {

    async handleSubmit () {
      try {
        // Validate form with better error handling
        let valid = false
        try {
          valid = await this.$refs.formRef.validate()
        } catch (validationError) {
          console.error('Form validation error:', validationError)
          return
        }

        if (!valid) return

        this.loading = true

        const submitData = {
          ip_address: this.form.ip_address,
          reason: this.form.reason || undefined,
          status: this.form.status,
          expires_at: this.form.expires_at || undefined
        }

        // Remove undefined values
        Object.keys(submitData).forEach(key => {
          if (submitData[key] === undefined) {
            delete submitData[key]
          }
        })

        const response = await createIpBlacklist(submitData)

        this.$emit('success')
        this.resetForm()
      } catch (error) {
        console.error('Failed to create IP blacklist:')
      } finally {
        this.loading = false
      }
    },
    handleCancel () {
      this.resetForm()
      this.$emit('success') // Close dialog
    },
    resetForm () {
      this.form.ip_address = ''
      this.form.reason = ''
      this.form.status = 'active'
      this.form.expires_at = ''

      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
