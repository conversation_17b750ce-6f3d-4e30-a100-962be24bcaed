<template>
  <div class="app-container">
    <el-card
      class="box-card"
    >
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('ipBlacklist.page_title') }}</span>
      </div>

      <div class="filter-container">
        <el-input
          v-model="listQuery.ip_address"
          :placeholder="$t('ipBlacklist.search_ip_address')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-select
          v-model="listQuery.status"
          :placeholder="$t('ipBlacklist.search_status')"
          clearable
          class="filter-item"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.display_name"
            :value="item.key"
          />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          :placeholder="$t('ipBlacklist.search_date_range')"
          :start-placeholder="$t('ipBlacklist.start_date')"
          :end-placeholder="$t('ipBlacklist.end_date')"
          style="width: 240px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleDateRangeChange"
        />

        <el-select
          v-model="listQuery.sort_field"
          style="width: 140px;margin-right: 10px;"
          class="filter-item"
          @change="handleFilter"
        >
          <el-option
            v-for="item in sortOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          />
        </el-select>

        <el-select
          v-model="listQuery.sort_order"
          style="width: 100px;margin-right: 10px;"
          class="filter-item"
          @change="handleFilter"
        >
          <el-option
            v-for="item in orderOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          />
        </el-select>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('ipBlacklist.search_btn') }}
        </el-button>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="handleCreate"
        >
          {{ $t('ipBlacklist.create_btn') }}
        </el-button>

        <el-button
          :disabled="selectedIds.length === 0"
          class="filter-item"
          type="success"
          icon="el-icon-check"
          @click="handleBulkActivate"
        >
          {{ $t('ipBlacklist.bulk_activate') }}
        </el-button>

        <el-button
          :disabled="selectedIds.length === 0"
          class="filter-item"
          type="warning"
          icon="el-icon-close"
          @click="handleBulkDeactivate"
        >
          {{ $t('ipBlacklist.bulk_deactivate') }}
        </el-button>

        <el-button
          :disabled="selectedIds.length === 0"
          class="filter-item"
          type="danger"
          icon="el-icon-delete"
          @click="handleBulkDelete"
        >
          {{ $t('ipBlacklist.bulk_delete') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        />

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_id')"
          width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_ip_address')"
          min-width="150"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ip_address }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_reason')"
          min-width="200"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.reason || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_status')"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.status === 'active' ? 'success' : 'danger'"
              size="mini"
            >
              {{ scope.row.status === 'active' ? $t('ipBlacklist.status_active') : $t('ipBlacklist.status_inactive') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_effective')"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.is_effective ? 'success' : 'info'"
              size="mini"
            >
              {{ scope.row.is_effective ? $t('ipBlacklist.effective_yes') : $t('ipBlacklist.effective_no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_blocked_by')"
          min-width="150"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.blocked_by_admin ? scope.row.blocked_by_admin.nickname : '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_expires_at')"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.expires_at ? appDatetime(scope.row.expires_at) : $t('ipBlacklist.never_expires') }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_created_at')"
          width="180"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('ipBlacklist.table_actions')"
          width="120"
        >
          <template slot-scope="scope">
            <el-dropdown
              trigger="click"
              placement="bottom-end"
              @command="(command) => handleDropdownCommand(command, scope.$index, scope.row)"
            >
              <el-button
                type="text"
                size="small"
                class="action-dropdown-trigger"
              >
                <i
                  class="el-icon-more"
                  style="font-size: 16px; color: #606266;"
                />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="edit"
                  class="dropdown-item-edit"
                >
                  <i class="el-icon-edit" />
                  {{ $t('ipBlacklist.table_update') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  :disabled="scope.row.id == loading.delete"
                  class="dropdown-item-delete"
                >
                  <i class="el-icon-delete" />
                  <span v-if="scope.row.id == loading.delete">
                    <i class="el-icon-loading" />
                    {{ $t('ipBlacklist.table_delete') }}
                  </span>
                  <span v-else>
                    {{ $t('ipBlacklist.table_delete') }}
                  </span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.per_page"
        @pagination="getList"
      />
    </el-card>

    <!-- Detail Dialog -->
    <el-dialog
      :title="$t('ipBlacklist.dialog_detail')"
      :visible.sync="detail.dialogVisible"
      width="600px"
    >
      <detail :data="detail.data" />
    </el-dialog>

    <!-- Create Dialog -->
    <el-dialog
      :title="$t('ipBlacklist.dialog_create')"
      :visible.sync="create.dialogVisible"
      width="600px"
    >
      <create
        :item="create"
        @success="handleCreateSuccess"
      />
    </el-dialog>

    <!-- Edit Dialog -->
    <el-dialog
      :title="$t('ipBlacklist.dialog_update')"
      :visible.sync="edit.dialogVisible"
      width="600px"
      @close="closeEdit"
    >
      <edit
        :item="edit"
        @success="handleEditSuccess"
      />
    </el-dialog>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <back-to-top
        :custom-style="backToTopStyle"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script>
import { MessageBox, Message } from 'element-ui'
const ElMessageBox = MessageBox
const ElMessage = Message
import { parseTime } from '@/utils'
import { appDatetime } from '@/filters'
import Pagination from '@/components/Pagination'
import BackToTop from '@/components/BackToTop'
import checkPermission from '@/utils/permission'
import Detail from '@/components/Larke/Detail'
import Create from './components/Create.vue'
import Edit from './components/Edit.vue'
import {
  getIpBlacklistList,
  deleteIpBlacklist,
  bulkUpdateIpBlacklist
} from '@/api/ipBlacklist'

export default {
  name: 'IpBlacklistIndex',
  components: {
    Pagination,
    BackToTop,
    Detail,
    Create,
    Edit
  },
  data () {
    return {
      list: [],
      total: 0,
      listLoading: true,
      selectedIds: [],
      dateRange: [],
      listQuery: {
        ip_address: '',
        status: '',
        date_from: '',
        date_to: '',
        sort_field: 'created_at',
        sort_order: 'desc',
        page: 1,
        per_page: 10
      },
      loading: {
        detail: '',
        delete: ''
      },
      detail: {
        dialogVisible: false,
        data: {}
      },
      create: {
        dialogVisible: false
      },
      edit: {
        dialogVisible: false,
        id: ''
      },
      backToTopStyle: {
        right: '50px',
        bottom: '50px',
        width: '40px',
        height: '40px',
        'border-radius': '4px',
        'line-height': '45px',
        background: '#e7eaf1'
      }
    }
  },
  computed: {
    statusOptions () {
      return [
        { key: 'active', display_name: this.$t('ipBlacklist.status_active') },
        { key: 'inactive', display_name: this.$t('ipBlacklist.status_inactive') }
      ]
    },
    sortOptions () {
      return [
        { key: 'id', label: this.$t('ipBlacklist.sort_id') },
        { key: 'ip_address', label: this.$t('ipBlacklist.sort_ip_address') },
        { key: 'status', label: this.$t('ipBlacklist.sort_status') },
        { key: 'expires_at', label: this.$t('ipBlacklist.sort_expires_at') },
        { key: 'created_at', label: this.$t('ipBlacklist.sort_created_at') }
      ]
    },
    orderOptions () {
      return [
        { key: 'asc', label: this.$t('ipBlacklist.order_asc') },
        { key: 'desc', label: this.$t('ipBlacklist.order_desc') }
      ]
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    checkPermission,
    parseTime,
    appDatetime,

    async getList () {
      this.listLoading = true
      try {
        const params = {
          ip_address: this.listQuery.ip_address,
          status: this.listQuery.status,
          date_from: this.listQuery.date_from,
          date_to: this.listQuery.date_to,
          sort_field: this.listQuery.sort_field,
          sort_order: this.listQuery.sort_order,
          per_page: this.listQuery.per_page,
          page: this.listQuery.page
        }

        // Remove empty parameters
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })

        const response = await getIpBlacklistList(params)
        console.log('List response:', response) // Debug log

        // Handle different response structures
        this.list = response.data?.ip_blacklists || response.ip_blacklists || []
        this.total = response.data?.pagination?.total || response.pagination?.total || 0
      } catch (error) {
        console.error('Failed to fetch IP blacklist:', error)
        // Handle different error response structures
        let errorMessage = this.$t('ipBlacklist.fetch_error')
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        } else if (typeof error === 'string') {
          errorMessage = error
        }
        ElMessage.error(errorMessage)
      } finally {
        this.listLoading = false
      }
    },

    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleDateRangeChange (dates) {
      if (dates && dates.length === 2) {
        this.listQuery.date_from = dates[0]
        this.listQuery.date_to = dates[1]
      } else {
        this.listQuery.date_from = ''
        this.listQuery.date_to = ''
      }
    },
    handleSelectionChange (selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    handleCreate () {
      this.create.dialogVisible = true
    },
    handleCreateSuccess () {
      this.create.dialogVisible = false
      this.getList()
    },
    handleEdit (index, row) {
      this.edit.dialogVisible = true
      this.edit.id = row.id
    },
    handleEditSuccess () {
      this.edit.dialogVisible = false
      this.getList()
    },
    closeEdit () {
      this.edit.id = ''
    },

    handleDropdownCommand (command, index, row) {
      switch (command) {
        case 'detail':
          this.handleDetail(index, row)
          break
        case 'edit':
          this.handleEdit(index, row)
          break
        case 'delete':
          this.handleDelete(index, row)
          break
        default:
          console.warn('Unknown dropdown command:', command)
      }
    },

    async handleDetail (index, row) {
      this.loading.detail = row.id
      try {
        // For detail, we can use the row data directly or fetch fresh data
        this.detail.data = row
        this.detail.dialogVisible = true
      } catch (error) {
        console.error('Failed to fetch detail:', error)
        // Handle different error response structures
        let errorMessage = this.$t('ipBlacklist.detail_error')
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        } else if (typeof error === 'string') {
          errorMessage = error
        }
        ElMessage.error(errorMessage)
      } finally {
        this.loading.detail = ''
      }
    },

    async handleDelete (index, row) {
      try {
        await ElMessageBox.confirm(
          this.$t('ipBlacklist.delete_confirm_message', { ip: row.ip_address }),
          this.$t('ipBlacklist.delete_confirm_title'),
          {
            confirmButtonText: this.$t('ipBlacklist.confirm'),
            cancelButtonText: this.$t('ipBlacklist.cancel'),
            type: 'warning'
          }
        )

        this.loading.delete = row.id
        const response = await deleteIpBlacklist(row.id)
        console.log('Delete response:', response) // Debug log

        ElMessage.success(this.$t('ipBlacklist.delete_success'))
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to delete IP blacklist:', error)
          // Handle different error response structures
          let errorMessage = this.$t('ipBlacklist.delete_error')
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          } else if (typeof error === 'string') {
            errorMessage = error
          }
          ElMessage.error(errorMessage)
        }
      } finally {
        this.loading.delete = ''
      }
    },

    async handleBulkActivate () {
      try {
        await ElMessageBox.confirm(
          this.$t('ipBlacklist.bulk_activate_confirm', { count: this.selectedIds.length }),
          this.$t('ipBlacklist.bulk_operation_title'),
          {
            confirmButtonText: this.$t('ipBlacklist.confirm'),
            cancelButtonText: this.$t('ipBlacklist.cancel'),
            type: 'warning'
          }
        )

        const response = await bulkUpdateIpBlacklist(this.selectedIds, 'activate', 'active')
        console.log('Bulk activate response:', response) // Debug log

        // Handle different response structures
        const results = response.results || response.data?.results || { success: this.selectedIds.length, failed: 0 }

        ElMessage.success(this.$t('ipBlacklist.bulk_activate_success', {
          success: results.success || this.selectedIds.length,
          failed: results.failed || 0
        }))

        if (results.failed && results.failed > 0) {
          console.warn('Some operations failed:', results.errors || 'No error details available')
        }

        this.getList()
        this.selectedIds = []
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to bulk activate:', error)
          // Handle different error response structures
          let errorMessage = this.$t('ipBlacklist.bulk_activate_error')
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          } else if (typeof error === 'string') {
            errorMessage = error
          }
          ElMessage.error(errorMessage)
        }
      }
    },

    async handleBulkDeactivate () {
      try {
        await ElMessageBox.confirm(
          this.$t('ipBlacklist.bulk_deactivate_confirm', { count: this.selectedIds.length }),
          this.$t('ipBlacklist.bulk_operation_title'),
          {
            confirmButtonText: this.$t('ipBlacklist.confirm'),
            cancelButtonText: this.$t('ipBlacklist.cancel'),
            type: 'warning'
          }
        )

        const response = await bulkUpdateIpBlacklist(this.selectedIds, 'deactivate', 'inactive')
        console.log('Bulk deactivate response:', response) // Debug log

        // Handle different response structures
        const results = response.results || response.data?.results || { success: this.selectedIds.length, failed: 0 }

        ElMessage.success(this.$t('ipBlacklist.bulk_deactivate_success', {
          success: results.success || this.selectedIds.length,
          failed: results.failed || 0
        }))

        if (results.failed && results.failed > 0) {
          console.warn('Some operations failed:', results.errors || 'No error details available')
        }

        this.getList()
        this.selectedIds = []
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to bulk deactivate:', error)
          // Handle different error response structures
          let errorMessage = this.$t('ipBlacklist.bulk_deactivate_error')
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          } else if (typeof error === 'string') {
            errorMessage = error
          }
          ElMessage.error(errorMessage)
        }
      }
    },
    async handleBulkDelete () {
      try {
        await ElMessageBox.confirm(
          this.$t('ipBlacklist.bulk_delete_confirm', { count: this.selectedIds.length }),
          this.$t('ipBlacklist.bulk_operation_title'),
          {
            confirmButtonText: this.$t('ipBlacklist.confirm'),
            cancelButtonText: this.$t('ipBlacklist.cancel'),
            type: 'warning'
          }
        )

        const response = await bulkUpdateIpBlacklist(this.selectedIds, 'delete')
        console.log('Bulk delete response:', response) // Debug log

        // Handle different response structures
        const results = response.results || response.data?.results || { success: this.selectedIds.length, failed: 0 }

        ElMessage.success(this.$t('ipBlacklist.bulk_delete_success', {
          success: results.success || this.selectedIds.length,
          failed: results.failed || 0
        }))

        if (results.failed && results.failed > 0) {
          console.warn('Some operations failed:', results.errors || 'No error details available')
        }

        this.getList()
        this.selectedIds = []
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Failed to bulk delete:', error)
          // Handle different error response structures
          let errorMessage = this.$t('ipBlacklist.bulk_delete_error')
          if (error.response?.data?.message) {
            errorMessage = error.response.data.message
          } else if (error.message) {
            errorMessage = error.message
          } else if (typeof error === 'string') {
            errorMessage = error
          }
          ElMessage.error(errorMessage)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.box-card {
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both;
  }
}

/* Dropdown action button styles */
.action-dropdown-trigger {
  border: 1px solid transparent;
  border-radius: 4px;
  padding: 8px 12px;
  transition: all 0.3s;
}

.action-dropdown-trigger:hover {
  border-color: #c0c4cc;
  background-color: #f5f7fa;
}

.dropdown-item-detail i {
  color: #909399;
  margin-right: 8px;
}

.dropdown-item-edit i {
  color: #409eff;
  margin-right: 8px;
}

.dropdown-item-delete i {
  color: #f56c6c;
  margin-right: 8px;
}

/* Ensure dropdown items have proper spacing */
:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  line-height: 1.5;
}

:deep(.el-dropdown-menu__item i) {
  width: 16px;
  text-align: center;
}
</style>
