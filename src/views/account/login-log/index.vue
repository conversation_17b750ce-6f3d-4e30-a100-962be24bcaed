<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('player_logs.header_title') }}</span>
      </div>

      <div class="filter-container">
        <el-input
          v-model="listQuery.username"
          :placeholder="$t('player_logs.username_placeholder')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-input
          v-model="listQuery.ip_address"
          :placeholder="$t('player_logs.ip_address_placeholder')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-select
          v-model="listQuery.type"
          :placeholder="$t('player_logs.type_placeholder')"
          clearable
          class="filter-item"
          style="width: 150px;margin-right: 10px;"
        >
          <el-option
            v-for="logType in logTypes"
            :key="logType"
            :label="$t(`player_logs.types.${logType}`)"
            :value="logType"
          />
        </el-select>

        <el-date-picker
          v-model="listQuery.from_date"
          type="date"
          :placeholder="$t('player_logs.from_date_placeholder')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />

        <el-date-picker
          v-model="listQuery.to_date"
          type="date"
          :placeholder="$t('player_logs.to_date_placeholder')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>

        <el-button
          class="filter-item"
          type="default"
          icon="el-icon-refresh"
          @click="handleReset"
        >
          {{ $t('player_logs.reset_btn') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :data="list"
        class="border-gray"
        fit
        highlight-current-row
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          :label="$t('player_logs.username')"
          prop="username"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.username }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('player_logs.type')"
          prop="type"
          sortable="custom"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getLogTypeTagType(scope.row.type)"
              size="small"
            >
              {{ $t(`player_logs.types.${scope.row.type}`) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('player_logs.ip_address')"
          prop="ip_address"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ip_address }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('player_logs.ip_area')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ip_area || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="left"
          :label="$t('admin.table_create_time')"
          prop="created_at"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.created_at) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.per_page"
        @pagination="getListData"
      />
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import BackToTop from '@/components/BackToTop'
import { getPlayerLogs, getPlayerLogTypes } from '@/api/playerLogs'
import { appDatetime } from '@/filters'
import { Message } from 'element-ui'

const ElMessage = Message

export default {
  name: 'PlayerLogs',
  components: {
    Pagination,
    BackToTop
  },
  data () {
    return {
      list: [],
      total: 0,
      listLoading: true,
      logTypes: [],
      listQuery: {
        username: '',
        ip_address: '',
        type: '',
        from_date: '',
        to_date: '',
        sort_field: 'created_at',
        sort_order: 'desc',
        page: 1,
        per_page: 15
      }
    }
  },
  mounted () {
    this.getLogTypesData()
    this.getListData()
  },
  methods: {
    appDatetime,
    async getListData () {
      this.listLoading = true

      try {
        const params = {
          page: this.listQuery.page,
          per_page: this.listQuery.per_page,
          sort_field: this.listQuery.sort_field,
          sort_order: this.listQuery.sort_order
        }

        // Add filter parameters if they exist
        if (this.listQuery.username) {
          params.username = this.listQuery.username
        }
        if (this.listQuery.ip_address) {
          params.ip_address = this.listQuery.ip_address
        }
        if (this.listQuery.type) {
          params.type = this.listQuery.type
        }
        if (this.listQuery.from_date) {
          params.from_date = this.listQuery.from_date
        }
        if (this.listQuery.to_date) {
          params.to_date = this.listQuery.to_date
        }

        const response = await getPlayerLogs(params)
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      } catch (error) {
        console.error('Error fetching player logs:', error)
        ElMessage({
          message: error.response?.data?.message || this.$t('player_logs.fetch_error'),
          type: 'error',
          duration: 5000
        })
        this.listLoading = false
      }
    },

    async getLogTypesData () {
      try {
        const response = await getPlayerLogTypes()
        this.logTypes = response.data.types
      } catch (error) {
        console.error('Error fetching log types:', error)
        ElMessage({
          message: error.response?.data?.message || this.$t('player_logs.fetch_types_error'),
          type: 'error',
          duration: 5000
        })
      }
    },

    handleFilter () {
      this.listQuery.page = 1
      this.getListData()
    },

    handleReset () {
      this.listQuery.username = ''
      this.listQuery.ip_address = ''
      this.listQuery.type = ''
      this.listQuery.from_date = ''
      this.listQuery.to_date = ''
      this.listQuery.page = 1
      this.getListData()
    },

    handleSortChange ({ column, prop, order }) {
      if (order === 'ascending') {
        this.listQuery.sort_order = 'asc'
      } else if (order === 'descending') {
        this.listQuery.sort_order = 'desc'
      } else {
        this.listQuery.sort_order = 'desc'
      }
      this.listQuery.sort_field = prop
      this.listQuery.page = 1
      this.getListData()
    },

    getLogTypeTagType (type) {
      const typeMap = {
        'login': 'success',
        'logout': 'info',
        'change_password': 'warning',
        'profile_update': 'primary',
        'balance_update': 'success',
        'bet_placed': 'warning',
        'game_result': 'info',
        'withdrawal': 'danger',
        'deposit': 'success',
        'security_event': 'danger'
      }
      return typeMap[type] || 'info'
    }
  }
}
</script>

<style scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed; /* required for BackToTop to work correctly */
}

.filter-container {
  padding-bottom: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

.border-gray {
  border: 1px solid #dcdfe6;
}

.el-table .cell {
  word-break: break-word;
}

@media (max-width: 768px) {
  .filter-container .filter-item {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }

  .filter-container {
    text-align: left;
  }
}
</style>
