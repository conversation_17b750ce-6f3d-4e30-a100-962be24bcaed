<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.menu.accounts.operation_logs') }}</span>
      </div>

      <div class="filter-container">
        <el-date-picker
          v-model="listQuery.created_at_from"
          type="date"
          :placeholder="$t('operation_logs.created_at_from')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />

        <el-date-picker
          v-model="listQuery.created_at_to"
          type="date"
          :placeholder="$t('operation_logs.created_at_to')"
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :data="list"
        class="border-gray"
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          width="80px"
          label="ID"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('operation_logs.causer_id')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.causer.nickname }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('operation_logs.type')"
        >
          <template slot-scope="scope">
            <span>{{ $t('operation_logs.types.'+scope.row.log_name) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('operation_logs.event')"
        >
          <template slot-scope="scope">
            <span>{{ $t('operation_logs.events.'+scope.row.event) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('operation_logs.subject_type')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.subject_type |removeModelName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('operation_logs.subject_id')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.subject_id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          :label="$t('admin.table_create_time')"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.created_at) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getListData"
      />
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { getActivityLogs } from '@/api/admin'
import BackToTop from '@/components/BackToTop'
import Pagination from '@/components/Pagination'
import { appDatetime } from '@/filters'
import { Message } from 'element-ui'
import { onMounted, reactive, ref } from 'vue'
import '@/styles/back-to-top.scss'
const ElMessage = Message

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  log_name: '',
  created_at_from: '',
  created_at_to: '',
  page: 1,
  limit: 10
})

const getListData = () => {
  listLoading.value = true

  const params = {
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  }

  // Add filter parameters if they exist
  if (listQuery.log_name) {
    params.log_name = listQuery.log_name
  }
  if (listQuery.created_at_from) {
    params.created_at_from = listQuery.created_at_from
  }
  if (listQuery.created_at_to) {
    params.created_at_to = listQuery.created_at_to
  }

  getActivityLogs(params).then(response => {
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  }).catch(error => {
    console.error('Error fetching operation logs:', error)
    ElMessage({
      message: error.response?.data?.message || 'Failed to fetch operation logs',
      type: 'error',
      duration: 5000
    })
    listLoading.value = false
  })
}

onMounted(() => {
  getListData()
})

const handleFilter = () => {
  listQuery.page = 1
  getListData()
}
</script>

<style scoped>

</style>
