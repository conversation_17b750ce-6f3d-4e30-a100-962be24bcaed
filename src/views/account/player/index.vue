<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.menu.accounts.player_list') }}</span>
      </div>
      <div class="filter-container">
        <el-input
          v-model="listQuery.searchword"
          :placeholder="$t('account.player_list.username')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :data="list"
        class="border-gray"
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          label="ID"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('account.player_list.username')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.username }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('account.player_list.balance')"
        >
          <template slot-scope="scope">
            <span>{{ formatCurrency(scope.row.balance) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="left"
          :label="$t('account.player_list.created_time')"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.created_at) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getListData"
      />
    </el-card>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        :custom-style="backToTopStyle"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue'
import {
  getListPlayer
} from '@/api/account'
import Pagination from '@/components/Pagination'
import BackToTop from '@/components/BackToTop'
import { appDatetime } from '@/filters'
import { formatCurrency } from '@/utils'

const list = ref([])
const listLoading = ref(true)
const total = ref(0)
const listQuery = reactive({
  searchword: '',
  page: 1,
  limit: 10
})

const backToTopStyle = {
  right: '50px',
  bottom: '50px',
  width: '40px',
  height: '40px',
  'border-radius': '4px',
  'line-height': '45px',
  background: '#e7eaf1'
}

const handleFilter = () => {
  getListData()
}

const getListData = () => {
  listLoading.value = true
  getListPlayer({
    username: listQuery.searchword,
    pageSize: listQuery.limit
  }).then(response => {
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    listLoading.value = false
  })
}

onMounted(() => {
  getListData()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
