<template>
  <div class="components-container">
    <pan-thumb :image="data.avatar" />

    <el-button
      type="primary"
      icon="el-icon-upload"
      style="position: absolute;bottom: 15px;margin-left: 40px;"
      @click="imagecropperShow=true"
    >
      {{ $t('admin.select_avatar') }}
    </el-button>

    <image-cropper
      v-show="imagecropperShow"
      :key="imagecropperKey"
      :width="300"
      :height="300"
      field="file"
      url="upload/file"
      lang-type="zh"
      @close="close"
      @crop-upload-success="cropSuccess"
    />
  </div>
</template>

<script>
import ImageCropper from '@/components/ImageCropper'
import PanThumb from '@/components/PanThumb'

export default {
  name: 'AdminAvatar',
  components: {
    ImageCropper,
    PanThumb
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          avatar: '',
          avatarKey: ''
        }
      }
    }
  },
  data () {
    return {
      imagecropperShow: false,
      imagecropper<PERSON>ey: 0
    }
  },
  methods: {
    cropSuccess (resData) {
      this.imagecropperShow = false
      this.imagecropperKey = this.imagecropperKey + 1
      this.data.avatar = resData.url
      this.data.avatarKey = resData.id
    },
    close () {
      this.imagecropperShow = false
    }
  }
}
</script>

<style scoped>
  .avatar{
    width: 200px;
    height: 200px;
    border-radius: 50%;
  }
</style>

