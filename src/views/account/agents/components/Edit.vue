<template>
  <el-form
    v-loading="detailLoading"
    ref="formRef"
    :model="data"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item
      :label="$t('admin.form_passport')"
      prop="name"
    >
      <el-input
        v-model.trim="data.name"
        :placeholder="$t('admin.form_enter_passport')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('admin.form_nickname')"
      prop="nickname"
    >
      <el-input
        v-model.trim="data.nickname"
        :placeholder="$t('admin.form_enter_nickname')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('admin.form_email')"
      prop="email"
    >
      <el-input
        v-model.trim="data.email"
        :placeholder="$t('admin.form_enter_email')"
      />
    </el-form-item>

    <el-form-item :label="$t('admin.form_avatar')">
      <div style="width: 100%;">
        <avatar :data="data" />
      </div>
    </el-form-item>
    <el-form-item
      :label="$t('admin.form_status')"
      prop="status"
    >
      <el-radio-group v-model="data.status">
        <el-radio :label="1">
          {{ $t('admin.form_status_enable') }}
        </el-radio>
        <el-radio :label="0">
          {{ $t('admin.form_status_disable') }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="submit"
      >
        {{ $t('common.save') }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-ui'
import Avatar from './Avatar'
import { getDetail, updateAdmin } from '@/api/admin'
import { useI18n } from 'vue-i18n-bridge'

// Props
const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  }
})

// Refs
const formRef = ref(null)
const detailLoading = ref(false)
const submitLoading = ref(false)
const id = ref('')
const { t } = useI18n()
// Reactive data
const data = reactive({
  name: '',
  nickname: '',
  email: '',
  avatar: require('@/assets/larke/avatar-default.jpg'),
  avatarKey: '',
  status: 1
})

// Rules
const rules = reactive({
  name: [
    { required: true, message: t('admin.rules_name_required'), trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: t('admin.rules_nickname_required'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('admin.rules_email_required'), trigger: 'blur' }
  ]
})

// Methods
const fetchData = async (id) => {
  const defaultAvatar = require('@/assets/larke/avatar-default.jpg')
  detailLoading.value = true

  try {
    const response = await getDetail(id)
    Object.assign(data, response.data)
    data.avatar = data.avatar || defaultAvatar
  } catch (error) {
    console.error('Failed to fetch data:', error)
  } finally {
    detailLoading.value = false
  }
}

const submit = () => {
  submitLoading.value = true

  formRef.value?.validate(async (valid) => {
    if (!valid) {
      submitLoading.value = false
      return false
    }

    try {
      await updateAdmin(id.value, {
        name: data.name,
        nickname: data.nickname,
        email: data.email,
        avatar: data.avatarKey,
        introduce: '',
        status: data.status
      })

      ElMessage({
        message: t('admin.update_success'),
        type: 'success',
        duration: 5 * 1000,
        onClose () {
          if (formRef.value) {
            id.value = ''
            formRef.value.resetFields()
          }
          props.item.dialogVisible = false
        }
      })
    } catch (error) {
      console.error('Failed to update:', error)
    } finally {
      submitLoading.value = false
    }
  })
}

// Watch
watch(
  () => props.item,
  (newVal) => {
    if (newVal.dialogVisible === true && id.value !== newVal.id) {
      id.value = newVal.id
      fetchData(newVal.id)
    }
  },
  { deep: true }
)

// Lifecycle
onMounted(() => {
  if (props.item.id) {
    id.value = props.item.id
    fetchData(props.item.id)
  }
})
</script>
