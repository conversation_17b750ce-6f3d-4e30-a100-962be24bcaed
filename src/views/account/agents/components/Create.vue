<template>
  <el-form
    ref="formRef"
    :model="data"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item
      :label="$t('admin.form_passport')"
      prop="passport"
    >
      <el-input
        v-model.trim="data.passport"
        :placeholder="$t('admin.form_enter_passport')"
      />
    </el-form-item>

    <el-form-item
      :label="$t('admin.form_nickname')"
      prop="nickname"
    >
      <el-input
        v-model.trim="data.nickname"
        :placeholder="$t('admin.form_enter_nickname')"
      />
    </el-form-item>

    <el-form-item
      :label="$t('login.password')"
      prop="password"
    >
      <el-input
        v-model.trim="data.password"
        :placeholder="$t('profile.new_password_enter')"
      />
    </el-form-item>

    <el-form-item
      :label="$t('admin.form_email')"
      prop="email"
    >
      <el-input
        v-model.trim="data.email"
        :placeholder="$t('admin.form_enter_email')"
      />
    </el-form-item>

    <el-form-item :label="$t('admin.form_avatar')">
      <div style="width: 100%;">
        <Avatar :data="data" />
      </div>
    </el-form-item>

    <el-form-item
      :label="$t('admin.form_status')"
      prop="status"
    >
      <el-radio-group v-model="data.status">
        <el-radio :label="1">
          {{ $t('admin.form_status_enable') }}
        </el-radio>
        <el-radio :label="0">
          {{ $t('admin.form_status_disable') }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        :loading="loading"
        @click="submit"
      >
        {{ $t('common.save') }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import md5 from 'js-md5'
import { ref, reactive } from 'vue'
import Avatar from './Avatar.vue'
import { createAdmin } from '@/api/admin'
import { Message } from 'element-ui'
const ElMessage = Message
import { useI18n } from 'vue-i18n-bridge'

// Define props
const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  onSubmited: {
    type: Function
  }
})

// Get global properties

// Reactive data
const formRef = ref(null)
const loading = ref(false)
const { t } = useI18n()

const data = reactive({
  name: '',
  nickname: '',
  email: '',
  parent_id: '',
  avatar: '',
  avatarKey: '',
  status: 1
})

const rules = reactive({
  name: [
    { required: true, message: t('admin.rules_name_required'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.rules_password_required'), trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: t('admin.rules_nickname_required'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('admin.rules_email_required'), trigger: 'blur' }
  ]
})

const submit = () => {
  loading.value = true

  formRef.value?.validate(async (valid) => {
    if (!valid) {
      loading.value = false
      return false
    }

    try {
      const payload = {
        name: data.nickname,
        password: md5(data.password),
        nickname: data.nickname,
        email: data.email,
        parent_id: props.item.id,
        avatar: data.avatarKey,
        introduce: '',
        status: data.status
      }
      if (!props.item.id) {
        delete payload.parent_id
      }
      await createAdmin(payload, props.item.forAgent)

      loading.value = false

      ElMessage({
        message: t('admin.create_success'),
        type: 'success',
        duration: 1 * 1000,
        onClose () {
          formRef.value?.resetFields()
          props.onSubmited()
        }
      })
    } catch (error) {
      loading.value = false
      console.error('Failed to create admin:', error)
    }
  })
}
</script>
