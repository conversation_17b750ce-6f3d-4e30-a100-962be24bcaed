<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('admin.search_title') }}</span>
      </div>

      <div class="filter-container">
        <el-input
          v-model="listQuery.searchword"
          :placeholder="$t('admin.search_searchword')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-select
          v-model="listQuery.status"
          :placeholder="$t('admin.search_status')"
          clearable
          class="filter-item"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.display_name"
            :value="item.key"
          />
        </el-select>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          :disabled="user.type !== 'general'"
          @click="handleCreate(null)"
        >
          {{ $t('admin.search_create_passport') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :data="list"
        class="border-gray"
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          width="150px"
          :label="$t('admin.table_name')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('admin.table_nickname')"
        >
          <template slot-scope="{row}">
            <span>{{ row.nickname }}</span>
          </template>
        </el-table-column>

        <el-table-column
          width="180px"
          align="left"
          :label="$t('admin.table_create_time')"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.create_time) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          class-name="status-col"
          :label="$t('admin.table_status')"
          width="80"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              :disabled="!checkPermission(['larke-admin.admin.enable']) || !checkPermission(['larke-admin.admin.disable'])"
              @change="changeStatus($event, scope.row, scope.$index)"
            />
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('admin.table_actions')"
          width="120"
        >
          <template slot-scope="scope">
            <el-dropdown
              trigger="click"
              placement="bottom-end"
              @command="(command) => handleDropdownCommand(command, scope.$index, scope.row)"
            >
              <el-button
                type="text"
                size="small"
                class="action-dropdown-trigger"
              >
                <i
                  class="el-icon-more"
                  style="font-size: 16px; color: #606266;"
                />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="password"
                  :disabled="!checkPermission(['larke-admin.admin.password'])"
                  class="dropdown-item-password"
                >
                  <i class="el-icon-key" />
                  {{ $t('admin.table_password') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="edit"
                  :disabled="!checkPermission(['larke-admin.admin.update'])"
                  class="dropdown-item-edit"
                >
                  <i class="el-icon-edit" />
                  {{ $t('admin.table_update') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  :disabled="!checkPermission(['larke-admin.admin.delete']) || scope.row.id == loading.delete"
                  class="dropdown-item-delete"
                >
                  <i class="el-icon-delete" />
                  <span v-if="scope.row.id == loading.delete">
                    <i class="el-icon-loading" />
                    {{ $t('admin.table_delete') }}
                  </span>
                  <span v-else>
                    {{ $t('admin.table_delete') }}
                  </span>
                </el-dropdown-item>
                <el-dropdown-item
                  command="createLocal"
                  class="dropdown-item-create"
                >
                  <i class="el-icon-plus" />
                  {{ $t('admin.create_local') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="rebateSetting"
                  class="dropdown-item-rebate"
                >
                  <i class="el-icon-money" />
                  {{ $t('admin.rebate_setting') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getListData"
      />
    </el-card>

    <el-dialog
      :title="$t('admin.dialog_create')"
      :visible.sync="create.dialogVisible"
    >
      <Create
        :item="create"
        :on-submited="handleCloseModal"
      />
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_edit')"
      :visible.sync="edit.dialogVisible"
    >
      <Edit
        :item="edit"
      />
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_password')"
      :visible.sync="password.dialogVisible"
    >
      <el-form>
        <el-form-item :label="$t('admin.dialog_password_newpassword')">
          <el-input
            v-model="password.newpassword"
            type="password"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading.password"
            @click="changePassword"
          >
            {{ $t('common.ok') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_access')"
      :visible.sync="access.dialogVisible"
    >
      <access :item="access" />
    </el-dialog>

    <el-dialog
      :title="$t('admin.rebate_setting')"
      :visible.sync="rebate.dialogVisible"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="rebateFormRef"
        :model="rebate.form"
        :rules="rebate.rules"
        label-width="120px"
      >
        <div class="rebate-tiers-container">
          <div class="rebate-tier-header">
            <h4>{{ $t('admin.rebate_configuration') }}</h4>
          </div>

          <div
            v-for="(tier, index) in rebate.form.rebates"
            :key="tier.id || `new-${index}`"
            class="rebate-tier-item"
          >
            <div class="tier-header">
              <div class="tier-actions">
                <span
                  v-if="tier.currency"
                  class="tier-currency"
                >{{ tier.currency }}</span>
              </div>
            </div>

            <el-row :gutter="20">
              <el-col :span="tier.range_max !== null ? 8 : 12">
                <el-form-item
                  :label="$t('admin.range_min')"
                  :prop="`rebates.${index}.range_min`"
                  :rules="rebate.rules.range_min"
                >
                  <el-input
                    v-model="tier.range_min"
                    style="width: 100%"
                    placeholder="0.00"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="tier.range_max !== null"
                :span="8"
              >
                <el-form-item
                  :label="$t('admin.range_max')"
                  :prop="`rebates.${index}.range_max`"
                  :rules="rebate.rules.range_max"
                >
                  <el-input
                    v-model="tier.range_max"
                    style="width: 100%"
                    placeholder="0.00"
                  />
                  <div class="field-hint">
                    {{ $t('admin.range_max_hint') }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="tier.range_max !== null ? 8 : 12">
                <el-form-item
                  :label="$t('admin.rate')"
                  :prop="`rebates.${index}.rate`"
                  :rules="rebate.rules.rate"
                >
                  <el-input
                    v-model="tier.rate"
                    style="width: 100%"
                    placeholder="0.90"
                  />
                  <div class="field-hint">
                    {{ $t('admin.rate_hint') }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <el-form-item style="margin-top: 30px;">
          <el-button
            type="primary"
            :loading="loading.rebate"
            @click="saveRebateSettings"
          >
            {{ $t('common.save') }}
          </el-button>
          <el-button @click="rebate.dialogVisible = false">
            {{ $t('common.cancel') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue'
import md5 from 'js-md5'
import Pagination from '@/components/Pagination'
import checkPermission from '@/utils/permission'
import BackToTop from '@/components/BackToTop'

import { MessageBox, Message } from 'element-ui'
const ElMessageBox = MessageBox
const ElMessage = Message
import Edit from '../components/Edit'
import Create from '../components/Create'
import {
  getListAgent as getListAgentApi,
  deleteAdmin,
  enableAdmin,
  disableAdmin,
  updatePassword,
  getRegionalRebates,
  updateRegionalRebates
} from '@/api/admin'
import { useI18n } from 'vue-i18n-bridge'
import { appDatetime } from '@/filters'

const { t } = useI18n()

const { proxy } = getCurrentInstance()
const user = computed(() => proxy.$store.state.user)

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  searchword: '',
  order: 'create_time__ASC',
  status: '',
  page: 1,
  limit: 10
})

const statusOptions = [
  { key: 'open', display_name: t('admin.table_enable') },
  { key: 'close', display_name: t('admin.table_disable') }
]

const sortOptions = [
  { key: 'create_time__ASC', label: t('admin.table_asc') },
  { key: 'create_time__DESC', label: t('admin.table_desc') }
]

const create = reactive({ dialogVisible: false, id: '', forAgent: 'regional-agent' })
const edit = reactive({ dialogVisible: false, id: '' })
const access = reactive({ id: '', name: '', dialogVisible: false })
const password = reactive({ dialogVisible: false, id: '', newpassword: '' })
const rebate = reactive({
  dialogVisible: false,
  regionalId: '',
  form: {
    rebates: [
      {
        id: null,
        range_min: '0',
        range_max: null,
        rate: '0.90',
        currency: 'USD'
      }
    ]
  },
  rules: {
    range_min: [
      { required: true, message: () => t('admin.range_min_required'), trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error(t('admin.range_min_required')))
            return
          }
          const num = parseFloat(value)
          if (isNaN(num) || num < 0) {
            callback(new Error(t('admin.range_min_validation')))
            return
          }
          callback()
        },
        trigger: 'blur'
      }
    ],
    range_max: [
      {
        validator: (rule, value, callback) => {
          if (value === '' || value === null || value === undefined) {
            callback() // range_max is optional
            return
          }
          const num = parseFloat(value)
          if (isNaN(num) || num < 0) {
            callback(new Error(t('admin.range_max_validation')))
            return
          }
          callback()
        },
        trigger: 'blur'
      }
    ],
    rate: [
      { required: true, message: () => t('admin.rate_required'), trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error(t('admin.rate_required')))
            return
          }
          const num = parseFloat(value)
          if (isNaN(num) || num < 0.01 || num > 1) {
            callback(new Error(t('admin.rate_validation')))
            return
          }
          callback()
        },
        trigger: 'blur'
      }
    ]
  }
})
const loading = reactive({ detail: '', delete: '', password: false, rebate: false })

const getListData = () => {
  listLoading.value = true
  getListAgentApi({
    searchword: listQuery.searchword,
    order: listQuery.order,
    status: listQuery.status,
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  }, `regional-agents`).then(response => {
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    listLoading.value = false
  })
}

onMounted(() => {
  getListData()
})

const handleCloseModal = () => {
  // reset modal and refresh data
  create.dialogVisible = false
  create.forAgent = 'regional-agent'
  create.id = ''

  getListData()
}

const handleFilter = () => {
  listQuery.page = 1
  getListData()
}

const handleCreate = (id) => {
  create.id = id
  if (id) {
    create.forAgent = 'local-agent'
  }
  create.dialogVisible = true
}

const handleEdit = (index, row) => {
  edit.dialogVisible = true
  edit.id = row.id
}

const changeStatus = (e, data, index) => {
  const fn = data.status == 1 ? enableAdmin : disableAdmin
  fn(data.id).then(() => {
    ElMessage({
      message: data.status == 1 ? t('admin.table_enable_success') : t('admin.table_disable_success'),
      type: 'success',
      duration: 2000
    })
  }).catch(error => {
    console.error('Error changing status:', error)
  })
}

const handleDelete = (index, row) => {
  ElMessageBox.confirm(t('admin.confirm_delete'), t('common.tips'), {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  }).then(() => {
    loading.delete = row.id
    deleteAdmin(row.id).then(() => {
      loading.delete = ''
      ElMessage({
        message: t('admin.confirm_delete_success'),
        type: 'success',
        duration: 5000,
        onClose: () => {
          list.value.splice(index, 1)
        }
      })
    }).catch(() => {
      loading.delete = ''
    })
  }).catch(() => {
    // User cancelled
  })
}

const handlePassword = (index, row) => {
  password.dialogVisible = true
  password.id = row.id
}

const changePassword = () => {
  loading.password = true
  if (password.newpassword === '') {
    ElMessage({
      message: t('admin.password_not_empty'),
      type: 'error',
      duration: 5000
    })
    loading.password = false
    return
  }
  updatePassword(password.id, { password: md5(password.newpassword) }).then(() => {
    loading.password = false
    ElMessage({
      message: t('admin.password_change_success'),
      type: 'success',
      duration: 5000,
      onClose: () => {
        password.newpassword = ''
        password.dialogVisible = false
      }
    })
  }).catch(() => {
    loading.password = false
  })
}

const rebateFormRef = ref(null)

const handleDropdownCommand = (command, index, row) => {
  switch (command) {
    case 'password':
      handlePassword(index, row)
      break
    case 'edit':
      handleEdit(index, row)
      break
    case 'delete':
      handleDelete(index, row)
      break
    case 'createLocal':
      handleCreate(row.id)
      break
    case 'rebateSetting':
      handleRebateSetting(index, row)
      break
    default:
      console.warn('Unknown dropdown command:', command)
  }
}

const handleRebateSetting = async (index, row) => {
  rebate.regionalId = row.id
  rebate.dialogVisible = true
  loading.rebate = true

  try {
    const response = await getRegionalRebates(row.id)
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      // Map all rebate tiers from API response
      rebate.form.rebates = response.data.map(tier => ({
        id: tier.id,
        range_min: tier.range_min ? tier.range_min.toString() : '0',
        range_max: tier.range_max ? tier.range_max.toString() : null,
        rate: tier.rate ? tier.rate.toString() : '0.90',
        currency: tier.currency || 'USD'
      }))
    } else {
      // Reset to default single tier if no data
      rebate.form.rebates = [
        {
          id: null,
          range_min: '0',
          range_max: null,
          rate: '0.90',
          currency: 'USD'
        }
      ]
    }
  } catch (error) {
    console.error('Error fetching rebate data:', error)

    // Extract server error message or use fallback
    let errorMessage = t('admin.rebate_load_error') // Default fallback message

    if (error.response && error.response.data) {
      // Try different possible paths for the error message
      if (error.response.data.message) {
        errorMessage = error.response.data.message
      } else if (error.response.data.error) {
        errorMessage = error.response.data.error
      } else if (error.response.data.data && error.response.data.data.message) {
        errorMessage = error.response.data.data.message
      } else if (typeof error.response.data === 'string') {
        errorMessage = error.response.data
      }
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 5000 // Increased duration for potentially longer server messages
    })
  } finally {
    loading.rebate = false
  }
}

const saveRebateSettings = () => {
  rebateFormRef.value?.validate(async (valid) => {
    if (!valid) {
      return false
    }

    loading.rebate = true

    try {
      // Prepare rebate data with proper structure for API
      const rebateData = {
        rebates: rebate.form.rebates.map(tier => {
          const tierData = {
            range_min: parseFloat(tier.range_min) || 0,
            range_max: tier.range_max ? parseFloat(tier.range_max) : null,
            rate: parseFloat(tier.rate) || 0,
            currency: tier.currency || 'USD'
          }

          // Include id for existing records
          if (tier.id) {
            tierData.id = tier.id
          }

          return tierData
        })
      }

      await updateRegionalRebates(rebate.regionalId, rebateData)

      ElMessage({
        message: t('admin.rebate_update_success'),
        type: 'success',
        duration: 3000
      })

      rebate.dialogVisible = false
    } catch (error) {
      console.error('Error updating rebate settings:', error)

      // Extract server error message or use fallback
      let errorMessage = t('admin.rebate_update_error') // Default fallback message

      if (error.response && error.response.data) {
        // Try different possible paths for the error message
        if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        } else if (error.response.data.data && error.response.data.data.message) {
          errorMessage = error.response.data.data.message
        } else if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        }
      } else if (error.message) {
        errorMessage = error.message
      }

      ElMessage({
        message: errorMessage,
        type: 'error',
        duration: 5000 // Increased duration for potentially longer server messages
      })
    } finally {
      loading.rebate = false
    }
  })
}
</script>

<style scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed; /* required for BackToTop to work correctly */
}
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

/* Action dropdown styles */
.action-dropdown-trigger {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.action-dropdown-trigger:hover {
  border-color: #c0c4cc;
  background-color: #f5f7fa;
}

.dropdown-item-password i {
  color: #e6a23c;
  margin-right: 8px;
}

.dropdown-item-edit i {
  color: #409eff;
  margin-right: 8px;
}

.dropdown-item-delete i {
  color: #f56c6c;
  margin-right: 8px;
}

.dropdown-item-create i {
  color: #67c23a;
  margin-right: 8px;
}

.dropdown-item-rebate i {
  color: #f39c12;
  margin-right: 8px;
}

/* Ensure dropdown items have proper spacing */
.el-dropdown-menu__item {
  padding: 8px 16px;
  line-height: 1.5;
}

.el-dropdown-menu__item i {
  width: 16px;
  text-align: center;
}

/* Rebate form styles */
.rebate-tiers-container {
  max-height: 500px;
  overflow-y: auto;
}

.rebate-tier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.rebate-tier-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.rebate-tier-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

.tier-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
}

.tier-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.tier-currency {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}
</style>
