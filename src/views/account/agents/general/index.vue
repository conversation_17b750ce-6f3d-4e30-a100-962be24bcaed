<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('admin.search_title') }}</span>
      </div>

      <div class="filter-container">
        <el-input
          v-model="listQuery.searchword"
          :placeholder="$t('admin.search_searchword')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-select
          v-model="listQuery.status"
          :placeholder="$t('admin.search_status')"
          clearable
          class="filter-item"
          style="width: 130px;margin-right: 10px;"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.display_name"
            :value="item.key"
          />
        </el-select>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="handleCreate(null)"
        >
          {{ $t('admin.search_create_passport') }}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading"
        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
        :data="list"
        class="border-gray"
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          width="150px"
          :label="$t('admin.table_name')"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          min-width="100px"
          :label="$t('admin.table_nickname')"
        >
          <template slot-scope="{row}">
            <span>{{ row.nickname }}</span>
          </template>
        </el-table-column>

        <el-table-column
          width="180px"
          align="left"
          :label="$t('admin.table_create_time')"
        >
          <template slot-scope="scope">
            <span>
              <i class="el-icon-time" />&nbsp;
              {{ appDatetime(scope.row.create_time) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          class-name="status-col"
          :label="$t('admin.table_status')"
          width="80"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="0"
              :disabled="!checkPermission(['larke-admin.admin.enable']) || !checkPermission(['larke-admin.admin.disable'])"
              @change="changeStatus($event, scope.row, scope.$index)"
            />
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('admin.table_actions')"
          width="120"
        >
          <template slot-scope="scope">
            <el-dropdown
              trigger="click"
              placement="bottom-end"
              @command="(command) => handleDropdownCommand(command, scope.$index, scope.row)"
            >
              <el-button
                type="text"
                size="small"
                class="action-dropdown-trigger"
              >
                <i
                  class="el-icon-more"
                  style="font-size: 16px; color: #606266;"
                />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="password"
                  :disabled="!checkPermission(['larke-admin.admin.password'])"
                  class="dropdown-item-password"
                >
                  <i class="el-icon-key" />
                  {{ $t('admin.table_password') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="edit"
                  :disabled="!checkPermission(['larke-admin.admin.update'])"
                  class="dropdown-item-edit"
                >
                  <i class="el-icon-edit" />
                  {{ $t('admin.table_update') }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  :disabled="!checkPermission(['larke-admin.admin.delete']) || scope.row.id == loading.delete"
                  class="dropdown-item-delete"
                >
                  <i class="el-icon-delete" />
                  <span v-if="scope.row.id == loading.delete">
                    {{ $t('admin.table_delete') }}
                  </span>
                  <span v-else>
                    {{ $t('admin.table_delete') }}
                  </span>
                </el-dropdown-item>
                <el-dropdown-item
                  command="createRegional"
                  class="dropdown-item-create"
                >
                  <i class="el-icon-plus" />
                  {{ $t('admin.create_regional') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getListData"
      />
    </el-card>

    <el-dialog
      :title="$t('admin.dialog_create')"
      :visible.sync="create.dialogVisible"
    >
      <Create
        :item="create"
        :on-submited="handleCloseModal"
      />
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_edit')"
      :visible.sync="edit.dialogVisible"
    >
      <Edit
        :item="edit"
      />
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_password')"
      :visible.sync="password.dialogVisible"
    >
      <el-form>
        <el-form-item :label="$t('admin.dialog_password_newpassword')">
          <el-input
            v-model="password.newpassword"
            type="password"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading.password"
            @click="changePassword"
          >
            {{ $t('common.ok') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="$t('admin.dialog_access')"
      :visible.sync="access.dialogVisible"
    >
      <access :item="access" />
    </el-dialog>

    <el-tooltip
      placement="top"
      :content="$t('common.goto_top')"
    >
      <BackToTop
        class="back-to-top-style"
        :visibility-height="300"
        :back-position="50"
        transition-name="fade"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import md5 from 'js-md5'
import Pagination from '@/components/Pagination'
import checkPermission from '@/utils/permission'
import BackToTop from '@/components/BackToTop'

import { MessageBox, Message } from 'element-ui'
const ElMessageBox = MessageBox
const ElMessage = Message
import Edit from '../components/Edit'
import Create from '../components/Create'
import {
  getListAgent as getListAgentApi,
  deleteAdmin,
  enableAdmin,
  disableAdmin,
  updatePassword
} from '@/api/admin'
import { useI18n } from 'vue-i18n-bridge'
import { appDatetime } from '@/filters'

const { t } = useI18n()

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = reactive({
  searchword: '',
  order: 'create_time__ASC',
  status: '',
  page: 1,
  limit: 10
})

const statusOptions = [
  { key: 'open', display_name: t('admin.table_enable') },
  { key: 'close', display_name: t('admin.table_disable') }
]

const sortOptions = [
  { key: 'create_time__ASC', label: t('admin.table_asc') },
  { key: 'create_time__DESC', label: t('admin.table_desc') }
]

const create = reactive({ dialogVisible: false, id: '', forAgent: 'general-agent' })
const edit = reactive({ dialogVisible: false, id: '' })
const access = reactive({ id: '', name: '', dialogVisible: false })
const password = reactive({ dialogVisible: false, id: '', newpassword: '' })
const loading = reactive({ detail: '', delete: '', password: false })

const getListData = () => {
  listLoading.value = true
  getListAgentApi({
    searchword: listQuery.searchword,
    order: listQuery.order,
    status: listQuery.status,
    start: (listQuery.page - 1) * listQuery.limit,
    limit: listQuery.limit
  }, `general-agents`).then(response => {
    list.value = response.data.data
    total.value = response.data.total
    listLoading.value = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    listLoading.value = false
  })
}

onMounted(() => {
  getListData()
})

const handleCloseModal = () => {
  // reset modal and refresh data
  create.dialogVisible = false
  create.forAgent = 'general-agent'
  create.id = ''

  getListData()
}

const handleFilter = () => {
  listQuery.page = 1
  getListData()
}

const handleCreate = (id) => {
  create.id = id
  if (id) {
    create.forAgent = 'regional-agent'
  }
  create.dialogVisible = true
}

const handleEdit = (index, row) => {
  edit.dialogVisible = true
  edit.id = row.id
}

const changeStatus = (e, data, index) => {
  const fn = data.status == 1 ? enableAdmin : disableAdmin
  fn(data.id).then(() => {
    ElMessage({
      message: data.status == 1 ? t('admin.table_enable_success') : t('admin.table_disable_success'),
      type: 'success',
      duration: 2000
    })
  }).catch(error => {
    console.error('Error changing status:', error)
  })
}

const handleDelete = (index, row) => {
  ElMessageBox.confirm(t('admin.confirm_delete'), t('common.tips'), {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    type: 'warning'
  }).then(() => {
    loading.delete = row.id
    deleteAdmin(row.id).then(() => {
      loading.delete = ''
      ElMessage({
        message: t('admin.confirm_delete_success'),
        type: 'success',
        duration: 5000,
        onClose: () => {
          list.value.splice(index, 1)
        }
      })
    }).catch(() => {
      loading.delete = ''
    })
  }).catch(() => {
    // User cancelled
  })
}

const handlePassword = (index, row) => {
  password.dialogVisible = true
  password.id = row.id
}

const changePassword = () => {
  loading.password = true
  if (password.newpassword === '') {
    ElMessage({
      message: t('admin.password_not_empty'),
      type: 'error',
      duration: 5000
    })
    loading.password = false
    return
  }
  updatePassword(password.id, { password: md5(password.newpassword) }).then(() => {
    loading.password = false
    ElMessage({
      message: t('admin.password_change_success'),
      type: 'success',
      duration: 5000,
      onClose: () => {
        password.newpassword = ''
        password.dialogVisible = false
      }
    })
  }).catch(() => {
    loading.password = false
  })
}

const handleDropdownCommand = (command, index, row) => {
  switch (command) {
    case 'password':
      handlePassword(index, row)
      break
    case 'edit':
      handleEdit(index, row)
      break
    case 'delete':
      handleDelete(index, row)
      break
    case 'createRegional':
      handleCreate(row.id)
      break
    default:
      console.warn('Unknown dropdown command:', command)
  }
}
</script>

<style scoped>
.back-to-top-style {
  right: 50px;
  bottom: 50px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  line-height: 45px;
  background: #e7eaf1;
  position: fixed; /* required for BackToTop to work correctly */
}
.pagination-container {
  padding: 5px 2px;
}
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

/* Action dropdown styles */
.action-dropdown-trigger {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.action-dropdown-trigger:hover {
  border-color: #c0c4cc;
  background-color: #f5f7fa;
}

.dropdown-item-password i {
  color: #e6a23c;
  margin-right: 8px;
}

.dropdown-item-edit i {
  color: #409eff;
  margin-right: 8px;
}

.dropdown-item-delete i {
  color: #f56c6c;
  margin-right: 8px;
}

.dropdown-item-create i {
  color: #67c23a;
  margin-right: 8px;
}

/* Ensure dropdown items have proper spacing */
.el-dropdown-menu__item {
  padding: 8px 16px;
  line-height: 1.5;
}

.el-dropdown-menu__item i {
  width: 16px;
  text-align: center;
}
</style>
