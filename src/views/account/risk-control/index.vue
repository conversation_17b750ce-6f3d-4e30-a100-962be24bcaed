<template>
  <div class="app-container">
    <el-card>
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ $t('route.menu.accounts.risk_control') }}</span>
      </div>

      <div class="filter-container">
        <DateRangePicker
          v-model="state.filters.date"
          style="margin-right:10px"
          @change="handleChangeDate"
        />

        <el-input
          v-model="state.filters.player_name"
          :placeholder="$t('bet.player_name')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="state.filters.player_id"
          :placeholder="$t('bet.player_id')"
          clearable
          style="width: 200px;margin-right: 10px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click.prevent="handleFilter"
        >
          {{ $t('admin.search_btn') }}
        </el-button>
      </div>
      <template>
        <table-data
          :data="state.tableData"
          :current-page="state.currentPage"
          :total="state.total"
          :loading="state.loading"
          @change="handleChangePage"
        >
          <el-table-column
            prop="bet_id"
            label="bet_id"
            width="250"
          />

          <el-table-column
            :label="$t('bet.player_name')"
            prop="player_name"
          />
          <el-table-column
            :label="$t('bet.amount')"
            prop="bet_amount"
          >
            <template slot-scope="scope">
              {{ scope.row.bet_amount | appCurrency }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('bet.winning_amount')"
            prop="winning_amount"
          >
            <template slot-scope="scope">
              {{ scope.row.winning_amount | appCurrency }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('bet.bet_status')"
            prop="winning_status"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.winning_status == 'won' ? 'success' : 'danger'"
                size="mini"
              >
                {{ $t('bet.status.' + scope.row.winning_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="created_at"
            width="200"
            align="center"
            :label="$t('created_at')"
          >
            <template slot-scope="scope">
              {{ scope.row.created_at | appDatetime }}
            </template>
          </el-table-column>
        </table-data>
      </template>
    </el-card>
    <!-- <div v-if="state.modal.show && state.modal.type == 'update'">
      <UpdateModal
        :item="state.modal.data"
        :show="state.modal.show"
        @close="handleCloseModal"
      />
    </div> -->
  </div>
</template>

<script setup>
import { getListRisk } from '@/api/account'
import DateRangePicker from '@/components/DateRangePicker/index.vue'
import TableData from '@/components/TableData/index.vue'
import { nextTick, onMounted, reactive } from 'vue'
const state = reactive({
  tableData: [],
  loading: false,
  filters: {
    player_name: '',
    player_id: '',
    start_date: '',
    end_date: '',
    page: 1,
    pageSize: 10
  },
  currentPage: 1,
  total: 0
})

const fetchData = () => {
  state.loading = true
  const requestParams = {
    page: state.filters.page,
    pageSize: 10
  }

  // Add filter parameters if they exist
  if (state.filters.player_name !== '') {
    requestParams.player_name = state.filters.player_name
  }
  if (state.filters.player_id !== '') {
    requestParams.player_id = state.filters.player_id
  }
  if (state.filters.start_date !== '' && state.filters.end_date !== '') {
    requestParams.start_date = state.filters.start_date
    requestParams.end_date = state.filters.end_date
  }
  getListRisk(requestParams).then(response => {
    state.tableData = response.data.data
    state.total = response.data.total
    state.currentPage = response.currentPage
    state.loading = false
  }).catch(error => {
    console.error('Error fetching list:', error)
    state.loading = false
  })
}
const handleFilter = () => {
  if (state.filters.player_name !== '' || state.filters.player_id !== '') {
    fetchData()
  }
}
const handleChangeDate = async (date) => {
  state.filters = {
    ...state.filters,
    ...date
  }
  await nextTick()
  fetchData
}
const handleChangePage = (page) => {
  state.filters = {
    ...state.filters,
    ...page
  }
  fetchData()
  // Fetch data for the new page
}

onMounted(() => {
  // Fetch initial data
})
</script>

<style lang="scss" scoped>

</style>
