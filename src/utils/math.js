import { toUTC } from '@/utils'

export const convertToRealAmount = (number, tenMultipler) => {
  if (tenMultipler !== 0) {
    return (number / (10 ** Math.abs(tenMultipler))).toFixed(10)
  }
  return number
}

export const formatNumber = (number, fixed = 5) => {
  const numberValue = parseFloat(number)
  if (!isNaN(numberValue)) {
    return numberValue.toFixed(fixed)
  }
  return number
}

export const formatDate = (date) => {
  const h = toUTC(new Date(date)).toISOString().substr(0, 10).split(/-/)
  return [h[0], h[1], h[2]].join('-')
}

/**
 * Format a number with exactly 2 decimal places for display
 * Handles edge cases like null, undefined, or non-numeric values gracefully
 * @param {number|string|null|undefined} value - The value to format
 * @returns {string} - Formatted string with exactly 2 decimal places
 */
export const formatCurrency = (value) => {
  // Handle null, undefined, empty string, or other falsy values
  if (value === null || value === undefined || value === '') {
    return '0.00'
  }

  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value)

  // Handle NaN or invalid numbers
  if (isNaN(numValue) || !isFinite(numValue)) {
    return '0.00'
  }

  // Return formatted number with exactly 2 decimal places
  return numValue.toFixed(2)
}
