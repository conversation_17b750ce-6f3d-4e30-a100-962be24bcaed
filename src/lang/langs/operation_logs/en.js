export default {
  operation_logs: {
    log_name: 'Log Name',
    description: 'Description',
    event: 'Event',
    type: 'Operation Type',
    types: {
      default: 'Default',
      modify_game_settings: 'Modify Game Settings',
      modify_admin: 'Modifiy Admin',
      modify_local_agent: 'Modify Local Agent',
      modify_regional_agent: 'Modify Regional Agent',
      modify_general_agent: 'Modify General Agent',
      modify_player: 'Modify Player',
      login: 'Login'
    },
    events: {
      created: 'Created',
      updated: 'Updated',
      deleted: 'Deleted',
      null: ''
    },
    causer_id: 'Causer ID',
    created_at_from: 'Created At From',
    created_at_to: 'Created At To',
    subject_type: 'Subject Type',
    subject_id: 'Subject ID'
  }
}
