export default {
  auth_group: {
    access_title: '分组名称',
    access_rules: '分组路由',
    access_select_all: '全选',
    access_tips: '注意：非超级管理员请不要授权给 `分组` 和 `管理分组` 分组。',
    access_save: '提交',
    access_save_success: '分组授权成功',

    form_top_group: '顶级分组',
    form_parentid: '父级分组',
    form_select: '请选择',
    form_title: '名称',
    form_enter_title: '请填写分组名称',
    form_description: '分组描述',
    form_enter_description: '请填写分组描述',
    form_listorder: '排序',
    form_enter_listorder: '请填写排序',
    form_listorder_tip: '排序值越大越靠前',
    form_status: '状态',
    form_status_enable: '启用',
    form_status_disable: '禁用',
    form_save: '提交',
    form_create_success: '添加分组成功',
    form_update_success: '更新分组信息成功',

    rules_parentid_required: '父级分组不能为空',
    rules_title_required: '名称不能为空',
    rules_listorder_required: '排序不能为空',

    search_title: '管理分组',
    search_searchword: '请输入关键字',
    search_status: '状态',
    search_btn: '搜索',
    search_create_group: '添加分组',
    search_clear_cache: '更新分组缓存',
    search_group_tree: '分组结构',
    search_all_group: '全部分组',

    table_title: '名称',
    table_listorder: '排序',
    table_create_time: '添加时间',
    table_status: '状态',
    table_actions: '操作',
    table_access: '授权',
    table_detail: '详情',
    table_update: '编辑',
    table_delete: '删除',
    table_enable: '启用',
    table_disable: '禁用',
    table_asc: '正序',
    table_desc: '倒序',
    table_listorder_success: '分组排序成功',

    dialog_create: '添加分组',
    dialog_update: '编辑分组',
    dialog_detail: '分组详情',
    dialog_access: '分组授权',

    detail_id: 'ID',
    detail_parentid: '父级ID',
    detail_title: '名称',
    detail_description: '描述',
    detail_listorder: '排序',
    detail_status: '状态',
    detail_update_time: '更新时间',
    detail_update_ip: '更新IP',
    detail_create_time: '添加时间',
    detail_create_ip: '添加IP',

    enable_success: '分组启用成功',
    disable_success: '分组禁用成功',
    confirm_delete: '确认要删除该分组吗？',
    confirm_delete_success: '删除分组成功',
    confirm_update_cache: '确认要更新分组缓存吗？',
    confirm_updateing_cache: '更新分组缓存中...',
    confirm_update_cache_success: '更新分组缓存成功'
  }
}
