export default {
  auth_group: {
    access_title: 'Group',
    access_rules: 'Rules',
    access_select_all: 'Select All',
    access_tips: 'Tips：dont give `权限` 和 `管理分组` to what not super admin',
    access_save: 'Save',
    access_save_success: 'Access save success',

    form_top_group: 'Top group',
    form_parentid: 'Parent',
    form_select: 'Please select',
    form_title: 'Title',
    form_enter_title: 'Enter title',
    form_description: 'Desc',
    form_enter_description: 'Enter desc',
    form_listorder: 'Sort',
    form_enter_listorder: 'Enter sort',
    form_listorder_tip: 'Group is top when sort is large',
    form_status: 'Status',
    form_status_enable: 'Enable',
    form_status_disable: 'Disable',
    form_save: 'Save',
    form_create_success: 'Create success',
    form_update_success: 'Update success',

    rules_parentid_required: 'Parent required',
    rules_title_required: 'Title required',
    rules_listorder_required: 'Sort required',

    search_title: 'Group',
    search_searchword: 'Enter keywords',
    search_status: 'Status',
    search_btn: 'Search',
    search_create_group: 'Create',
    search_clear_cache: 'Refresh',
    search_group_tree: 'Tree',
    search_all_group: 'All group',

    table_title: 'Title',
    table_listorder: 'Sort',
    table_create_time: 'CreateTime',
    table_status: 'Status',
    table_actions: 'Actions',
    table_access: 'Access',
    table_detail: 'Detail',
    table_update: 'Update',
    table_delete: 'Delete',
    table_enable: 'Enable',
    table_disable: 'Disable',
    table_asc: 'Asc',
    table_desc: 'Desc',
    table_listorder_success: 'Sort update success',

    dialog_create: 'Group create',
    dialog_update: 'Group update',
    dialog_detail: 'Group detail',
    dialog_access: 'Group access',

    detail_id: 'ID',
    detail_parentid: 'Parent',
    detail_title: 'Title',
    detail_description: 'Description',
    detail_listorder: 'Sort',
    detail_status: 'Status',
    detail_update_time: 'UpdateTime',
    detail_update_ip: 'UpdateIp',
    detail_create_time: 'CreateTime',
    detail_create_ip: 'CreateIp',

    enable_success: 'Enable success',
    disable_success: 'Disable success',
    confirm_delete: 'Confirm delete the group?',
    confirm_delete_success: 'Delete the group success',
    confirm_update_cache: 'Confirm update cache?',
    confirm_updateing_cache: 'Updating cache...',
    confirm_update_cache_success: 'Update cache success'
  }
}
