export default {
  oddstrader: {
    // Page titles and navigation
    back_to_game_config: 'Back to Game Config',
    back_to_client_list: 'Back to Client List',
    client_list: 'Client List',
    configuration: 'Configuration',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',
    unit: 'Unit',
    interval: 'Interval',
    currency: 'Currency',
    actions: 'Actions',
    tool: 'Tool',

    // Search and actions
    search_clients: 'Search clients...',
    search_configurations: 'Search configurations...',
    search: 'Search',
    reset: 'Reset',
    configure: 'Configure',
    edit: 'Edit',

    // Form labels
    select_currency: 'Select currency',
    select_interval: 'Select interval',
    enter_number_of_units: 'Enter number of units',
    units: 'Units',

    // Dialog titles
    edit_configuration: 'Edit Configuration',
    create_configuration: 'Create Configuration',

    // Form actions
    cancel: 'Cancel',
    update: 'Update',
    create: 'Create',
    save_configuration: 'Save Configuration',

    // Validation messages
    currency_required: 'Currency is required',
    interval_required: 'Interval is required',
    units_required: 'Units is required',
    units_range: 'Units must be between 1 and 999999',

    // Success/error messages
    fetch_client_list_failed: 'Failed to fetch client list',
    fetch_configurations_failed: 'Failed to fetch configurations',
    config_updated_successfully: 'Configuration updated successfully!',
    config_created_successfully: 'Configuration created successfully!',
    failed_to_save_config: 'Failed to save configuration',

    // Time units
    secs: 'secs',

    // Pagination and table info
    total: 'Total',
    page_size: 'Page Size',
    current_page: 'Current Page'
  }
}
