export default {
  ipBlacklist: {
    // Page titles and navigation
    page_title: 'IP Blacklist Management',
    back_to_list: 'Back to IP Blacklist',

    // Search and filters
    search_ip_address: 'Search IP Address',
    search_status: 'Select Status',
    search_date_range: 'Select Date Range',
    start_date: 'Start Date',
    end_date: 'End Date',
    search_btn: 'Search',
    create_btn: 'Add IP Block',

    // Bulk operations
    bulk_activate: 'Bulk Activate',
    bulk_deactivate: 'Bulk Deactivate',
    bulk_delete: 'Bulk Delete',
    bulk_operation_title: 'Bulk Operation Confirmation',
    bulk_activate_confirm: 'Are you sure you want to activate {count} IP blacklist entries?',
    bulk_deactivate_confirm: 'Are you sure you want to deactivate {count} IP blacklist entries?',
    bulk_delete_confirm: 'Are you sure you want to delete {count} IP blacklist entries? This action cannot be undone.',

    // Table headers
    table_id: 'ID',
    table_ip_address: 'IP Address',
    table_reason: 'Reason',
    table_status: 'Status',
    table_effective: 'Effective',
    table_blocked_by: 'Blocked By',
    table_expires_at: 'Expires At',
    table_created_at: 'Created At',
    table_actions: 'Actions',
    table_detail: 'Detail',
    table_update: 'Edit',
    table_delete: 'Delete',

    // Status values
    status_active: 'Active',
    status_inactive: 'Inactive',
    effective_yes: 'Yes',
    effective_no: 'No',
    never_expires: 'Never',

    // Sort options
    sort_id: 'Sort by ID',
    sort_ip_address: 'Sort by IP Address',
    sort_status: 'Sort by Status',
    sort_expires_at: 'Sort by Expiration',
    sort_created_at: 'Sort by Created Date',
    order_asc: 'Ascending',
    order_desc: 'Descending',

    // Form labels and placeholders
    form_ip_address: 'IP Address',
    form_ip_address_placeholder: 'Enter IP address (e.g., *************)',
    form_reason: 'Reason',
    form_reason_placeholder: 'Enter reason for blocking this IP address',
    form_status: 'Status',
    form_expires_at: 'Expires At',
    form_expires_at_placeholder: 'Select expiration date and time',
    form_expires_at_help: 'Leave empty for permanent block',
    form_submit: 'Create',
    form_update: 'Update',
    form_cancel: 'Cancel',

    // Validation messages
    validation_ip_required: 'IP address is required',
    validation_ip_format: 'Please enter a valid IP address',
    validation_status_required: 'Status is required',
    validation_expires_future: 'Expiration date must be in the future',

    // Dialog titles
    dialog_detail: 'IP Blacklist Details',
    dialog_create: 'Add New IP Block',
    dialog_update: 'Edit IP Block',

    // Confirmation messages
    delete_confirm_title: 'Delete Confirmation',
    delete_confirm_message: 'Are you sure you want to delete IP block for {ip}? This action cannot be undone.',
    confirm: 'Confirm',
    cancel: 'Cancel',

    // Success messages
    create_success: 'IP blacklist entry created successfully',
    update_success: 'IP blacklist entry updated successfully',
    delete_success: 'IP blacklist entry deleted successfully',
    bulk_activate_success: 'Bulk activation completed. Success: {success}, Failed: {failed}',
    bulk_deactivate_success: 'Bulk deactivation completed. Success: {success}, Failed: {failed}',
    bulk_delete_success: 'Bulk deletion completed. Success: {success}, Failed: {failed}',

    // Error messages
    fetch_error: 'Failed to fetch IP blacklist data',
    create_error: 'Failed to create IP blacklist entry',
    update_error: 'Failed to update IP blacklist entry',
    delete_error: 'Failed to delete IP blacklist entry',
    detail_error: 'Failed to fetch IP blacklist details',
    load_error: 'Failed to load IP blacklist data',
    bulk_activate_error: 'Failed to perform bulk activation',
    bulk_deactivate_error: 'Failed to perform bulk deactivation',
    bulk_delete_error: 'Failed to perform bulk deletion'
  }
}
