export default {
  europeanRoulette: {
    // Page titles and navigation
    back_to_game_config: 'Back to Game Config',
    back_to_client_list: 'Back to Client List',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',

    // Search and actions
    search_clients: 'Search clients...',
    fetch_client_list_failed: 'Failed to fetch client list',

    // Form labels
    min_amount: 'Min Amount',
    max_amount: 'Max Amount',

    // Form placeholders
    enter_min_amount: 'Enter minimum betting amount',
    enter_max_amount: 'Enter maximum betting amount',

    // Form actions
    save_configuration: 'Save Configuration',

    // Validation messages
    min_amount_required: 'Min amount is required',
    min_amount_range: 'Min amount must be greater than or equal to 0',
    max_amount_required: 'Max amount is required',
    max_amount_range: 'Max amount must be greater than or equal to 0',
    max_amount_greater_than_min: 'Max amount must be greater than min amount',

    // Success/error messages
    no_config_found_using_defaults: 'No existing configuration found. Using default values.',
    config_updated_successfully: 'Configuration updated successfully!',
    failed_to_save_config: 'Failed to save configuration',
    form_reset_to_defaults: 'Form reset to default values'
  }
}
