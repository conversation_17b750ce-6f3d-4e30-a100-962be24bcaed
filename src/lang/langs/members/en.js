export default {
  member: {
    list: 'Member List',
    gender: 'Gender',
    name: 'Name',
    phone: 'Phone',
    level: 'Level',
    type: 'Type',
    wallet: 'Wallet',
    total_member: 'Total Members',
    total_investment: 'Team Total Investment',
    personal_investment: 'Personal Total Investment',
    agent_number: 'Agent Phone Number',
    change_password: 'Change User Password',
    access: 'Access',
    phones: 'Phones',
    remarks: 'Remarks',
    amount: 'Amount',
    add_funds: 'Add Funds',
    modify_balance: 'Modify Balance',
    deduce_funds: 'Deduce Funds',
    modify_type: 'Modify Type',
    id_name: 'ID Name',
    id_card: 'ID Card Number',
    last_login: 'Last Login',
    result: 'Verify Result',
    verify_identify: 'Verify Identify',
    binding_status: 'Binding Account Status',
    id_card_taken: 'Member Id Card Is Already Taken',
    binding_success: 'Binded Id Card To Member Account',
    cannot_bind: 'Id Formation Incorrect, Please Check That And Input Again',
    invitation_code: 'Invitation Code',
    select_all: 'Select All',
    lock_selected: 'Lock Selected',
    unlock_selected: 'Unlock Selected',
    lock: 'Lock User',
    locked: '(Locked)',
    parent: 'Parent',
    ip: 'IP',
    ip_country: 'Ip Region',
    store: {
      serial_number: 'Store Serial Number'
    },
    input: {
      password: 'Please Input Password (Min Length Is 8)',
      passcode: 'Please Input 6 Digits Of Transaction Passcode',
      amount: 'Please Input Amount To Add',
      phones: 'Please Add Phones: 1,2,3,4',
      remark: '(Optional) Please Add remark'
    },
    confirm: {
      password: 'Please Input Confirm Password (Min Length Is 8)',
      passcode: 'Please Confirm 6 Digits Of Transaction Passcode'
    },
    level_item: {
      all: 'All',
      general: 'General',
      silver: 'Silver',
      gold: 'Gold',
      star: 'Star'
    },
    gender_type: {
      male: 'Male',
      female: 'Female',
      unknown: 'Unknown'
    },
    type_item: {
      all: 'All',
      individual: 'Individual',
      agent: 'Agent'
    },
    wallet_type: {
      reward: 'Reward Wallet',
      promotion: 'Promotion Wallet',
      commission: 'Commission Wallet',
      deposit: 'Deposit Wallet',
      profit: 'Profit Wallet',
      partnership: 'Partnership Wallet'
    },
    access_type: {
      active: 'Active',
      locked_deposit: 'Locked Deposit',
      locked_withdraw: 'Locked Withdraw'
    },
    vip_level: 'VIP Level',
    send_promotion_card: 'Send Promotion Card',
    send_coupon: 'Send Coupon',
    quantity: 'Quantity',
    must_select_user_before_send_promotion_card: 'Please select user before send promotion card',
    must_select_user_first: 'Please select user first',
    appointment: {
      time: 'Appointment Time',
      header: 'Appointments',
      popup_header: 'Preview Appointment'
    },
    agent_type_label: 'Agent Type',
    agent_type: {
      regular: 'Regular Agent',
      regional_super_agent: 'Regional Super Agent',
      silver_super_agent: 'Silver Super Agent',
      gold_super_agent: 'Gold Super Agent'
    },
    bypass_face_auth: 'Bypass Face Authentication',
    bypass_face_auth_enabled: 'Face Authentication Bypass Enabled',
    bypass_face_auth_disabled: 'Face Authentication Bypass Disabled',
    change_agent_type: 'Change Agent Type',
    please_select_agent_type: 'Please select an agent type',
    agent_type_change_not_allowed: 'Agent type change not allowed',
    gold_super_agent_cannot_change: 'Gold Super Agent type cannot be changed',
    invalid_agent_type_change: 'Invalid agent type change',
    agent_type_upgrade_invalid: 'This agent type upgrade path is not allowed',
    partner_type_label: 'Partner Type',
    partner_type: {
      agent: 'Regular user',
      elementary: 'Elementary partner',
      senior: 'Senior partner',
      premium: 'Premium partner'
    },
    change_partner_type: 'Change Partner Type',
    please_select_partner_type: 'Please select a partner type'
  }
}
