export default {
  member: {
    list: '會員列表',
    gender: '性别',
    name: '名称',
    phone: '电话',
    level: '等级',
    type: '类型',
    total_member: '总会员数',
    total_investment: '团队总投资',
    personal_investment: '个人总投资',
    wallet: '钱包',
    amount: '金额',
    add_funds: '添加资金',
    modify_balance: '修改餘額',
    deduce_funds: '扣除资金',
    modify_type: '修改類型',
    phones: '电话号码',
    remarks: '备注',
    id_name: '身份證名稱',
    id_card: '身份證號碼',
    agent_number: '代理電話號碼',
    change_password: '更改用戶密碼',
    access: '訪問',
    last_login: '最後登錄',
    result: '验证结果',
    verify_identify: '验证身份',
    binding_status: '绑定账户状态',
    id_card_taken: '会员身份证已被占用',
    binding_success: '成功绑定身份证到会员账户',
    cannot_bind: '身份证号格式错误，请检查后重新输入',
    parent: '上级',
    invitation_code: '邀请码',
    ip: 'IP',
    ip_country: 'IP区域',
    select_all: '全选',
    lock_selected: '锁定所选',
    unlock_selected: '解锁所选',
    lock: '封锁帐号',
    locked: '(锁)',
    input: {
      password: '请输入密码（最少8个字符）',
      passcode: '请输入6位交易密码',
      amount: '请输入添加的金额',
      phones: '请添加电话号码：1,2,3,4',
      remark: '（可选）请添加备注'
    },
    confirm: {
      password: '请再次输入密码(至少8个字符)',
      passcode: '请确认6位交易密码'
    },
    level_item: {
      all: '全部',
      general: '通用',
      silver: '银级',
      gold: '金级',
      star: '星级'
    },
    type_item: {
      all: '全部',
      individual: '个人',
      agent: '代理'
    },
    gender_type: {
      male: '男',
      female: '女',
      unknown: '未知'
    },
    wallet_type: {
      reward: '储备金钱包',
      promotion: '体验钱包',
      commission: '佣金钱包',
      deposit: '存款钱包',
      profit: '外卖钱包',
      partnership: '动态钱包'
    },
    access_type: {
      active: '活躍',
      locked_deposit: '鎖定存款',
      locked_withdraw: '鎖定提款'
    },
    vip_level_id: 'VIP等级',
    send_promotion_card: '发送促销卡',
    send_coupon: '发送优惠券',
    quantity: '数量',
    must_select_user_before_send_promotion_card: '请在发送促销卡之前选择用户',
    must_select_user_first: '请先选择用户',
    store: {
      serial_number: '商店序列号'
    },
    appointment: {
      time: '预约时间',
      header: '预约',
      popup_header: '预览预约'
    },
    agent_type_label: '代理类型',
    agent_type: {
      regular: '普通代理',
      regional_super_agent: '区域超级代理',
      silver_super_agent: '银牌超级代理',
      gold_super_agent: '金牌超级代理'
    },
    bypass_face_auth: '绕过人脸认证',
    bypass_face_auth_enabled: '已启用人脸认证绕过',
    bypass_face_auth_disabled: '已禁用人脸认证绕过',
    change_agent_type: '更改代理类型',
    please_select_agent_type: '请选择代理类型',
    agent_type_change_not_allowed: '不允许更改代理类型',
    gold_super_agent_cannot_change: '金牌超级代理类型不能更改',
    invalid_agent_type_change: '无效的代理类型更改',
    agent_type_upgrade_invalid: '不允许此代理类型升级路径',
    partner_type_label: '合伙人类型',
    partner_type: {
      agent: '一般用户',
      elementary: '初级合伙人',
      senior: '中级合伙人',
      premium: '高级合伙人'
    },
    change_partner_type: '更改合伙人类型',
    please_select_partner_type: '请选择合伙人类型'
  }
}
