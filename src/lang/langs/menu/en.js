export default {
  menu: {
    form_parent: 'Parent',
    form_parent_select: 'Please select',
    form_close_select: 'Close',
    form_fast_select: 'Select',
    form_select_rules: 'Select rules',
    form_title: 'Title',
    form_enter_title: 'Enter title',
    form_slug: 'Slug',
    form_enter_slug: 'Enter slug',
    form_url: 'Url',
    form_url_tooltip: 'Url has prefix',
    form_enter_url: 'Enter url',
    form_method: 'Method',
    form_sort: 'Sort',
    form_enter_sort: 'Enter sort',
    form_save: 'Save',
    form_clear: 'Reset',
    form_top_menu: 'Top menu',
    form_create_success: 'Create success',
    form_update_success: 'Update success',

    rules_pid_required: 'Parent required',
    rules_title_required: 'Title required',
    rules_slug_required: 'Slug required',
    rules_url_required: 'Url required',
    rules_sort_required: 'Sort required',

    search_title: 'Menus',
    search_alert_title: 'Menu set tip',
    search_alert_description: 'Now, menu not use',
    search_create: 'Add',

    table_title: 'Menu',
    table_url: 'Url',
    table_sort: 'Sort',
    table_actions: 'Actions',
    table_detail: 'Detail',
    table_update: 'Update',
    table_delete: 'Delete',

    dialog_create: 'Create',
    dialog_update: 'Update',
    dialog_detail: 'Detail',

    detail_id: 'ID',
    detail_parentid: 'Parent',
    detail_title: 'Title',
    detail_slug: 'Slug',
    detail_url: 'Url',
    detail_method: 'Method',
    detail_sort: 'Sort',

    confirm_delete: 'Confirm delete the menu?',
    confirm_delete_success: 'Delete the menu success',

    // Account management menu items
    accounts: {
      player_list: 'Player List',
      general_agents: 'General Agents',
      regional_agents: 'Regional Agents',
      local_agents: 'Local Agents',
      login_logs: 'Login Logs',
      operation_logs: 'Operation Logs',
      risk_control: 'Risk Control',
      black_list: 'IP Blacklist'
    }
  }
}
