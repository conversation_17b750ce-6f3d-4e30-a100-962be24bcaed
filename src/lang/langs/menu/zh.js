export default {
  menu: {
    form_parent: '父级菜单',
    form_parent_select: '请选择',
    form_close_select: '关闭选择',
    form_fast_select: '快速选择',
    form_select_rules: '请选择菜单信息',
    form_title: '名称',
    form_enter_title: '请填写菜单名称',
    form_slug: '标识',
    form_enter_slug: '请填写菜单标识',
    form_url: '链接',
    form_url_tooltip: '链接默认不用加前缀',
    form_enter_url: '请填写菜单链接',
    form_method: '请求方式',
    form_sort: '排序',
    form_enter_sort: '请填写排序',
    form_save: '提交',
    form_clear: '清空',
    form_top_menu: '顶级菜单',
    form_create_success: '添加菜单成功',
    form_update_success: '更新菜单信息成功',

    rules_pid_required: '父级菜单不能为空',
    rules_title_required: '名称不能为空',
    rules_slug_required: '标识不能为空',
    rules_url_required: '链接不能为空',
    rules_sort_required: '排序不能为空',

    search_title: '菜单管理',
    search_alert_title: '菜单设置及新增需注意',
    search_alert_description: '菜单目前只有设置，暂时没有使用',
    search_create: '添加菜单',

    table_title: '菜单',
    table_url: '链接',
    table_sort: '排序',
    table_actions: '操作',
    table_detail: '详情',
    table_update: '编辑',
    table_delete: '删除',

    dialog_create: '添加菜单',
    dialog_update: '编辑菜单',
    dialog_detail: '菜单详情',

    detail_id: 'ID',
    detail_parentid: '父级ID',
    detail_title: '名称',
    detail_slug: '菜单标识',
    detail_url: '菜单链接',
    detail_method: '链接请求类型',
    detail_sort: '排序',

    confirm_delete: '确认要删除该菜单吗?',
    confirm_delete_success: '删除菜单成功',

    // Account management menu items
    accounts: {
      player_list: '玩家列表',
      general_agents: '总代理',
      regional_agents: '区域代理',
      local_agents: '本地代理',
      login_logs: '登录日志',
      operation_logs: '操作日志',
      risk_control: '风险控制',
      black_list: 'IP黑名单'
    }
  }
}
