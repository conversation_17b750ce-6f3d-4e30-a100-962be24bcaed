export default {
  double: {
    // Page titles and navigation
    client_list: 'Client List',
    back_to_game_config: 'Back to Game Config',
    back_to_client_list: 'Back to Client List',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',

    // Search and actions
    search_clients: 'Search clients...',
    fetch_client_list_failed: 'Failed to fetch client list',

    // Form labels
    red_prize: 'Red Prize',
    black_prize: 'Black Prize',
    special_prize: 'Special Prize',
    min_amount: 'Min Amount',
    max_amount: 'Max Amount',

    // Form placeholders
    enter_red_prize: 'Enter red prize multiplier',
    enter_black_prize: 'Enter black prize multiplier',
    enter_special_prize: 'Enter special prize multiplier',
    enter_min_amount: 'Enter minimum betting amount',
    enter_max_amount: 'Enter maximum betting amount',

    // Form actions
    save_configuration: 'Save Configuration',

    // Validation messages
    red_prize_required: 'Red prize is required',
    red_prize_range: 'Red prize must be greater than or equal to 0',
    black_prize_required: 'Black prize is required',
    black_prize_range: 'Black prize must be greater than or equal to 0',
    special_prize_required: 'Special prize is required',
    special_prize_range: 'Special prize must be greater than or equal to 0',
    min_amount_required: 'Min amount is required',
    min_amount_range: 'Min amount must be greater than or equal to 0',
    max_amount_required: 'Max amount is required',
    max_amount_range: 'Max amount must be greater than or equal to 0',
    max_amount_greater_than_min: 'Max amount must be greater than min amount',

    // Success/error messages
    config_updated_successfully: 'Configuration updated successfully!',
    failed_to_save_config: 'Failed to save configuration',
    no_config_found_using_defaults: 'No existing configuration found. Using default values.'
  }
}
