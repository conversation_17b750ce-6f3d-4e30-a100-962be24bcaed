export default {
  auth_rule: {
    form_parentid: '父级权限',
    form_select: '请选择',
    form_title: '名称',
    form_enter_title: '请填写权限名称',
    form_url: '请求链接',
    form_url_tooltip: '请求链接默认不用加前缀',
    form_enter_url: '请填写请求链接',
    form_url_tip: '请求链接默认前缀为: admin-api/ 。可以在配置文件更改',
    form_method: '请求方式',
    form_slug: '标识',
    form_enter_slug: '请填写标识',
    form_description: '权限描述',
    form_enter_description: '请填写权限描述',
    form_listorder: '排序',
    form_enter_listorder: '请填写排序',
    form_listorder_tip: '排序值越大越靠前',
    form_need_auth: '鉴定权限',
    form_need_auth_enable: '启用',
    form_need_auth_disable: '禁用',
    form_status: '状态',
    form_status_enable: '启用',
    form_status_disable: '禁用',
    form_save: '提交',
    form_top_rule: '顶级权限',
    form_create_success: '添加权限成功',
    form_update_success: '更新权限信息成功',

    rules_parentid_required: '父级不能为空',
    rules_title_required: '名称不能为空',
    rules_url_required: '请求链接不能为空',
    rules_slug_required: '标识不能为空',
    rules_listorder_required: '排序不能为空',

    search_title: '权限路由',
    search_delete_selected: '删除选中',
    search_searchword: '请输入关键字',
    search_method: '请求方式',
    search_status: '状态',
    search_btn: '搜索',
    search_method_get: 'GET',
    search_method_head: 'HEAD',
    search_method_post: 'POST',
    search_method_put: 'PUT',
    search_method_delete: 'DELETE',
    search_method_patch: 'PATCH',
    search_method_options: 'OPTIONS',
    search_status_enable: '启用',
    search_status_disable: '禁用',
    search_sort_asc: '正序',
    search_sort_desc: '倒序',
    search_all_rule: '全部权限',
    search_create_rule: '添加权限',
    search_rule_tree: '权限结构',

    table_title: '名称',
    table_url: '链接',
    table_copy_slug: '点击复制 {slug}',
    table_listorder: '排序',
    table_create_time: '添加时间',
    table_status: '状态',
    table_actions: '操作',
    table_detail: '详情',
    table_update: '编辑',
    table_delete: '删除',
    table_listorder_success: '权限排序成功',

    dialog_create: '添加权限',
    dialog_update: '编辑权限',
    dialog_detail: '权限详情',

    detail_id: 'ID',
    detail_parentid: '父级ID',
    detail_title: '名称',
    detail_url: '权限链接',
    detail_method: '请求类型',
    detail_slug: '地址标识',
    detail_description: '描述',
    detail_listorder: '排序',
    detail_need_auth: '验证权限',
    detail_status: '状态',
    detail_update_time: '更新时间',
    detail_update_ip: '更新IP',
    detail_create_time: '添加时间',
    detail_create_ip: '添加IP',

    enable_success: '权限启用成功',
    disable_success: '权限禁用成功',
    confirm_delete: '确认要删除该权限吗？',
    confirm_delete_success: '删除权限成功',
    confirm_delete_selected: '确认要删除选中的权限吗？',
    select_delete_rules: '请选择要删除的权限'
  }
}
