export default {
  auth_rule: {
    form_parentid: 'Parent',
    form_select: 'Please select',
    form_title: 'Title',
    form_enter_title: 'Enter title',
    form_url: 'Url',
    form_url_tooltip: 'Default url not need add prefix',
    form_enter_url: 'Enter url',
    form_url_tip: 'Default prefix: admin-api/ and config can update it.',
    form_method: 'Method',
    form_slug: 'Slug',
    form_enter_slug: 'Enter slug',
    form_description: 'Desc',
    form_enter_description: 'Enter desc',
    form_listorder: 'Sort',
    form_enter_listorder: 'Enter sort',
    form_listorder_tip: 'Rule is top when sort is large',
    form_need_auth: 'Need Auth',
    form_need_auth_enable: 'Enable',
    form_need_auth_disable: 'Disable',
    form_status: 'Status',
    form_status_enable: 'Enable',
    form_status_disable: 'Disable',
    form_save: 'Save',
    form_top_rule: 'Top rule',
    form_create_success: 'Create success',
    form_update_success: 'Update success',

    rules_parentid_required: 'Parent required',
    rules_title_required: 'Title required',
    rules_url_required: 'Url required',
    rules_slug_required: 'Slug required',
    rules_listorder_required: 'Sort required',

    search_title: 'Rules',
    search_delete_selected: 'Delete',
    search_searchword: 'Enter keywords',
    search_method: 'Method',
    search_status: 'Status',
    search_btn: 'Search',
    search_method_get: 'GET',
    search_method_head: 'HEAD',
    search_method_post: 'POST',
    search_method_put: 'PUT',
    search_method_delete: 'DELETE',
    search_method_patch: 'PATCH',
    search_method_options: 'OPTIONS',
    search_status_enable: 'Enable',
    search_status_disable: 'Disable',
    search_sort_asc: 'Asc',
    search_sort_desc: 'Desc',
    search_all_rule: 'All',
    search_create_rule: 'Create',
    search_rule_tree: 'Tree',

    table_title: 'Title',
    table_url: 'Url',
    table_copy_slug: 'Copy {slug}',
    table_listorder: 'Sort',
    table_create_time: 'Created Time',
    table_status: 'Status',
    table_actions: 'Actions',
    table_detail: 'Detail',
    table_update: 'Update',
    table_delete: 'Delete',
    table_listorder_success: 'Update sort success',

    dialog_create: 'Rule create',
    dialog_update: 'Rule update',
    dialog_detail: 'Rule detail',

    detail_id: 'ID',
    detail_parentid: 'Parent',
    detail_title: 'Title',
    detail_url: 'Url',
    detail_method: 'Method',
    detail_slug: 'Slug',
    detail_description: 'Desc',
    detail_listorder: 'Sort',
    detail_need_auth: 'Need Auth',
    detail_status: 'Status',
    detail_update_time: 'Updated Time',
    detail_update_ip: 'Updated Ip',
    detail_create_time: 'Created Time',
    detail_create_ip: 'Created Ip',

    enable_success: 'Enable success',
    disable_success: 'Disable success',
    confirm_delete: 'Confirm delete the rule?',
    confirm_delete_success: 'Delete the rule success',
    confirm_delete_selected: 'Confirm delete the selected rule?',
    select_delete_rules: 'Please will delete rules'
  }
}
