export default {
  player_logs: {
    header_title: '玩家日志',
    username: '用户名',
    type: '日志类型',
    ip_address: 'IP地址',
    ip_area: 'IP区域',
    created_at: '创建时间',
    updated_at: '更新时间',

    // Filter placeholders
    username_placeholder: '按用户名搜索',
    ip_address_placeholder: '按IP地址筛选',
    type_placeholder: '选择日志类型',
    from_date_placeholder: '开始日期',
    to_date_placeholder: '结束日期',

    // Buttons
    reset_btn: '重置',
    export_btn: '导出',

    // Log types
    types: {
      login: '登录',
      logout: '登出',
      change_password: '密码修改',
      profile_update: '资料更新',
      balance_update: '余额更新',
      bet_placed: '下注',
      game_result: '游戏结果',
      withdrawal: '提款',
      deposit: '存款',
      security_event: '安全事件'
    },

    // Messages
    fetch_error: '获取玩家日志失败',
    fetch_types_error: '获取日志类型失败',
    no_data: '未找到玩家日志',
    loading: '正在加载玩家日志...',

    // Statistics
    statistics: {
      total_logs: '总日志数',
      logs_today: '今日',
      logs_this_week: '本周',
      logs_this_month: '本月',
      unique_players: '独立玩家',
      unique_ip_addresses: '独立IP'
    }
  }
}
