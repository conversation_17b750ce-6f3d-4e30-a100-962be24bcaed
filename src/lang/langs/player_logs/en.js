export default {
  player_logs: {
    header_title: 'Player Logs',
    username: '<PERSON>rna<PERSON>',
    type: 'Log Type',
    ip_address: 'IP Address',
    ip_area: 'IP Area',
    created_at: 'Created At',
    updated_at: 'Updated At',

    // Filter placeholders
    username_placeholder: 'Search by username',
    ip_address_placeholder: 'Filter by IP address',
    type_placeholder: 'Select log type',
    from_date_placeholder: 'From date',
    to_date_placeholder: 'To date',

    // Buttons
    reset_btn: 'Reset',
    export_btn: 'Export',

    // Log types
    types: {
      login: 'Login',
      logout: 'Logout',
      change_password: 'Password Change',
      profile_update: 'Profile Update',
      balance_update: 'Balance Update',
      bet_placed: 'Bet Placed',
      game_result: 'Game Result',
      withdrawal: 'Withdrawal',
      deposit: 'Deposit',
      security_event: 'Security Event'
    },

    // Messages
    fetch_error: 'Failed to fetch player logs',
    fetch_types_error: 'Failed to fetch log types',
    no_data: 'No player logs found',
    loading: 'Loading player logs...',

    // Statistics
    statistics: {
      total_logs: 'Total Logs',
      logs_today: 'Today',
      logs_this_week: 'This Week',
      logs_this_month: 'This Month',
      unique_players: 'Unique Players',
      unique_ip_addresses: 'Unique IPs'
    }
  }
}
