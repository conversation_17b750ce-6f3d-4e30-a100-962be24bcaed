export default {
  announcement: {
    header_title: 'Announcements',
    title: 'Title',
    message: 'Message',
    release_time: 'Release Time',
    user_id: 'User Id',
    user_phone: 'User Phone',
    subordinate_type: 'Subordinate Type',
    direct_subordinates: 'Direct Subordinates',
    all_subordinates: 'All Subordinates',
    add_text: 'Create Announcements',
    edit: 'Edit Announcement',
    create: 'Create Announcement',
    force_popup: 'Force Popup',
    header_title_published: 'Published Announcement',
    header_finance_product_announcement: 'Finance Product Announcement'
  },
  notification: {
    image: 'Popup Image',
    header_title: 'Login Popup Management',
    title: {
      create_login_popup: 'Create Login Popup',
      update_login_popup: 'Update Login Popup'
    }
  }
}
