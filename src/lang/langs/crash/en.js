export default {
  crash: {
    // Page titles and navigation
    client_list: 'Client List',
    back_to_client_list: 'Back to Client List',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',

    // Search and actions
    search_clients: 'Search clients...',
    fetch_client_list_failed: 'Failed to fetch client list',

    // Form labels
    betting_period_display: 'Betting Period Display',
    crash_animation: 'Crash Animation',
    exponential_value: 'Exponential Value',
    min_weighted_random_float: 'Min Weighted Random Float',
    max_weighted_random_float: 'Max Weighted Random Float',

    // Form placeholders
    enter_betting_period_display: 'Enter betting period display time',
    enter_crash_animation: 'Enter crash animation duration',
    enter_exponential_value: 'Enter exponential value',
    enter_min_weighted_random_float: 'Enter minimum weighted random float',
    enter_max_weighted_random_float: 'Enter maximum weighted random float',

    // Form descriptions
    betting_period_display_desc: 'Time in seconds for betting period display (1-300)',
    crash_animation_desc: 'Animation duration in seconds (1-60)',
    exponential_value_desc: 'Exponential multiplier value (1-100)',
    min_weighted_random_float_desc: 'Minimum weighted random float value (0.01-99.99)',
    max_weighted_random_float_desc: 'Maximum weighted random float value (0.01-99.99)',

    // Form actions
    save_configuration: 'Save Configuration',

    // Validation messages
    betting_period_display_required: 'Betting period display is required',
    betting_period_display_range: 'Must be between 1 and 300 seconds',
    crash_animation_required: 'Crash animation duration is required',
    crash_animation_range: 'Must be between 1 and 60 seconds',
    exponential_value_required: 'Exponential value is required',
    exponential_value_range: 'Must be between 1 and 100',
    min_weighted_random_float_required: 'Minimum weighted random float is required',
    min_weighted_random_float_range: 'Must be between 0.01 and 99.99',
    max_weighted_random_float_required: 'Maximum weighted random float is required',
    max_weighted_random_float_range: 'Must be between 0.01 and 99.99',
    max_value_greater_than_min: 'Maximum value must be greater than minimum value',

    // Success/error messages
    config_updated_successfully: 'Configuration updated successfully!',
    no_config_id_cannot_update: 'No configuration ID found. Cannot update.',
    failed_to_save_config: 'Failed to save configuration',
    no_config_found_using_defaults: 'No existing configuration found. Using default values.',
    form_reset_to_defaults: 'Form reset to default values'
  }
}
