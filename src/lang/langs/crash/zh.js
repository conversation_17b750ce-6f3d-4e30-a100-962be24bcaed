export default {
  crash: {
    // Page titles and navigation
    client_list: '客户端列表',
    back_to_client_list: '返回客户端列表',

    // Table headers
    client_id: '客户端ID',
    client_name: '客户端名称',

    // Search and actions
    search_clients: '搜索客户端...',
    fetch_client_list_failed: '获取客户端列表失败',

    // Form labels
    betting_period_display: '投注期显示',
    crash_animation: '崩溃动画',
    exponential_value: '指数值',
    min_weighted_random_float: '最小加权随机浮点数',
    max_weighted_random_float: '最大加权随机浮点数',

    // Form placeholders
    enter_betting_period_display: '请输入投注期显示时间',
    enter_crash_animation: '请输入崩溃动画持续时间',
    enter_exponential_value: '请输入指数值',
    enter_min_weighted_random_float: '请输入最小加权随机浮点数',
    enter_max_weighted_random_float: '请输入最大加权随机浮点数',

    // Form descriptions
    betting_period_display_desc: '投注期显示时间（秒）(1-300)',
    crash_animation_desc: '动画持续时间（秒）(1-60)',
    exponential_value_desc: '指数乘数值 (1-100)',
    min_weighted_random_float_desc: '最小加权随机浮点数值 (0.01-99.99)',
    max_weighted_random_float_desc: '最大加权随机浮点数值 (0.01-99.99)',

    // Form actions
    save_configuration: '保存配置',

    // Validation messages
    betting_period_display_required: '投注期显示是必需的',
    betting_period_display_range: '必须在1到300秒之间',
    crash_animation_required: '崩溃动画持续时间是必需的',
    crash_animation_range: '必须在1到60秒之间',
    exponential_value_required: '指数值是必需的',
    exponential_value_range: '必须在1到100之间',
    min_weighted_random_float_required: '最小加权随机浮点数是必需的',
    min_weighted_random_float_range: '必须在0.01到99.99之间',
    max_weighted_random_float_required: '最大加权随机浮点数是必需的',
    max_weighted_random_float_range: '必须在0.01到99.99之间',
    max_value_greater_than_min: '最大值必须大于最小值',

    // Success/error messages
    config_updated_successfully: '配置更新成功！',
    no_config_id_cannot_update: '未找到配置ID。无法更新。',
    failed_to_save_config: '保存配置失败',
    no_config_found_using_defaults: '未找到现有配置。使用默认值。',
    form_reset_to_defaults: '表单已重置为默认值'
  }
}
