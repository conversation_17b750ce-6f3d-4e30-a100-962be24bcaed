export default {
  config: {
    form_group: 'Group',
    form_select_group: 'Select Group',
    form_type: 'Type',
    form_select_type: 'Select type',
    form_title: 'Title',
    form_enter_title: 'Enter title',
    form_name: 'Name',
    form_enter_name: 'Enter name',
    form_options: 'Options',
    form_enter_options: 'Enter options',
    form_options_value: 'Value',
    form_enter_options_value: 'Enter options value',
    form_description: 'Desc',
    form_enter_description: 'Enter desc',
    form_listorder: 'Sort',
    form_enter_listorder: 'Enter sort',
    form_group_none: 'None group',
    form_type_text: 'text',
    form_type_textarea: 'textarea',
    form_type_array: 'array',
    form_type_number: 'number',
    form_type_radio: 'radio',
    form_type_checkbox: 'checkbox',
    form_type_select: 'select',
    form_type_switch: 'switch',
    form_type_image: 'image',
    form_type_rate: 'rate',
    form_type_color: 'color',
    form_type_slider: 'slider',
    form_type_time: 'time',
    form_type_date: 'date',
    form_type_range_time: 'range time',
    form_type_range_date: 'range date',
    form_show: 'Show',
    form_show_enable: 'Enable',
    form_show_disable: 'Disable',
    form_status: 'Status',
    form_status_enable: 'Enable',
    form_status_disable: 'Disable',
    form_save: 'Save',
    form_create_success: 'Create success',
    form_update_success: 'Update success',

    rules_type_required: 'Type required',
    rules_title_required: 'Title required',
    rules_name_required: 'Name required',
    rules_listorder_required: 'Sort required',

    search_title: 'Config Option',
    search_searchword: 'Enter keywords',
    search_group: 'Group',
    search_group_none: 'none group',
    search_status: 'Status',
    search_status_enable: 'Enable',
    search_status_disable: 'Disable',
    search_sort_asc: 'Asc',
    search_sort_desc: 'Desc',
    search_btn: 'Search',
    search_create_config: 'Add',

    table_name: 'Name',
    table_title: 'Title',
    table_type: 'Type',
    table_group: 'Group',
    table_listorder: 'Sort',
    table_create_time: 'Created Time',
    table_status: 'Status',
    table_actions: 'Actions',
    table_detail: 'Detail',
    table_update: 'Update',
    table_delete: 'Delete',
    table_listorder_success: 'Update sort success',

    dialog_detail: 'Config detail',
    dialog_create: 'Config create',
    dialog_update: 'Config update',

    detail_id: 'ID',
    detail_group: 'Group',
    detail_type: 'Type',
    detail_title: 'Title',
    detail_name: 'Name',
    detail_options: 'Options',
    detail_options_value: 'Value',
    detail_description: 'Desc',
    detail_listorder: 'Sort',
    detail_show: 'Show',
    detail_status: 'Status',
    detail_update_time: 'UpdateTime',
    detail_create_time: 'CreateTime',

    enable_success: 'Enable success',
    disable_success: 'Disable success',
    confirm_delete: 'Confirm delete the config?',
    confirm_delete_success: 'Delete the config success',

    setting_title: 'Web Setting',
    setting_save: 'Save',
    setting_loading: 'Loading..',
    setting_save_success: 'Save success'
  }
}
