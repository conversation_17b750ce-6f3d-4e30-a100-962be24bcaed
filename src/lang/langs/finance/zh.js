export default {
  finance: {
    code: '交易代码',
    amount: '金额',
    old_balance: '旧余额',
    new_balance: '新余额',
    transactions: '交易列表',
    commissions: '收益清单',
    action: '行动',
    rebate: '佣金清单',
    deposits: '存款列表',
    status: '状态',
    remark: '備註',
    transaction: {
      type: '交易類型',
      method: '方法',
      auto: '自動',
      manual: '手動',
      types: {
        transfer: '转账',
        deposit: '存款',
        manual_deposit: '手动存款',
        manual_deduction: '手动扣款',
        withdraw: '提现',
        withdraw_rollback: '提现回滚',
        order: '订单',
        rebate: '返利',
        investment: '投资',
        activity: '活动',
        activity_register: '注册活动',
        activity_sign_in: '签到活动',
        extra_reward: '额外奖励',
        promotion_card_order: '推广卡订单',
        subordinate_verification_invited_reward: '下属验证邀请奖励',
        delivery_service_profit: '配送服务利润'
      }
    },
    commission: {
      rate: '佣金率'
    },
    activity_title: '活动标题',
    activity_type: '活动类型',
    activity_cycle: '活动周期',
    active_date: '活动日期',
    sign_in_reward: '登录奖励',
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    specified_date: '指定日期',
    cycle: '周期',
    start_date: '开始日期',
    end_date: '结束日期',
    operate: '操作',
    activity_amount: '活动金额',
    activity_period: '活动期间',
    created_at: '创建时间',
    phone: '手机',
    ID: 'ID',
    withdraw_list: '提现列表',
    user_id: '用户ID',
    user_bank_id: '用户银行ID',
    wallet_id: '钱包ID',
    fee: '手续费',
    activity_name: '活动名称',
    total_users: '总用户',
    total_amount: '总金额',
    daily_avg_amount: '平均金额/用户',
    withdraw: {
      amount: '提现金额',
      code: '提现代码',
      status: '提现状态',
      update_title: '更新提现状态',
      pending: '待处理',
      processing: '处理中',
      approved: '已批准',
      rejected: '已拒绝',
      failed: '失败',
      pending_for_face_auth: '等待人脸识别',
      payment_channel: '支付渠道',
      offline_payment: '线下打款',
      total_withdraw: '总提现次数',
      wallet: '提现钱包',
      wallet_types: {
        reward: '储备金钱包',
        promotion: '体验钱包',
        commission: '佣金钱包',
        deposit: '存款钱包',
        profit: '外卖钱包',
        partnership: '动态钱包'
      }
    },
    senhuo_status: '提供商状态',
    senhuo_statuses: {
      requesting: '請求中',
      request_failed: '請求失敗',
      processing: '處理中',
      payment_completed: '支付完成',
      payment_failed: '支付失敗',
      review_failed: '審核失敗'
    },
    rejected: '已拒絕',
    bank_name: '銀行名稱',
    wallet_type: '錢包類型',
    loan_list: '贷款清单',
    name: '名稱',
    bank_account: '銀行帳號',
    one_time: '一次性',
    notes: '備註',
    total_deposits: '總存款',
    saudi_wallet_profit: '沙地錢包利潤',
    general_wallet_profit: '通用錢包利潤',
    commission_wallet_profit: '佣金錢包利潤',
    parent_phone: '上级手机号',
    team_size: '團隊規模',
    claimed_subordinates: '已领取下级',
    self_claimed: '用户领取次数',
    subordinates_claimed_times: '下属领取次数',
    accumulated_withdraw_amount: '累積提現金額',
    subordinate_deposit_amount: '下屬存款金額',
    account: '帳戶',
    account_name: '帳戶名稱',
    first_withdraw_time: '首次提款時間',
    is_first_withdraw: '是否首次提款',
    payout_info: '支付信息',
    deposit: {
      amount: '存款金額',
      status: '存款狀態',
      update_title: '更新存款狀態',
      pending: '待處理',
      processing: '處理中',
      success: '成功',
      failed: '失敗',
      payment_channel: '支付渠道',
      outOrderId: '外部訂單ID',
      type: '存款類型',
      verifyStatus: '驗證狀態',
      verifyProcessing: '订单状态查核中，稍候更状态更新'
    },
    order: {
      product_name: '产品名称',
      payment_type: '支付类型',
      quantity: '数量',
      return_type: '回报类型',
      return_value: '回报金额',
      daily_return: '每日回报',
      order_profit: '订单利润',
      profit: '利润',
      cost: '成本',
      confirm_delivered: '确认已送达',
      multiple_delivered: '多次送达'
    },
    add_chance_for_user: '为用户添加旋转机会',
    number_of_chances: '机会数',
    savingSummary: {
      activityName: '活动名称',
      activityPeriod: '活动期间',
      totalSaving: '总存款',
      totalRewardPoints: '总奖励积分'
    },
    saving: {
      title: {
        summary: '天天存摘要',
        list: '天天存列表'
      },
      type: {
        direct: '直接付款'
      },
      amount: '天天存类别',
      reward: '奖励',
      times: '总储蓄次数'
    },
    approve: '批准',
    reject: '拒绝',
    refund: {
      header_title: '退款',
      preview_title: '预览退款',
      detail_title: '退款详情',
      order_detail_title: '订单详情',
      refund_identify: '用户身份证',
      id_card_front: '身份证正面',
      id_card_back: '身份证背面',
      request_submit_document: '请求提交文件',
      status: {
        0: '待处理',
        1: '加工',
        2: '文件提交中',
        3: '文件验证中',
        4: '已批准',
        5: '已拒绝'
      },
      actions: {
        0: '已请求退款',
        1: '开始处理退款',
        2: '已请求提交身份证明文件',
        3: '已提交文件',
        4: '已批准退款',
        5: '拒绝退款'
      }
    }
  },
  loans: {
    installments: '分期付款',
    installment_amount: '分期付款金额'
  },
  wallet: {
    commission: '佣金钱包',
    promotion: '推广钱包',
    deposit: '存款钱包',
    profit: '利润钱包',
    financial: '财务钱包',
    reward: '奖励钱包'
  }
}
