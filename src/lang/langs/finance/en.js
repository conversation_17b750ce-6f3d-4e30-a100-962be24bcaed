export default {
  finance: {
    code: 'Transaction Code',
    amount: 'Amount',
    old_balance: 'Old Balance',
    new_balance: 'New Balance',
    transactions: 'Transaction List ',
    commissions: 'Commission List ',
    rebate: 'Rebate List',
    deposits: 'Deposit List',
    status: 'Status',
    remark: 'Remark',
    action: 'Action',
    transaction: {
      type: 'Transaction Type',
      method: 'Method',
      auto: 'Auto',
      manual: 'Manual',
      types: {
        transfer: 'Transfer',
        deposit: 'Deposit',
        manual_deposit: 'Manual Deposit',
        manual_deduction: 'Manual Deduction',
        withdraw: 'Withdraw',
        withdraw_rollback: 'Withdraw Rollback',
        order: 'Order',
        rebate: 'Rebate',
        investment: 'Investment',
        activity: 'Activity',
        activity_register: 'Register Activity',
        activity_sign_in: 'Sign In Activity',
        extra_reward: 'Extra Reward',
        promotion_card_order: 'Promotion Card Order',
        subordinate_verification_invited_reward: 'Subordinate Verification Invited Reward',
        delivery_service_profit: 'Delivery Service Profit'
      }
    },
    commission: {
      rate: 'Commission Rate'
    },
    activity_title: 'Activity Title',
    activity_type: 'Activity Type',
    activity_cycle: 'Activity Cycle',
    active_date: 'Active Date',
    sign_in_reward: 'Sign In Reward',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    specified_date: 'Specified Date',
    cycle: 'Cycle',
    start_date: 'Start Date',
    end_date: 'End Date',
    operate: 'Operate',
    activity_amount: 'Activity Amount',
    activity_period: 'Activity Period',
    created_at: 'Created At',
    phone: 'Phone',
    ID: 'ID',
    withdraw_list: 'Withdraw List',
    user_id: 'User ID',
    user_bank_id: 'User Bank ID',
    wallet_id: 'Wallet ID',
    fee: 'Fee',
    activity_name: 'Activity Name',
    total_users: 'Total Users',
    total_amount: 'Total Amount',
    daily_avg_amount: 'Avg Amount/user',
    loan_list: 'Loan List',
    withdraw: {
      amount: 'Withdraw Amount',
      code: 'Code',
      status: 'Withdraw Status',
      update_title: 'Update Withdraw Status',
      pending: 'Pending',
      processing: 'Processing',
      approved: 'Approved',
      rejected: 'Rejected',
      failed: 'Failed',
      pending_for_face_auth: 'Face Auth',
      payment_channel: 'Payment Channel',
      offline_payment: 'Offline Payment',
      total_withdraw: 'Total withdraw times',
      wallet: 'Withdraw Wallet',
      wallet_types: {
        reward: 'Reward Wallet',
        promotion: 'Promotion Wallet',
        commission: 'Commission Wallet',
        deposit: 'Deposit Wallet',
        profit: 'Profit Wallet',
        partnership: 'Partnership Wallet'
      }
    },
    senhuo_status: 'Provider Status',
    senhuo_statuses: {
      requesting: 'Requesting',
      request_failed: 'Request Failed',
      processing: 'Processing',
      payment_completed: 'Payment Completed',
      payment_failed: 'Payment Failed',
      review_failed: 'Review Failed',
      PENDING: 'Requesting',
      PROCESSING: 'Processing',
      SUCCESS: 'Success',
      FAILED: 'Failed'
    },
    bank_name: 'Bank Name',
    wallet_type: 'Wallet Type',
    name: 'Name',
    bank_account: 'Bank Account',
    one_time: 'One Time',
    notes: 'Notes',
    total_deposits: 'Total Deposits',
    saudi_wallet_profit: 'Saudi Wallet Profit',
    general_wallet_profit: 'General Wallet Profit',
    commission_wallet_profit: 'Commission Wallet Profit',
    parent_phone: 'Parent Phone',
    team_size: 'Team Size',
    claimed_subordinates: 'Subordinates Claimed',
    self_claimed: 'User Claimed Times',
    subordinates_claimed_times: 'Subordinates Claimed Times',
    accumulated_withdraw_amount: 'Accumulated Withdraw Amount',
    subordinate_deposit_amount: 'Subordinate Deposit Amount',
    account: 'Account',
    account_name: 'Account Name',
    first_withdraw_time: 'First withdraw time',
    is_first_withdraw: 'Is First Withdraw',
    payout_info: 'Payout Information',
    deposit: {
      amount: 'Amount',
      status: 'Status',
      pending: 'Pending',
      processing: 'Processing',
      success: 'Success',
      failed: 'Failed',
      payment_channel: 'Payment Channel',
      outOrderId: 'Out Order ID',
      type: 'Deposit Type',
      verifyStatus: 'Verify Status',
      verifyProcessing:
        'The order status is being checked and will be updated later.'
    },
    order: {
      product_name: 'Product Name',
      payment_type: 'Payment Type',
      quantity: 'Quantity',
      return_type: 'Return Type',
      return_value: 'Return Value',
      daily_return: 'Daily Return',
      verifyStatus: 'Verify Status',
      order_profit: 'Order Profit',
      cost: 'Cost',
      confirm_delivered: 'Confirm Delivered',
      multiple_delivered: 'Multiple Delivered'
    },
    add_chance_for_user: 'Add spin chance for user',
    number_of_chances: 'Number of chances',
    savingSummary: {
      activityName: 'Activity Name',
      activityPeriod: 'Activity Period',
      totalSaving: 'Total Saving',
      totalRewardPoints: 'Total Reward Points'
    },
    saving: {
      title: {
        summary: 'Saving Summary',
        list: 'Saving List'
      },
      type: {
        direct: 'Direct Payment'
      },
      amount: 'Daily save category',
      reward: 'Reward',
      times: 'Total saving time'
    },
    approve: 'Approve',
    reject: 'Reject',
    refund: {
      header_title: 'Refunds',
      preview_title: 'Preview Refund',
      detail_title: 'Refund Detail',
      order_detail_title: 'Order Detail',
      refund_identify: 'User Identify Card',
      id_card_front: 'Id Card Front',
      id_card_back: 'Id Card Back',
      request_submit_document: 'Request Submit Document',
      status: {
        0: 'Pending',
        1: 'Processing',
        2: 'Document Submmiting',
        3: 'Document Verifing',
        4: 'Approved',
        5: 'Declined'
      },
      actions: {
        0: 'Refund Requested',
        1: 'Start process refund',
        2: 'Requested to submit Indentify document',
        3: 'Submmited Document',
        4: 'Approved Refund',
        5: 'Declined Refund'
      }
    }
  },
  loans: {
    installments: 'Installments',
    installment_amount: 'Installment Amount'
  },
  wallet: {
    commission: 'Commission Wallet',
    promotion: 'Promotion Wallet',
    deposit: 'Deposit Wallet',
    profit: 'Profit Wallet',
    financial: 'Financial Wallet',
    reward: 'Reward Wallet',
    virtual: 'Virtual Wallet'
  }

}
