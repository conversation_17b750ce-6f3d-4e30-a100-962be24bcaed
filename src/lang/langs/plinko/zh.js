export default {
  plinko: {
    // Page titles and navigation
    back_to_game_config: '返回游戏配置',
    back_to_client_list: '返回客户端列表',
    game_config: '游戏配置',
    client_list: '客户端列表',
    configuration: '配置',

    // Table headers
    client_id: '客户端ID',
    client_name: '客户端名称',
    actions: '操作',
    color: '颜色',
    position: '位置',
    rate: '比率',
    number: '数字',
    calculation: '计算方式',

    // Search and actions
    search_clients: '搜索客户端...',
    search_configurations: '搜索配置...',
    refresh: '刷新',
    configure: '配置',
    edit: '编辑',
    create: '创建',

    // Form labels
    select_color: '选择颜色',
    enter_position: '输入位置',
    enter_rate: '输入比率',
    enter_number: '输入数字',
    select_calculation: '选择计算类型',

    // Color options
    blue: '蓝色',
    red: '红色',
    green: '绿色',
    yellow: '黄色',
    purple: '紫色',
    orange: '橙色',

    // Calculation options
    multiplier: '乘数',
    addition: '加法',
    percentage: '百分比',

    // Dialog titles
    edit_configuration: '编辑配置',
    create_configuration: '创建配置',

    // Form actions
    save: '保存',
    cancel: '取消',
    save_configuration: '保存配置',

    // Validation messages
    color_required: '颜色是必需的',
    position_required: '位置是必需的',
    position_range: '位置必须在0到100之间',
    rate_required: '比率是必需的',
    rate_range: '比率必须大于0',
    number_required: '数字是必需的',
    number_range: '数字必须大于0',
    calculation_required: '计算类型是必需的',

    // Success/error messages
    fetch_client_list_failed: '获取客户端列表失败',
    fetch_configurations_failed: '获取配置失败',
    configuration_updated_successfully: '配置更新成功！',
    configuration_created_successfully: '配置创建成功！',
    failed_to_save_configuration: '保存配置失败'
  }
}
