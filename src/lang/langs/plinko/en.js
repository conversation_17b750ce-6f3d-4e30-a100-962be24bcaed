export default {
  plinko: {
    // Page titles and navigation
    back_to_game_config: 'Back to Game Config',
    back_to_client_list: 'Back to Client List',
    game_config: 'Game Configuration',
    client_list: 'Client List',
    configuration: 'Configuration',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',
    actions: 'Actions',
    color: 'Color',
    position: 'Position',
    rate: 'Rate',
    number: 'Number',
    calculation: 'Calculation',

    // Search and actions
    search_clients: 'Search clients...',
    search_configurations: 'Search configurations...',
    refresh: 'Refresh',
    configure: 'Configure',
    edit: 'Edit',
    create: 'Create',

    // Form labels
    select_color: 'Select color',
    enter_position: 'Enter position',
    enter_rate: 'Enter rate',
    enter_number: 'Enter number',
    select_calculation: 'Select calculation type',

    // Color options
    blue: 'Blue',
    red: 'Red',
    green: 'Green',
    yellow: 'Yellow',
    purple: 'Purple',
    orange: 'Orange',

    // Calculation options
    multiplier: 'Multiplier',
    addition: 'Addition',
    percentage: 'Percentage',

    // Dialog titles
    edit_configuration: 'Edit Configuration',
    create_configuration: 'Create Configuration',

    // Form actions
    save: 'Save',
    cancel: 'Cancel',
    save_configuration: 'Save Configuration',

    // Validation messages
    color_required: 'Color is required',
    position_required: 'Position is required',
    position_range: 'Position must be between 0 and 100',
    rate_required: 'Rate is required',
    rate_range: 'Rate must be greater than 0',
    number_required: 'Number is required',
    number_range: 'Number must be greater than 0',
    calculation_required: 'Calculation type is required',

    // Success/error messages
    fetch_client_list_failed: 'Failed to fetch client list',
    fetch_configurations_failed: 'Failed to fetch configurations',
    configuration_updated_successfully: 'Configuration updated successfully!',
    configuration_created_successfully: 'Configuration created successfully!',
    failed_to_save_configuration: 'Failed to save configuration'
  }
}
