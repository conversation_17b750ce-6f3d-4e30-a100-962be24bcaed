export default {
  settings: {
    commission: 'Commission Settings',
    app: 'App Settings',
    general_team_size_min: 'General Team Size Minimum',
    general_team_size_max: 'General Team Size Maximum',
    silver_team_size_min: 'Silver Team Size Minimum',
    silver_team_size_max: 'Silver Team Size Maximum',
    silver_team_invested: 'Silver Team Investment Amount',
    gold_team_size_min: 'Gold Team Size Minimum',
    gold_team_invested: 'Gold Team Investment Amount',
    gold_silver_team_size_min: 'Gold-Silver Team Size Minimum',
    gold_team_size_max: 'Gold Team Size Maximum',
    star_team_size_min: 'Star Team Size Minimum',
    star_gold_team_size_min: 'Star Gold Team Size Minimum',
    star_team_invested: 'Star Team Investment Amount',
    max_sub_level: 'Maximum Sub-Level',
    rebate_commission_lv_1: 'Rebate Commission Level 1',
    rebate_commission_lv_2: 'Rebate Commission Level 2',
    rebate_commission_lv_3: 'Rebate Commission Level 3',
    rebate_commission_lv_4: 'Rebate Commission Level 4',
    rebate_commission_lv_5: 'Rebate Commission Level 5',
    rebate_commission_lv_6: 'Rebate Commission Level 6',
    first_login_message: 'ID Verify Remind Message',
    app_title: 'Application Title',
    crm_link: 'CRM Link',
    app_logo: 'Application Logo',
    login_popup_qr: 'Login Popup QR',
    app_banner: 'App Banner',
    community_url: 'Community URL',
    community_qr: 'Community QR',
    apk_download_link: 'APK Download Link',
    brochure_file: 'Brochure Download Link',
    login_reward: {
      title: 'Daily Sign-in Reward',
      update_title: 'Update Daily Sign-in Reward Setting',
      create_title: 'Update Daily Sign-in Reward Setting',
      period: 'Period',
      can_repeated: 'Can Repeated',
      reward: 'Reward',
      input: {
        period: 'Please Input Reward Period',
        can_repeated: 'Can Repeated',
        reward: 'Please Input Reward Value'
      }
    },
    wallet: 'Wallet Settings',
    wallet_general_rule: 'General Wallet Rules',
    wallet_commission_rule: 'Commission Wallet Rules',
    wallet_saudi_rule: 'Saudi Wallet Rules',
    invite_subordinate_id_verification_reward: 'Invite Subordinate ID Verification Reward',
    wallet_reward_withdraw_day: 'Reward wallet Withdraw Day',
    wallet_promotion_withdraw_day: 'Promotion wallet Withdraw Day',
    wallet_commission_withdraw_day: 'Commission wallet Withdraw Day',
    wallet_deposit_withdraw_day: 'Deposit wallet Withdraw Day',
    wallet_profit_withdraw_day: 'Profit wallet Withdraw Day',
    wallet_partnership_withdraw_day: 'Partnership Wallet Withdraw Day',
    force_bypass_id_verification: 'Force Bypass ID Verification',
    deposit_setting: 'Deposit Settings',
    withdraw: 'Withdraw Settings',
    saudi_allow_to_withdraw: 'Allow Withdrawal',
    saudi_maximum_withdraw_requests: 'Maximum Withdrawal Requests Per Day',
    saudi_maximum_withdraw_amount: 'Maximum Withdrawal Amount Per Day',
    saudi_minimum_withdraw_amount: 'Minimum Withdrawal Amount',
    general_allow_to_withdraw: 'Allow Withdrawal',
    general_maximum_withdraw_requests: 'Maximum Withdrawal Requests Per Day',
    general_maximum_withdraw_amount: 'Maximum Withdrawal Amount Per Day',
    general_minimum_withdraw_amount: 'Minimum Withdrawal Amount',
    commission_allow_to_withdraw: 'Allow Withdrawal',
    commission_maximum_withdraw_requests: 'Maximum Withdrawal Requests Per Day',
    commission_maximum_withdraw_amount: 'Maximum Withdrawal Amount Per Day',
    commission_minimum_withdraw_amount: 'Minimum Withdrawal Amount',
    withdraw_noticed: 'Withdraw Note',
    ios_download_link: 'Ios Download Link',
    lucky_spin: {
      title: 'Lucky Spin',
      for_specific_user: 'Lucky Spin chance for specific user',
      reward_title: 'Reward title',
      reward_point: 'Reward Point',
      probability: 'Probability',
      user_id: 'User ID',
      user: 'User',
      user_phone_with_commaseparator: 'Example: 123,456,789'
    },
    lucky_spin_rule: 'Lucky Spin Rule',
    first_withdraw_validation: 'First withdrawal validation',
    first_withdraw_minimum_amount: 'First withdrawal minimum amount',
    second_withdraw_validation: 'Second withdrawal validation',
    second_withdraw_minimum_amount: 'Second withdrawal minimum amount',
    request_invitation_code: 'Require Invitation Code',
    not_allowed_phone_prefix: 'Forbidden prefix phone numbers',
    stock_value: 'Stock Value',
    user_level: {
      title: 'User VIP Level',
      name: 'Name',
      min_points: 'Min Deposit',
      max_points: 'Max Deposit',
      level_order: 'Level Order',
      discount: 'Discount',
      withdraw_fee: 'Withdraw Fee',
      min_withdraw_amount: 'Min Withdraw Amount'
    },
    direct_sub_purchase_prod_cate_1_reward:
      'Direct Sub Purchase Prod Cate 1 Reward',
    max_subordinates_to_show: 'Max Subordinates To Show',
    equity_stake_reminder: 'Equity Stake Reminder',
    profile_ads_banner: 'Profile Ads Banner',
    profile_ads_url: "Profile Banner's Redirect Url",
    redirect: {
      product_page: 'Product Page',
      invite_page: 'Invite Page',
      inapp_loans_page: 'InApp Loan Page',
      new_year_saving_reward_page: 'New Year Saving Reward Page',
      discount_coupon_page: 'Discount Coupon Page'
    },
    float_icon: 'Float Icon',
    float_icon_link: 'Float Icon Link',
    referral_url: 'Referral URL (邀请链接)',
    saudi_wallet_temp_reset_date: 'Saudi Wallet Temp Reset Date',
    saudi_wallet_reset_date: 'Saudi Wallet Reset Date',
    saudi_wallet_reminder_start_date: 'Saudi Wallet Reminder Start Date',
    saudi_wallet_reminder_end_date: 'Saudi Wallet Reminder End Date',
    saudi_wallet_grace_period_start_date: 'Saudi Wallet Grace Period Start Date',
    saudi_wallets_active_reminder_content: 'Active Reminder Content',
    transfer_saudi_profit_to_general_wallet_date: 'Transfer Saudi Profit To General Wallet Date',
    general_wallet_10_times_multiplier_end_date: 'General Wallet amount 10 times multiplier withdraw end date',
    reward_value: 'Reward Value',
    rewards: {
      name: 'Name',
      description: 'Description',
      reward_source: 'Reward Source',
      type: 'Type',
      reward_type: 'Reward Type',
      required_points: 'Required Points',
      reward_amount: 'Reward Amount',
      is_active: 'Is Active',
      point: 'Point',
      special_qr: 'Special QR',
      default: 'Default',
      extra: 'Extra',
      special: 'Special',
      referral: 'Referral',
      title: 'Rewards',
      auto_release: 'Auto Release'
    },
    profit_rules: 'Profit Rules',
    cs_working_hours: 'CS Working Hours',
    cs_btn_url: 'CS Button URL',
    cs_website_url: 'CS Website URL',
    cs_img: 'CS Image',
    allow_to_withdraw: 'Allow Withdrawal',
    withdrawal_fee: 'Withdrawal Fee',
    withdrawal_min_amount: 'Withdrawal Min Amount',
    withdrawal_max_amount: 'Withdrawal Max Amount',
    for_official_community: 'For Official Community',
    require_purchase_before_withdraw: 'Require Purchase Before Withdraw',
    withdraw_stage: {
      stage1: 'Stage 1',
      stage2: 'Stage 2',
      stage3: 'Stage 3',
      stage4: 'Stage 4'
    },
    min_withdraw_err_msg: 'Minimum withdrawal error message',
    product_require_err_msg: 'Product require error message',
    is_maintenance_mode: 'Maintenance Mode',
    maintenance_reason: 'Maintenance Reason',
    maintenance_from_date: 'Maintenance From Date',
    maintenance_to_date: 'Maintenance To Date',
    // Operation Service
    operation_service: 'Operation Service',
    enable_delivery_service: 'Enable Operation Service',
    operation_service_promotion_profit: 'Promotion Operation Service Profit',
    operation_order_profit: 'Operation Order Profit',
    enable_operation_service_for_purchased_user: 'Enable Promotion For Purchased User',
    enable_operation_service_start_time: 'Enable Operation Service Start',
    enable_operation_service_end_time: 'Enable Operation Service End',
    allow_transfer_to_deposit_wallet: 'Allow Transfer To Deposit Wallet',
    max_operation_request_per_day: 'Max Operation Service Per Day',
    auto_release_operation_request: 'Auto Release Operation Service',
    operation_service_maximum_profit: 'Maximum Free Store Profit',
    operation_request_service_extra_profit: 'Extra Profit',
    reserved_fund_rate: 'Reserved Fund Rate',
    dedicated_link_0: 'Dedicated Link 1',
    dedicated_link_1: 'Dedicated Link 2',
    dedicated_link_2: 'Dedicated Link 3',
    dedicated_link_3: 'Dedicated Link 4',
    forbidden_deposit_time: 'Forbidden Deposit Time',
    share_product: 'Share Product',
    share_product_price: 'Share Price',
    share_product_interest_rate: 'Interest Rate',
    share_product_weight: 'Weight value',
    share_product_banner: 'Banner',
    invitation_link: 'Invitation Link',
    loan_monthly_release_day: 'Loan Monthly Release Day',
    partnership_commission: 'Commissions',
    partnership: 'Partnership Settings',
    rebate_commission: 'Rebate Commission',
    // partner_banners: 'Partner Banners',
    partner_type: 'Partner Type',
    level: 'Level',
    partner_brochure_link: 'Partner Brochure Download Link',
    face_recognition_reminder_message: 'Face Recognition Reminder Message',
    withdraw_stage_remind_message: 'Stage Remind Message'
  }
}
