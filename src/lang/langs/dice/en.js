export default {
  dice: {
    // Page titles and navigation
    back_to_game_config: 'Back to Game Config',
    back_to_dice_config: 'Back to Dice Config',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',

    // Search and actions
    search_clients: 'Search clients...',
    fetch_client_list_failed: 'Failed to fetch client list',

    // Configuration modes
    setup: 'Setup',
    auto: 'Auto',
    manual: 'Manual',

    // Form labels
    max_odds: 'Max Odds',
    rolling_up: 'Rolling Up',
    rolling_down: 'Rolling Down',
    prediction: 'Prediction',
    winrate: 'Winrate',

    // Form actions
    apply_auto_configuration: 'Apply Auto Configuration',
    save_manual_configuration: 'Save Manual Configuration',
    load_defaults: 'Load Defaults',

    // Validation messages
    max_odds_required: 'Max odds is required',
    max_odds_range: 'Max odds must be between 1 and 100',

    // Success/error messages
    no_config_found_using_defaults: 'No existing configuration found. Initialized with default values.',
    failed_to_load_config_using_defaults: 'Failed to load configuration. Using default values.',
    switched_to_mode: 'Switched to {mode} configuration mode',
    config_updated_successfully: 'Configuration updated successfully!',
    config_created_successfully: 'Configuration created successfully!',
    failed_to_save_config: 'Failed to save configuration',
    form_reset_to_defaults: 'Form reset to default values',
    default_values_loaded: 'Default values loaded'
  }
}
