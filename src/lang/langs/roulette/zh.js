export default {
  roulette: {
    // Page titles and navigation
    back_to_game_config: '返回游戏配置',
    back_to_client_list: '返回客户端列表',
    client_list: '客户端列表',
    configuration: '配置',
    game_configuration: '游戏配置',

    // Table headers
    client_id: '客户端ID',
    client_name: '客户端名称',
    actions: '操作',

    // Search and actions
    search_clients: '搜索客户端...',
    configure: '配置',

    // Basic form labels
    interval: '间隔',
    waiting_time: '等待时间',
    start_time: '开始时间',
    end_time: '结束时间',
    minimum_bet: '最小投注',
    maximum_bet: '最大投注',

    // Form placeholders
    enter_interval_seconds: '输入间隔秒数',
    enter_waiting_time_seconds: '输入等待时间秒数',
    select_start_time: '选择开始时间',
    select_end_time: '选择结束时间',
    enter_minimum_bet_amount: '输入最小投注金额',
    enter_maximum_bet_amount: '输入最大投注金额',

    // Choice configurations
    choice_configurations: '选择配置',
    green_probability: '绿色概率',
    green_multiplier: '绿色倍数',
    purple_probability: '紫色概率',
    purple_multiplier: '紫色倍数',
    chest_probability: '宝箱概率',
    chest_multiplier: '宝箱倍数',

    // Choice placeholders
    green_probability_placeholder: '绿色概率 %',
    green_multiplier_placeholder: '绿色倍数',
    purple_probability_placeholder: '紫色概率 %',
    purple_multiplier_placeholder: '紫色倍数',
    chest_probability_placeholder: '宝箱概率 %',
    chest_multiplier_placeholder: '宝箱倍数',

    // Form actions
    save_configuration: '保存配置',
    reset: '重置',

    // Validation messages
    interval_required: '间隔是必需的',
    interval_range: '间隔必须在1到3600秒之间',
    waiting_time_required: '等待时间是必需的',
    waiting_time_range: '等待时间必须在1到300秒之间',
    start_time_required: '开始时间是必需的',
    end_time_required: '结束时间是必需的',
    minimum_bet_required: '最小投注是必需的',
    minimum_bet_range: '最小投注必须大于或等于0',
    maximum_bet_required: '最大投注是必需的',
    maximum_bet_range: '最大投注必须大于或等于0',
    maximum_bet_greater_than_minimum: '最大投注必须大于最小投注',
    green_probability_required: '绿色概率是必需的',
    probability_range: '概率必须在0到100之间',
    green_multiplier_required: '绿色倍数是必需的',
    multiplier_range: '倍数必须大于0',
    purple_probability_required: '紫色概率是必需的',
    purple_multiplier_required: '紫色倍数是必需的',
    chest_probability_required: '宝箱概率是必需的',
    chest_multiplier_required: '宝箱倍数是必需的',
    callback_url_required: '回调URL是必需的',
    callback_url_invalid: '请输入有效的URL',

    // Success/error messages
    fetch_client_list_failed: '获取客户端列表失败',
    no_config_found_using_defaults: '未找到现有配置。使用默认值。',
    config_updated_successfully: '配置更新成功！',
    failed_to_save_config: '保存配置失败',
    no_config_id_found: '未找到配置ID。无法更新。',
    error_parsing_config: '解析配置数据错误',
    form_reset_to_defaults: '表单已重置为默认值'
  }
}
