export default {
  roulette: {
    // Page titles and navigation
    back_to_game_config: 'Back to Game Config',
    back_to_client_list: 'Back to Client List',
    client_list: 'Client List',
    configuration: 'Configuration',
    game_configuration: 'Game Configuration',

    // Table headers
    client_id: 'Client ID',
    client_name: 'Client Name',
    actions: 'Actions',

    // Search and actions
    search_clients: 'Search clients...',
    configure: 'Configure',

    // Basic form labels
    interval: 'Interval',
    waiting_time: 'Waiting Time',
    start_time: 'Start Time',
    end_time: 'End Time',
    minimum_bet: 'Minimum Bet',
    maximum_bet: 'Maximum Bet',

    // Form placeholders
    enter_interval_seconds: 'Enter interval in seconds',
    enter_waiting_time_seconds: 'Enter waiting time in seconds',
    select_start_time: 'Select start time',
    select_end_time: 'Select end time',
    enter_minimum_bet_amount: 'Enter minimum bet amount',
    enter_maximum_bet_amount: 'Enter maximum bet amount',

    // Choice configurations
    choice_configurations: 'Choice Configurations',
    green_probability: 'Green Probability',
    green_multiplier: 'Green Multiplier',
    purple_probability: 'Purple Probability',
    purple_multiplier: 'Purple Multiplier',
    chest_probability: 'Chest Probability',
    chest_multiplier: 'Chest Multiplier',

    // Choice placeholders
    green_probability_placeholder: 'Green probability %',
    green_multiplier_placeholder: 'Green multiplier',
    purple_probability_placeholder: 'Purple probability %',
    purple_multiplier_placeholder: 'Purple multiplier',
    chest_probability_placeholder: 'Chest probability %',
    chest_multiplier_placeholder: 'Chest multiplier',

    // Form actions
    save_configuration: 'Save Configuration',
    reset: 'Reset',

    // Validation messages
    interval_required: 'Interval is required',
    interval_range: 'Interval must be between 1 and 3600 seconds',
    waiting_time_required: 'Waiting time is required',
    waiting_time_range: 'Waiting time must be between 1 and 300 seconds',
    start_time_required: 'Start time is required',
    end_time_required: 'End time is required',
    minimum_bet_required: 'Minimum bet is required',
    minimum_bet_range: 'Minimum bet must be greater than or equal to 0',
    maximum_bet_required: 'Maximum bet is required',
    maximum_bet_range: 'Maximum bet must be greater than or equal to 0',
    maximum_bet_greater_than_minimum: 'Maximum bet must be greater than minimum bet',
    green_probability_required: 'Green probability is required',
    probability_range: 'Probability must be between 0 and 100',
    green_multiplier_required: 'Green multiplier is required',
    multiplier_range: 'Multiplier must be greater than 0',
    purple_probability_required: 'Purple probability is required',
    purple_multiplier_required: 'Purple multiplier is required',
    chest_probability_required: 'Chest probability is required',
    chest_multiplier_required: 'Chest multiplier is required',
    callback_url_required: 'Callback URL is required',
    callback_url_invalid: 'Please enter a valid URL',

    // Success/error messages
    fetch_client_list_failed: 'Failed to fetch client list',
    no_config_found_using_defaults: 'No existing configuration found. Using default values.',
    config_updated_successfully: 'Configuration updated successfully!',
    failed_to_save_config: 'Failed to save configuration',
    no_config_id_found: 'No configuration ID found. Cannot update.',
    error_parsing_config: 'Error parsing configuration data',
    form_reset_to_defaults: 'Form reset to default values'
  }
}
