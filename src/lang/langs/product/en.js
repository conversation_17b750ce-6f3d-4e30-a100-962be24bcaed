export default {
  product: {
    name: 'Name',
    description: 'Description',
    rules: 'Rules',
    tags: 'Tags',
    banner: 'Banner',
    stock: 'Stock',
    unit_price: 'Unit Price',
    title: 'Product List',
    update_title: 'Update Product',
    create_title: 'Create Product',
    return_percentage: 'Return Percentage',
    category: 'Category Name',
    name_input: 'Input Product Name',
    description_input: 'Input Product Description',
    free_store: 'Free Store',
    free_store_settings: 'Free Store Settings',
    profit_rules: 'Profit Rules',
    return_type: {
      title: 'Return Type',
      monthly: 'Monthly',
      yearly: 'Yearly',
      daily: 'Daily'
    },
    return_value_type: 'Return Value Type',
    return_value: 'Return Value',
    reward_value_type: 'Reward Value Type',
    reward_value: 'Reward Value',
    return_type_changed_to_monthly: 'Return Type changed to Monthly',
    basic_info: 'Basic Information',
    pricing_details: 'Pricing Details',
    reward_info: 'Reward Information',
    additional_details: 'Additional Details',
    remaining_stock: 'Remaining Stock',
    purchase_limit: 'Purchase Limit',
    sort: 'Sort',
    promotion_cards: 'Promotion Cards',
    type: {
      title: 'Type',
      micro: 'Micro Store',
      medium: 'Medium Size Store',
      high_end: 'High End Store',
      flagship: 'Flagship Store'
    },
    image: 'Image',
    price: 'Price',
    promotion_price: 'Promotion Price',
    limited: 'Limited',
    reward: 'Reward',
    daily_profit: 'Daily Profit',
    profit_rate: 'Profit Rate',
    promotion_icon: 'Promotion Icon',
    share_quantity: 'Share Quantity',
    available_loan: 'Available Loan',
    icons: {
      limited: 'Limited',
      recommended: 'Recommended',
      hot: 'Hot',
      hot_sale: 'Hot Sale'
    }
  }
}
