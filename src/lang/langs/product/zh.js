export default {
  product: {
    name: '产品名字',
    description: '产品描述',
    rules: '规则',
    tags: '标签',
    banner: '横幅',
    stock: '库存',
    unit_price: '单价',
    title: '产品列表',
    update_title: '更新产品',
    create_title: '创建产品',
    return_percentage: '收益百分比',
    category: '类别名称',
    name_input: '输入产品名称',
    description_input: '输入产品描述',
    free_store: '免费商店',
    free_store_settings: '免费商店设置',
    profit_rules: '规则',
    return_type: {
      title: '收益类型',
      monthly: '每月',
      yearly: '每年',
      daily: '每日',
      finance: '财务'
    },
    return_value_type: '收益值类型',
    return_value: '收益值',
    reward_value_type: '奖励值类型',
    reward_value: '奖励值',
    return_type_changed_to_monthly: '收益类型更改为每月',
    basic_info: '基本信息',
    pricing_details: '定价详情',
    reward_info: '奖励信息',
    additional_details: '附加细节',
    remaining_stock: '剩余库存',
    purchase_limit: '购买限制',
    sort: '排序',
    promotion_cards: '促销卡',
    type: {
      title: '类型',
      micro: '微型店铺',
      medium: '中级店铺',
      high_end: '高级店铺',
      flagship: '旗舰店'
    },
    image: '产品图片',
    price: '产品价格',
    promotion_price: '补贴价',
    limited: '产品是否限购',
    reward: '产品可赠送期权',
    daily_profit: '产品收益',
    profit_rate: '收益率',
    promotion_icon: '促销图标',
    icons: {
      limited: '限时抢购',
      recommended: '推荐',
      hot: '火热',
      hot_sale: '热销中'
    },
    share_quantity: '股数',
    available_loan: 'con可贷款金额'
  }
}
