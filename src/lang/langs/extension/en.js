export default {
  extension: {
    title: 'Extension',
    local_extension: 'Local extension',

    command_alert_title: 'Tips',
    command_alert_content: 'Command is for installing local extension',
    command_title: 'Title',
    command_name: 'Name',
    command_repository_register: 'Register',
    command_repository_register_btn: 'Register to repository',
    command_repository_register_tip: 'Register local extension to repository',
    command_require_title: 'Install',
    command_require_tip: 'Composer install command',
    command_repository_remove: 'Remove',
    command_repository_remove_btn: 'Register from repository',
    command_repository_remove_tip: 'Remove local extension from repository',
    command_unstall_title: 'Uninstall',
    command_unstall_tip: 'Composer uninstall command',
    command_confirm_tip: 'It will update composer.json file',

    table_title: 'Extension',
    table_description: 'Desc',
    table_adaptation: 'Adaptation',
    table_installed: 'Installed',
    table_version: 'Version',
    table_new_version: 'New Version',
    table_authors: 'Authors',
    table_actions: 'Actions',
    table_install: 'Install',
    table_upgrade: 'Upgrade',

    confirm_install: 'Confirm install the extension ({name})？',
    confirm_installing: 'Extension install...',
    confirm_install_success: 'Extension install success',
    confirm_upgrade: 'Confirm upgrade the extension ({name})？',
    confirm_upgradeing: 'Extension upgrade...',
    confirm_upgrade_success: 'Extension upgrade success',

    settings_save: 'Save',
    settings_save_success: 'Save success',

    search_title: 'Extension',
    search_searchword: 'Enter keywords',
    search_status: 'Status',
    search_status_enable: 'Enable',
    search_status_disable: 'Disable',
    search_sort_asc: 'Install Asc',
    search_sort_desc: 'Install Desc',
    search_order_asc: 'Order Asc',
    search_order_desc: 'Order Desc',
    search_btn: 'Search',
    search_upload: 'Upload',
    search_install: 'Install',
    search_refresh: 'Refresh',
    table_listorder: 'Sort',
    table_installtime: 'Install time',
    table_installtime_tips: 'Install time',
    table_upgradetime: 'Upgrade time',
    table_status: 'Status',
    table_detail: 'Detail',
    table_uninstall: 'Uninstall',
    table_config: 'Config',
    table_command: 'Command',

    dialog_detail: 'Detail',
    dialog_local: 'Local',
    dialog_setting: 'Setting',
    dialog_command: 'Command',

    confirm_refresh: 'Confirm refresh the extension?',
    confirm_refresh_success: 'Refresh the extension success!',
    confirm_upload_error: "Upload success'ext must zip!",
    confirm_upload_success: 'Upload success',
    confirm_upload_force: 'Extension exists and want force?',
    confirm_sort_success: 'Sort update success',

    detail_name: 'Name',
    detail_title: 'Title',
    detail_version: 'Version',
    detail_adaptation: 'Adaptation',
    detail_description: 'Desc',
    detail_keywordlist: 'Keywords',
    detail_homepage: 'Homepage',
    detail_authorlist: 'Authors',
    detail_requires: 'Requires',
    detail_class_name: 'Class name',
    detail_installtime: 'Install time',
    detail_upgradetime: 'Upgrade time',
    detail_listorder: 'Sort',
    detail_status: 'Status',

    enable_success: 'Enable success',
    disable_success: 'Disable success',
    confirm_uninstall: 'Confirm uninstall the extension?',
    confirm_uninstalling: 'Extension uninstall...',
    confirm_uninstall_success: 'Uninstall success'
  }
}
