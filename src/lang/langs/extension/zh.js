export default {
  extension: {
    title: '扩展管理',
    local_extension: '本地扩展',

    command_alert_title: '特别注意',
    command_alert_content:
      '扩展脚本是为了非composer扩展可以下载依赖使用的脚本，如果使用composer下载的扩展可不用使用该脚本。使用"注册仓库"后只需要执行命令"composer require vendor/package"即可',
    command_title: '扩展',
    command_name: '包名',
    command_repository_register: '注册仓库',
    command_repository_register_btn: '仓库注册扩展',
    command_repository_register_tip:
      '本地扩展注册到composer.json仓库，在安装脚本不成功时使用',
    command_require_title: '安装脚本',
    command_require_tip: '扩展的composer安装脚本',
    command_repository_remove: '移除仓库',
    command_repository_remove_btn: '仓库移除扩展',
    command_repository_remove_tip:
      '本地扩展从composer.json仓库移除，在卸载脚本不成功时使用',
    command_unstall_title: '卸载脚本',
    command_unstall_tip: '扩展的composer卸载脚本',
    command_confirm_tip: '该项操作会修改composer.json文件，确认要进行操作吗？',

    table_title: '扩展',
    table_description: '简介',
    table_adaptation: '当前扩展适配系统版本',
    table_installed: '已安装',
    table_version: '当前扩展版本',
    table_new_version: '扩展可更新版本',
    table_authors: '作者',
    table_actions: '操作',
    table_install: '安装',
    table_upgrade: '更新',

    confirm_install: '确认要安装该扩展({name})吗？',
    confirm_installing: '扩展安装中...',
    confirm_install_success: '安装扩展成功',
    confirm_upgrade: '确认要更新该扩展({name})吗？',
    confirm_upgradeing: '扩展更新中...',
    confirm_upgrade_success: '更新扩展成功',

    settings_save: '提交',
    settings_save_success: '扩展配置更新成功',

    search_title: '扩展管理',
    search_searchword: '请输入关键字',
    search_status: '状态',
    search_status_enable: '启用',
    search_status_disable: '禁用',
    search_sort_asc: '安装正序',
    search_sort_desc: '安装倒序',
    search_order_asc: '排序正序',
    search_order_desc: '排序倒序',
    search_btn: '搜索',
    search_upload: '上传扩展',
    search_install: '安装/更新',
    search_refresh: '刷新',
    table_listorder: '排序',
    table_installtime: '安装时间',
    table_installtime_tips: '扩展安装时间',
    table_upgradetime: '扩展最后更新时间',
    table_status: '状态',
    table_detail: '详情',
    table_uninstall: '卸载',
    table_config: '配置',
    table_command: '脚本',

    dialog_detail: '扩展详情',
    dialog_local: '本地扩展',
    dialog_setting: '扩展设置',
    dialog_command: '扩展脚本',

    confirm_refresh: '确认要刷新本地扩展缓存吗？',
    confirm_refresh_success: '刷新本地扩展缓存成功！',
    confirm_upload_error: '上传扩展只能是zip格式！',
    confirm_upload_success: '扩展上传成功',
    confirm_upload_force: '扩展已经存在，是否上传覆盖？',
    confirm_sort_success: '扩展排序更新成功',

    detail_name: '扩展包名',
    detail_title: '扩展名称',
    detail_version: '当前版本',
    detail_adaptation: '适配系统版本',
    detail_description: '描述',
    detail_keywordlist: '关键字',
    detail_homepage: '项目主页',
    detail_authorlist: '作者',
    detail_requires: '依赖扩展',
    detail_class_name: '扩展绑定类',
    detail_installtime: '安装时间',
    detail_upgradetime: '最后更新',
    detail_listorder: '排序',
    detail_status: '激活状态',

    enable_success: '扩展启用成功',
    disable_success: '扩展禁用成功',
    confirm_uninstall: '确认要卸载该扩展吗？',
    confirm_uninstalling: '扩展卸载中...',
    confirm_uninstall_success: '卸载扩展成功'
  }
}
