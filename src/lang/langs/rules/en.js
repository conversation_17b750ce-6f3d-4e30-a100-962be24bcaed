const translate = {
  'Get Larke Admin Detail': 'Get Larke Admin Detail',
  'Remove Telegram Group Role': 'Remove Telegram Group Role',
  'Update Larke Admin': 'Update Larke Admin',
  'Create Larke Admin': 'Create Larke Admin',
  'Get Telegram Group Role List': 'Get Telegram Group Role List',
  'Get Telegram Group Detail': 'Get Telegram Group Detail',
  'Add Telegram Group Role': 'Add Telegram Group Role',
  'Wallet List': 'Wallet List',
  'Wallet Controller': 'Wallet Controller',
  CurrencyController: 'Currency Controller',
  'Assign Telegram Group': 'Assign Telegram Group',
  'Is Super Admin': 'Is Super Admin',
  TelegramGroupController: 'Telegram Group Controller',
  'Telegram Group User List': 'Telegram Group User List',
  'List Of Budget Incomes': 'List Of Budget Incomes',
  'Budget Incomes': 'Budget Incomes',
  'Daily Expense Report': 'Daily Expense Report',
  'List Telegram Groups': 'List Telegram Groups',
  'List Budget Categories': 'List Budget Categories',
  'Telegram Api': 'Telegram API',
  'Budget Categories': 'Budget Categories',
  'Category Controller': 'Category Controller',
  'Category List': 'Category List',
  'Expense Category': 'Expense Category',
  'Expense Currency': 'Expense Currency',
  ExpenseController: 'Expense Controller',
  'ExpenseReport By Currency': 'Expense Report By Currency',
  'Report Controller': 'Report Controller',
  'Expense List': 'Expense List',
  'Update Currency': 'Update Currency',
  'List Currency': 'List Currency',
  'Get Currency Detail': 'Get Currency Detail',
  'Delete Currency': 'Delete Currency',
  'Create Currency': 'Create Currency',
  菜单删除: 'Delete Menu',
  菜单列表: 'Menu List',
  菜单获取全部: 'Get All Menus',
  菜单子列表: 'Menu Sublist',
  菜单树: 'Menu Tree',
  菜单创建: 'Create Menu',
  菜单树列表: 'Menu Tree List',
  菜单更新: 'Update Menu',
  菜单管理: 'Menu Management',
  清除缓存: 'Clear Cache',
  菜单保存全部: 'Save All Menus',
  扩展禁用: 'Disable Extension',
  分组删除: 'Delete Group',
  管理员权限: 'Admin Permissions',
  扩展上传: 'Upload Extension',
  管理员授权: 'Admin Authorization',
  附件下载码: 'Attachment Download Code',
  登陆: 'Login',
  分组启用: 'Enable Group',
  分组禁用: 'Disable Group',
  权限删除: 'Delete Permission',
  配置全部列表: 'Configuration Full List',
  语言设置: 'Language Settings',
  分组详情: 'Group Details',
  扩展排序: 'Sort Extensions',
  配置详情: 'Configuration Details',
  配置列表: 'Configuration List',
  扩展配置: 'Extension Configuration',
  权限更新: 'Update Permissions',
  权限排序: 'Sort Permissions',
  获取分组: 'Get Group',
  管理员禁用: 'Disable Admin',
  分组列表: 'Group List',
  权限列表配置: 'Permission List Configuration',
  附件删除: 'Delete Attachment',
  权限启用: 'Enable Permissions',
  设置缓存: 'Set Cache',
  个人信息: 'Personal Information',
  管理员: 'Admin',
  验证码: 'Verification Code',
  附件列表: 'Attachment List',
  分组子列表: 'Group Sublist',
  管理员列表: 'Admin List',
  权限禁用: 'Disable Permissions',
  附件上传: 'Upload Attachment',
  分组更新: 'Update Group',
  退出: 'Logout',
  系统详情: 'System Details',
  附件详情: 'Attachment Details',
  我的信息: 'My Information',
  刷新扩展: 'Refresh Extension',
  分组树结构: 'Group Tree Structure',
  公钥: 'Public Key',
  系统: 'System',
  管理员启用: 'Enable Admin',
  扩展启用: 'Enable Extension',
  修改头像: 'Change Avatar',
  权限清空: 'Clear Permissions',
  扩展列表: 'Extension List',
  修改密码: 'Change Password',
  管理员删除: 'Delete Admin',
  语言包: 'Language Package',
  分组授权: 'Group Authorization',
  附件下载: 'Download Attachment',
  本地扩展命令: 'Local Extension Command',
  配置删除: 'Delete Configuration',
  附件启用: 'Enable Attachment',
  上传文件: 'Upload File',
  配置禁用: 'Disable Configuration',
  重设权限缓存: 'Reset Permission Cache',
  附件: 'Attachment',
  分组添加: 'Add Group',
  配置排序: 'Sort Configuration',
  配置更新: 'Update Configuration',
  获取配置数组: 'Get Configuration Array',
  扩展卸载: 'Uninstall Extension',
  修改我的信息: 'Update My Information',
  仓库注册扩展: 'Register Extension In Repository',
  管理员退出: 'Admin Logout',
  扩展更新: 'Update Extension',
  仓库移除扩展: 'Remove Extension From Repository',
  配置启用: 'Enable Configuration',
  扩展: 'Extension',
  本地扩展: 'Local Extension',
  配置: 'Configuration',
  附件禁用: 'Disable Attachment',
  扩展安装: 'Install Extension',
  更新配置: 'Update Configuration',
  管理分组: 'Manage Group',
  分组排序: 'Sort Group',
  刷新token: 'Refresh Token',
  配置添加: 'Add Configuration',
  权限详情: 'Permission Details',
  权限树结构: 'Permission Tree Structure',
  权限列表: 'Permission List',
  权限添加: 'Add Permission',
  权限子列表: 'Permission Sublist',
  权限: 'Permission'
}

export default {
  ...translate
}
