const translate = {
  system: {
    route: {
      dashboard: 'Dashboard',
      profile: 'Profile',
      icons: 'Icons',

      system: 'System',
      setting: 'Setting',
      config: 'Config',
      // menu: 'Menu',

      attachment: 'Attachment',

      permission: 'Permission',
      admin: 'Admin',
      adminIndex: 'Admin Index',
      authGroup: 'Auth Group',
      authGroupTree: 'Auth Group Tree',
      authRule: 'Auth Rule',
      authRuleTree: 'Auth Rule Tree',
      adminLog: 'Admin Log',

      localExtension: 'Local Extension',
      extension: 'Extension',

      gameManagement: 'Game Management',
      gameConfig: 'Game Config',
      gameConfigOverview: 'Game Config Overview',
      diceConfig: 'Dice Config',
      diceClientConfig: 'Dice Client Config',
      plinkoConfig: 'Plinko Config',
      plinkoClientConfig: 'Plinko Client Config',
      europeanRouletteConfig: 'European Roulette Config',
      europeanRouletteClientConfig: 'European Roulette Client Config',
      doubleConfig: 'Double Config',
      doubleClientConfig: 'Double Client Config',
      rouletteConfig: 'Roulette Config',
      rouletteClientConfig: 'Roulette Client Config',
      kenoConfig: 'Keno Config',
      kenoClientConfig: 'Keno Client Config',
      crashConfig: 'Crash Config',
      crashClientConfig: 'Crash Client Config',
      ringConfig: 'Ring Config',
      ringClientConfig: 'Ring Client Config',
      minesConfig: 'Mines Config',
      minesClientConfig: 'Mines Client Config',
      fruitSlotsConfig: 'Fruit Slots Config',
      fruitSlotsClientConfig: 'Fruit Slots Client Config',
      massKenoConfig: 'Mass Keno Config',
      massKenoClientConfig: 'Mass Keno Client Config',
      oddstraderConfig: 'Oddstrader Config',
      oddstraderClientConfig: 'Oddstrader Client Config',

      member: 'Member',
      team: 'Team',
      team_list: 'Team',
      category: 'Category',
      category_list: 'Category',
      product: 'Product',
      product_list: 'Product',
      news: 'News',
      list_news: 'List News',
      finance: 'Finance',
      transaction_list: 'Transaction List',
      commission_list: 'Commission List',
      rebate_list: 'Rebate List',
      settings: 'App Settings',
      announcements: 'Announcements',
      announcements_list: 'Announcements',
      activities: 'Activities',
      activityTransactions: 'Activity Transactions',
      activityManagement: 'Activity Management',
      user_reward: 'Daily Sign-in Reward',
      withdraw_list: 'Withdraw List',
      activityTransactionsReport: 'Activity Report',
      reports: 'Reports',
      global_report: 'Global Report',
      deposit_list: 'Deposit List',
      payment_channel: 'Payment Channels',
      otp: 'OTP',
      login_popup: 'Login Popup',
      lucky_spin: 'Lucky Spin',
      luckyspin_result: 'Lucky Spin Result',
      wallet_summary: 'Wallet Profit Summary',
      deposit_request_list: 'Deposit Request List',
      order_list: 'Purchase History',
      bank_account_management: 'Bank Account Management',
      payment_channel_deposit_summary: 'Payment Channel Deposit',
      user_level: 'User VIP Level',
      promotion_cards: 'Promotion Cards',
      loan_request_list: 'Loan Requests',
      savingSummary: 'Saving Summary',
      saving_summary: 'Saving Summary',
      saving_list: 'Saving List',
      coupons: 'Coupons',
      delivery_services: 'Delivery Services',
      pages: 'Pages',
      reward: 'Rewards',
      banner_sliders: 'Banner Sliders',
      appointments_list: 'Appointments',
      operation_service: 'Operation Service',
      payment_organizations: 'Payment Organization',
      refunds: 'Refund',
      free_store: 'Free Store',
      account: 'Account',
      transaction: 'Transaction',
      menu: {
        accounts: {
          player_list: 'Player List',
          black_list: 'Black List',
          general_agents: 'General Agents',
          local_agents: 'Local Agents',
          login_logs: 'Login Logs',
          operation_logs: 'Operation Logs',
          regional_agents: 'Regional Agents',
          risk_control: 'Risk Control'
        },
        transaction: {
          fund_log: 'Fund Log',
          betting_log: 'Betting Log',
          data_export: 'Data Export',
          liquidation_report: 'Liquidation Report',
          player_report: 'Player Report',
          sale_report: 'Sale Report',
          single_wallet_log: 'Single Wallet Log'
        }

      }
    },
    navbar: {
      dashboard: 'Dashboard',
      github: 'Github',
      logOut: 'Log Out',
      profile: 'Profile',
      theme: 'Theme',
      size: 'Global Size'
    },
    login: {
      title: 'System Login',
      logIn: 'Login',
      username: 'Username',
      password: 'Password',
      captcha: 'Captcha',
      refreshCaptcha: 'Refresh Captcha',
      refreshCaptchaTip: 'Go Refresh Captcha',
      two_fa: 'Two Factor Verification',
      rules_username_required: 'Please enter username',
      rules_password_required: 'Password must min 6 bytes',
      rules_captcha_required: 'Captcha must equal 4 bytes',
      two_fa_required: 'Two Factor Verification is required'
    },
    tagsView: {
      refresh: 'Refresh',
      close: 'Close',
      closeOthers: 'Close Others',
      closeAll: 'Close All'
    },
    settings: {
      title: 'Page style setting',
      theme: 'Theme Color',
      tagsView: 'Open Tags-View',
      fixedHeader: 'Fixed Header',
      sidebarLogo: 'Sidebar Logo'
    },

    err401: {
      title: '你没有权限去该页面',
      content: '你可以进行以下操作解决你的问题',
      relogin: '退出重新登录'
    },

    err404: {
      content:
        'Please check that the URL you entered is correct, or click the button below to return to the homepage.'
    },

    common: {
      download: 'Download',
      more: 'More',
      bet_date: 'Bet date',
      total_players: 'Total players',
      total_bet: 'Total bet',
      win_bet: 'Win bet',
      lose_bet: 'Lose bet',
      profit_and_loss: 'Profit and loss %',
      win_rate: 'Win rate',
      player_name: 'Player name',
      bet_id: 'Bet ID',
      bet_amount: 'Bet Amount',
      winning_amount: 'Winning Amount',
      winning_status: 'Winning Status',
      number: 'Number',
      probability: 'Probability',
      payouts: 'Payouts',
      bonus: 'Bonus',
      max_prize: 'Max Prize',
      index_position: 'Index Position',
      fruit: 'Fruit',
      gameIndex: 'Game Index',
      lucky_draw: 'Lucky Draw',
      color_mark: 'Color Mark',
      possibility: 'Possibility',
      multiplier: 'Multiplier',
      operation: 'Operation',
      go_back: 'Go back',
      tips: 'Tips',
      ok: 'Ok',
      cancel: 'Cancel',
      save: 'Save',

      search: 'Search',
      reset: 'Reset',

      enable: 'Enable',
      disable: 'Disable',

      detail: 'Detail',
      edit: 'Edit',
      delete: 'Delete',

      goto_top: 'GoTop',

      clear_cache: 'Clear Cache',
      clear_cache_success: 'Clear Cache Success',
      confirm_clear_cache: 'You will clear Cache?',
      clear_cache_doing: 'Clearing Cache...',

      confirm_logout: 'Confirm logout?',

      close_page: 'Close',
      last_week: 'Last Week',
      last_month: 'Last Month',
      last_3_months: 'Last 3 Months',
      this_week: 'This Week',
      this_month: 'This Month',
      lastweek: 'Last Week',
      lastmonth: 'Last Month',
      last3months: 'Last 3 Months',
      thisweek: 'This Week',
      thismonth: 'This Month',
      to: 'To',
      select_file: 'Select File',
      image_size_tip: 'jpg/png files with a size less than 500kb',
      change_file: 'Change File',
      refresh: 'Refresh',
      add: 'Add',
      type: 'Type',
      login: 'Login',
      sign_in: 'Sign In',
      today: 'Today',
      yesterday: 'Yesterday',
      last_6_months: 'Last 6 Months',
      action: 'Action',
      configure: 'Configure',
      registration: 'Registration',
      name: 'Name',
      cycle: 'Cycle',
      status: 'Status',
      start_date: 'Start Date',
      end_date: 'End Date',
      submit: 'Submit',
      one_time: 'One Time',
      daily: 'Daily',
      amount: 'Amount',
      please_input: 'Please Input ',
      please_select: 'Please Select ',
      wallet_type: 'Wallet Type',
      deposit: 'Deposit',
      reward: 'Reward',
      commission: 'Commission',
      promotion: 'Promotion',
      wallet: 'Wallet',
      balance: 'Balance',
      profit: 'Profit',
      please_select_date: 'Please Select Date',
      start_date_must_be_earlier_than_end_date: 'Start Date must be earlier than End Date',
      create_successfully: 'Create Successfully',
      edit_successfully: 'Edit Successfully',
      edit_failed: 'Edit Failed',
      preview: 'Preview',
      yes: 'Yes',
      no: 'No',
      recommended: 'Recommended',
      percentage: 'Percentage',
      fixed: 'Fixed',
      sort: 'Sort Order',
      confirm_delete: 'Confirm delete that item?',
      confirm_delete_success: 'Delete Success',
      any_time: 'Any Time',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday',
      return_value_type_required: 'Return Value Type is required',
      no_negative_numbers: 'No negative numbers',
      please_fill_required_fields: 'Please fill in the required fields',
      confirm_cancel: 'Are you sure you want to cancel this item?',
      confirm_delete_item: 'Are you sure you want to delete this item?',
      all: 'All',
      confirm: 'Confirm',
      saving: 'Saving',
      maximum_saving_days: 'Max Saving Days',
      release_date: 'Release Date',
      daily_reward: 'Daily Reward',
      consecutive_day: 'Consecutive Day',
      consecutive_reward: 'Consecutive Reward',
      referral: 'Referral',
      review: 'Review'
    },
    filters: {
      date_range: 'Time Range'
    }
  },

  generic: {
    created_at: 'Created At',
    processed_at: 'Processed At',
    available_at: 'Available At',
    updated_at: 'Updated At',
    save: 'Save',
    close: 'Close',
    active: 'Active',
    inactive: 'Inactive',
    processed: 'Processed',
    processing: 'Processing',
    pending: 'Pending',
    cancelled: 'Cancelled'
  }
}
export default {
  ...translate.system,
  ...translate.generic
}
