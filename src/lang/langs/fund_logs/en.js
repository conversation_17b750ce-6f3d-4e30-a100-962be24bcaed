export default {
  fund_logs: {
    header_title: 'Fund Logs',
    // Table columns
    id: 'ID',
    client_id: 'Client ID',
    player_id: 'Player ID',
    player_username: '<PERSON>rna<PERSON>',
    type: 'Type',
    amount: 'Amount',
    currency: 'Currency',
    balance_before: 'Before',
    balance_after: 'After',
    description: 'Description',
    created_at: 'Created At',
    updated_at: 'Updated At',

    // Filter labels
    filter_client_id: 'Client ID',
    filter_player_id: 'Player ID',
    filter_player_username: 'Player Username',
    filter_type: 'Transaction Type',
    filter_currency: 'Currency',
    filter_amount_min: 'Min Amount',
    filter_amount_max: 'Max Amount',
    filter_date_from: 'Date From',
    filter_date_to: 'Date To',
    filter_created_at_from: 'Created From',
    filter_created_at_to: 'Created To',

    // Filter placeholders
    client_id_placeholder: 'Enter client ID',
    player_id_placeholder: 'Enter player ID',
    player_username_placeholder: 'Search by username',
    type_placeholder: 'Select transaction type',
    currency_placeholder: 'Select currency',
    amount_min_placeholder: 'Minimum amount',
    amount_max_placeholder: 'Maximum amount',
    date_from_placeholder: 'Start date',
    date_to_placeholder: 'End date',
    created_at_from_placeholder: 'Start datetime',
    created_at_to_placeholder: 'End datetime',

    // Transaction types
    types: {
      bet: 'Bet',
      winning: 'Winning',
      won: 'Won'
    },

    // Buttons
    search_btn: 'Search',
    reset_btn: 'Reset',
    export_btn: 'Export',

    // Messages
    fetch_error: 'Failed to fetch fund logs',
    fetch_filter_options_error: 'Failed to fetch filter options',
    no_data: 'No fund logs found',
    loading: 'Loading fund logs...',

    // Validation messages
    amount_range_error: 'Maximum amount must be greater than or equal to minimum amount',
    date_range_error: 'End date must be greater than or equal to start date',
    date_range_limit_error: 'Date range cannot exceed 365 days',

    // Statistics
    statistics: {
      total_logs: 'Total Logs',
      logs_today: 'Today',
      logs_this_week: 'This Week',
      logs_this_month: 'This Month',
      unique_players: 'Unique Players',
      unique_clients: 'Unique Clients',
      total_bet_amount: 'Total Bet Amount',
      total_winning_amount: 'Total Winning Amount'
    }
  }
}
