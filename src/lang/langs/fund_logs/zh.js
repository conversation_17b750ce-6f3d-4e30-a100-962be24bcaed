export default {
  fund_logs: {
    header_title: '资金日志',
    // Table columns
    id: 'ID',
    client_id: '客户端ID',
    player_id: '玩家ID',
    player_username: '用户名',
    type: '类型',
    amount: '金额',
    currency: '货币',
    balance_before: '交易前',
    balance_after: '交易后',
    description: '描述',
    created_at: '创建时间',
    updated_at: '更新时间',

    // Filter labels
    filter_client_id: '客户端ID',
    filter_player_id: '玩家ID',
    filter_player_username: '玩家用户名',
    filter_type: '交易类型',
    filter_currency: '货币',
    filter_amount_min: '最小金额',
    filter_amount_max: '最大金额',
    filter_date_from: '开始日期',
    filter_date_to: '结束日期',
    filter_created_at_from: '创建开始时间',
    filter_created_at_to: '创建结束时间',

    // Filter placeholders
    client_id_placeholder: '输入客户端ID',
    player_id_placeholder: '输入玩家ID',
    player_username_placeholder: '按用户名搜索',
    type_placeholder: '选择交易类型',
    currency_placeholder: '选择货币',
    amount_min_placeholder: '最小金额',
    amount_max_placeholder: '最大金额',
    date_from_placeholder: '开始日期',
    date_to_placeholder: '结束日期',
    created_at_from_placeholder: '开始时间',
    created_at_to_placeholder: '结束时间',

    // Transaction types
    types: {
      bet: '投注',
      winning: '中奖',
      won: '已中奖'
    },

    // Buttons
    search_btn: '搜索',
    reset_btn: '重置',
    export_btn: '导出',

    // Messages
    fetch_error: '获取资金日志失败',
    fetch_filter_options_error: '获取筛选选项失败',
    no_data: '未找到资金日志',
    loading: '正在加载资金日志...',

    // Validation messages
    amount_range_error: '最大金额必须大于或等于最小金额',
    date_range_error: '结束日期必须大于或等于开始日期',
    date_range_limit_error: '日期范围不能超过365天',

    // Statistics
    statistics: {
      total_logs: '总日志数',
      logs_today: '今日',
      logs_this_week: '本周',
      logs_this_month: '本月',
      unique_players: '独立玩家',
      unique_clients: '独立客户端',
      total_bet_amount: '总投注金额',
      total_winning_amount: '总中奖金额'
    }
  }
}
