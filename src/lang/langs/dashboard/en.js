export default {
  dashboard: {
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursdday: 'Thursdday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday',
    morning: 'Morning',
    afternoon: 'Afternoon',
    night: 'Night',
    midnight: 'Midnight',
    now_time: '{nickname} {nowTimeCall} hello，Now time：{nowTime}',
    now_time_show: '{year}-{month}{day} {hour}:{minute}:{second} {week}',
    admins: 'Admins',
    attachments: 'Attachments',
    extensions: 'Extensions',
    groups: 'Groups',
    system_info: 'System Info',
    version: 'Version',
    domain_ip: 'Server domain/ip',
    server_info: 'Server Info',
    server_env: 'Server Env',
    php_version: 'PHP Version',
    mysql_version: 'MySQL Version',
    max_upload: 'Max Upload',
    server_time: 'Server Time',
    register_user: 'New Register',
    active_user: 'Active User',
    total_deposit: 'Total Deposit',
    total_purchase: 'Total Purchase',
    total_withdraw: 'Total Withdraw',
    total_member_all_time: 'All User',
    hourly_register: 'New Members',
    total_amount: 'Total Amount',
    transaction_count: 'Total Transaction',
    unique_users: 'Unique Users',
    deposit: {
      title: 'Deposit'
    },
    order: {
      title: 'Product Purchased'
    },
    withdraw: {
      title: 'Withdraw'
    },
    register: {
      title: 'New Register User'
    },

    // Summary Report Chart
    summary_report_title: 'Summary Report',
    summary_report_chart_title: 'Performance Overview',
    select_time_range: 'Select Range',
    last_7_days: 'Last 7 Days',
    last_14_days: 'Last 14 Days',
    last_30_days: 'Last 30 Days',
    loading_chart_data: 'Loading chart data...',
    chart_error_title: 'Chart Error',
    chart_error_message: 'Failed to load chart data. Please try again.',
    no_data_title: 'No Data Available',
    no_data_description: 'No data available for the selected time range.',
    bet_amount: 'Bet Amount',
    total_profit: 'Total Profit',
    retention_report: {
      title: 'Retention rate of valid Player',
      '3days': '3 days Retention',
      '7days': '7 days Retention',
      '14days': '14 days Retention',
      '21days': '21 days Retention',
      '30days': '30 days Retention'
    },
    bet: {
      title: 'Average bet amount of valid Player',
      bet_amount: 'Bet Amount'
    },
    win_lost: {
      title: 'Average Win/Lost of valid Player',
      win_lost: 'Win/Lost'
    },
    today: 'Today',
    last_seven_days: 'Last 7 Days',
    last_twenty_one_days: 'Last 21 Days',
    last_thirty_days: 'Last 30 Days'
  }
}
