export default {
  payment_channel: {
    header_title: 'Payment Channel List',
    add_payment_channel: 'Create Payment Channel',
    create_title: 'Create Payment Channel',
    edit_title: 'Update Payment Channel',
    provider: 'Payment Service Provider',
    name: 'Payment Channel Name',
    active: 'Active',
    for_deposit: 'For Deposit',
    for_withdraw: 'For Withdraw',
    logo: 'Payment Channel Logo',
    minimum_amount: 'Minimum Deposit Amount',
    maximum_amount: 'Maximum Deposit Amount',
    sort: 'Sort Order',
    merchant_name: 'Merchant Name',
    merchant_private_key: 'Merchant Private Key',
    merchant_public_key: 'Merchant Public Key',
    platform_private_key: 'Platform Private Key',
    platform_public_key: 'Platform Public Key',
    sub_merchant_name: 'Sub-Merchant Name',
    extend_prop_1: 'Extended Property 1',
    extend_prop_2: 'Extended Property 2',
    extend_prop_3: 'Extended Property 3',
    extend_prop_4: 'Extended Property 4',
    extend_config: 'Extent Config',
    merchant_config: 'Merchant Config',
    input: {
      header_title: 'Input Payment Channel List',
      add_payment_channel: 'Input Create Payment Channel',
      name: 'Input Name',
      logo: 'Input Logo',
      minimum_amount: 'Input Minimum Amount',
      maximum_amount: 'Input Maximum Amount',
      sort: 'Input Sort',
      merchant_name: 'Input Merchant Name',
      merchant_private_key: 'Input Merchant Private Key',
      merchant_public_key: 'Input Merchant Public Key',
      platform_private_key: 'Input Platform Private Key',
      platform_public_key: 'Input Platform Public Key',
      sub_merchant_name: 'Input Sub-Merchant Name',
      extend_prop_1: 'Input Extended Property 1',
      extend_prop_2: 'Input Extended Property 2',
      extend_prop_3: 'Input Extended Property 3',
      extend_prop_4: 'Input Extended Property 4'
    },
    min_user_vip_level: 'Minimum User VIP Level',
    any_level: 'Any Level',
    service_provider: 'Service Provider'
  },
  payment_organization: {
    name: 'Payment Organization Name',
    description: 'Payment Organization Description',
    logo: 'Payment Organization Logo',
    header_title: 'Payment Organization',
    update_title: 'Update Payment Organization',
    create_title: 'Create Payment Organization'
  }
}
