export default {
  payment_channel: {
    header_title: '支付渠道列表',
    add_payment_channel: '创建支付渠道',
    create_title: '创建支付渠道',
    edit_title: '更新支付渠道',
    provider: '支付渠道',
    name: '支付方名称',
    active: '活躍',
    for_deposit: '用於存款',
    for_withdraw: '用於提款',
    logo: '支付方图标',
    minimum_amount: '最低存款金额',
    maximum_amount: '最高存款金额',
    sort: '排序',
    merchant_name: '商户号',
    merchant_private_key: '商户私钥',
    merchant_public_key: '商户公钥',
    platform_private_key: '平台私钥',
    platform_public_key: '平台公钥',
    sub_merchant_name: '子商户号',
    extend_prop_1: '拓展参数1',
    extend_prop_2: '拓展参数2',
    extend_prop_3: '拓展参数3',
    extend_prop_4: '拓展参数4',
    extend_config: '扩展配置',
    merchant_config: '商户配置',
    input: {
      header_title: '输入支付渠道列表',
      add_payment_channel: '输入创建支付渠道',
      name: '输入名称',
      logo: '输入标志',
      minimum_amount: '输入最低金额',
      maximum_amount: '输入最高金额',
      sort: '输入排序',
      merchant_name: '输入商户名称',
      merchant_private_key: '输入商户私钥',
      merchant_public_key: '输入商户公钥',
      platform_private_key: '输入平台私钥',
      platform_public_key: '输入平台公钥',
      sub_merchant_name: '输入子商户名称',
      extend_prop_1: '输入扩展属性 1',
      extend_prop_2: '输入扩展属性 2',
      extend_prop_3: '输入扩展属性 3',
      extend_prop_4: '输入扩展属性 4'
    },
    min_user_vip_level: '最低用户VIP等级',
    any_level: '任何等级',
    service_provider: '渠道方名称'
  },
  payment_organization: {
    name: '支付组织名称',
    description: '支付组织描述',
    logo: '支付组织徽标',
    header_title: '支付组织',
    update_title: '更新支付组织',
    create_title: '创建支付组织'
  }
}
