export default {
  admin: {
    access_title: 'Admin',
    access_group: 'Group',
    access_success: 'Access success',

    select_avatar: 'Select avatar',

    form_passport: 'Passport',
    form_enter_passport: 'Enter passport',
    form_nickname: 'Nickname',
    form_enter_nickname: 'Enter nickname',
    form_email: '<PERSON><PERSON>',
    form_enter_email: 'Enter email',
    form_group: 'Group',
    form_need_one_group: 'Need one group',
    form_introduce: 'Introduce',
    form_enter_introduce: 'Enter introduce',
    form_avatar: 'Avatar',
    form_status: 'Status',
    form_status_enable: 'Enable',
    form_status_disable: 'Disable',

    rules_name_required: 'Name required',
    rules_nickname_required: 'Nickname required',
    rules_email_required: 'Email required',
    rules_group_id_required: 'Group ID required',
    rules_introduce_required: 'Introduce required',

    create_success: 'Create success',
    update_success: 'Update success',

    search_title: 'Admin',
    search_searchword: 'Enter keywords',
    search_status: 'Status',
    search_btn: 'Search',
    search_create_passport: 'Create',
    search_clear_cache: 'Refresh',
    search_passport_logout: 'Logout',

    create_regional: 'Create Region Agent',
    create_local: 'Create Local Agent',

    table_name: 'Name',
    table_nickname: 'Nickname',
    table_create_time: 'Created Time',
    table_status: 'Status',
    table_actions: 'Actions',
    table_setting_group: 'Setting Group',
    table_password: 'Password',
    table_detail: 'Detail',
    table_update: 'Update',
    table_delete: 'Delete',
    table_enable: 'Enable',
    table_disable: 'Disable',
    table_asc: 'Asc',
    table_desc: 'Desc',
    table_enable_success: 'Enable success',
    table_disable_success: 'Disable success',

    dialog_detail: 'Detail',
    dialog_create: 'Create',
    dialog_edit: 'Edit',
    dialog_password: 'Password',
    dialog_password_newpassword: 'New password',
    dialog_Logout: 'Logout',
    dialog_logout_refreshToken: 'Refresh token',
    dialog_logout_tips: 'Put refresh token to blocklist',
    dialog_logout_ok: 'Logout',
    dialog_access: 'Access group',

    detail_id: 'ID',
    detail_name: 'Name',
    detail_nickname: 'Nickname',
    detail_email: 'Email',
    detail_introduce: 'Introduce',
    detail_groups: 'Groups',
    detail_avatar: 'Avatar',
    detail_create_time: 'Join Time',
    detail_last_active: 'Last Active',
    detail_last_ip: 'Last Ip',
    detail_status: 'Status',

    confirm_delete: 'Confirm delete admin?',
    confirm_delete_success: 'Delete admin success',
    password_not_empty: 'Password not empty',
    password_change_success: 'Change password success',
    confirm_update_cache: 'Confirm update cache?',
    confirm_updateing_cache: 'Updating cache...',
    confirm_update_cache_success: 'Update cache success',
    refreshToken_not_empty: 'Refresh token not empty',
    logout_success: 'Logout success',

    // Rebate setting translations
    rebate_setting: 'Rebate Setting',
    rebate_configuration: 'Rebate Configuration',
    range_min: 'Range Min',
    range_max: 'Range Max',
    rate: 'Rate',
    range_min_required: 'Range Min is required',
    range_min_validation: 'Range Min must be greater than or equal to 0',
    range_max_required: 'Range Max is required',
    range_max_validation: 'Range Max must be greater than or equal to 0',
    range_max_hint: 'Leave empty for unlimited upper range',
    rate_required: 'Rate is required',
    rate_validation: 'Rate must be between 0.01 and 1.00',
    rate_hint: 'Commission rate (0.01 to 1.00)',
    rebate_load_error: 'Failed to load rebate settings',
    rebate_update_success: 'Rebate settings updated successfully',
    rebate_update_error: 'Failed to update rebate settings'
  }
}
