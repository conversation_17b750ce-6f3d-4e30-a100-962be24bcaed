export default {
  admin: {
    access_title: '管理员账号',
    access_group: '分组',
    access_success: '账号授权成功',

    select_avatar: '选择头像',

    form_passport: '账号',
    form_enter_passport: '请填写账号',
    form_nickname: '昵称',
    form_enter_nickname: '请填写昵称',
    form_email: '邮箱',
    form_enter_email: '请填写邮箱',
    form_group: '分组',
    form_need_one_group: '请选择至少一个分组',
    form_introduce: '简介',
    form_enter_introduce: '请填写简介',
    form_avatar: '头像',
    form_status: '状态',
    form_status_enable: '启用',
    form_status_disable: '禁用',

    rules_name_required: '账号不能为空',
    rules_nickname_required: '昵称不能为空',
    rules_email_required: '邮箱不能为空',
    rules_group_id_required: '所属分组不能为空',
    rules_introduce_required: '简介不能为空',

    create_success: '添加管理员成功',
    update_success: '编辑管理员信息成功',

    search_title: '管理员',
    search_searchword: '请输入关键字',
    search_status: '状态',
    search_btn: '搜索',
    search_create_passport: '添加账号',
    search_clear_cache: '更新权限缓存',
    search_passport_logout: '账号退出',

    create_regional: '创建区域代理',
    create_local: '创建本地代理',

    table_name: '账号',
    table_nickname: '昵称',
    table_create_time: '添加时间',
    table_status: '状态',
    table_actions: '操作',
    table_setting_group: '设置分组',
    table_password: '密码',
    table_detail: '详情',
    table_update: '编辑',
    table_delete: '删除',
    table_enable: '启用',
    table_disable: '禁用',
    table_asc: '正序',
    table_desc: '倒序',
    table_enable_success: '管理员启用成功',
    table_disable_success: '管理员禁用成功',

    dialog_detail: '账号详情',
    dialog_create: '添加账号',
    dialog_edit: '编辑账号',
    dialog_password: '更改密码',
    dialog_password_newpassword: '新密码',
    dialog_logout: '退出账号',
    dialog_logout_refreshToken: '账号的刷新令牌',
    dialog_logout_tips: '退出账号主要是将刷新令牌加入到黑名单，防止再次登录',
    dialog_logout_ok: '确认退出',
    dialog_access: '设置分组',

    detail_id: 'ID',
    detail_name: '账号',
    detail_nickname: '昵称',
    detail_email: '邮箱',
    detail_introduce: '简介',
    detail_groups: '分组',
    detail_avatar: '头像',
    detail_create_time: '加入时间',
    detail_last_active: '最近活动',
    detail_last_ip: '最近活动IP',
    detail_status: '激活状态',

    confirm_delete: '确认要删除该管理员吗？',
    confirm_delete_success: '删除管理员成功',
    password_not_empty: '密码不能为空',
    password_change_success: '管理员密码修改成功',
    confirm_update_chache: '确认要更新权限缓存吗？',
    confirm_updateing_cache: '更新权限缓存中...',
    confirm_update_chache_success: '更新权限缓存成功',
    refreshToken_not_empty: '刷新令牌不能为空',
    logout_success: '管理员退出成功',

    // 返利设置翻译
    rebate_setting: '返利设置',
    rebate_configuration: '返利配置',
    range_min: '最小范围',
    range_max: '最大范围',
    rate: '费率',
    range_min_required: '最小范围是必需的',
    range_min_validation: '最小范围必须大于或等于0',
    range_max_required: '最大范围是必需的',
    range_max_validation: '最大范围必须大于或等于0',
    range_max_hint: '留空表示无上限',
    rate_required: '费率是必需的',
    rate_validation: '费率必须在0.01到1.00之间',
    rate_hint: '佣金费率 (0.01 到 1.00)',
    rebate_load_error: '加载返利设置失败',
    rebate_update_success: '返利设置更新成功',
    rebate_update_error: '更新返利设置失败'
  }
}
