.filter{
    &-container{
        display: flex;
        margin-bottom: 10px;
    }
    &-item{
        span{
            margin-right:5px;
        }
        display: flex;
        align-items: center;
    }
}

.el-table__header {
  text-transform: capitalize;
  word-break: break-word;
}

.el-table .cell {
  word-break: break-word;
}

.report_list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  margin-bottom: 20px
}

.report_title {
  text-transform: capitalize;
  color: #fff;
  font-weight: 550
}

.report_content {
  color: #fff;
  margin-top: 12px
}

.report_list_content1,.report_list_content8 {
  background: -webkit-gradient(linear,left top,right top,from(#fc3b58),to(#fe828c));
  background: linear-gradient(90deg,#fc3b58,#fe828c)
}

.report_list_content2 {
  background: -webkit-gradient(linear,left top,right top,from(#fbab2e),to(#ffbd31));
  background: linear-gradient(90deg,#fbab2e,#ffbd31)
}

.report_list_content3,.report_list_content7 {
  background: -webkit-gradient(linear,left top,right top,from(#1ec283),to(#67dfb9));
  background: linear-gradient(90deg,#1ec283,#67dfb9)
}

.report_list_content4 {
  background: -webkit-gradient(linear,left top,right top,from(#1e8ae5),to(#21c5fa));
  background: linear-gradient(90deg,#1e8ae5,#21c5fa)
}

.report_list_content5 {
  background: -webkit-gradient(linear,left top,right top,from(#934fe4),to(#c687fa));
  background: linear-gradient(90deg,#934fe4,#c687fa)
}

.report_list_content6 {
  background: #ff9800
}

.report_item {
  border-radius: 6px;
  padding: 16px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1
}
.register-header{
  background-color: #1ec283 !important;
  color: white;
  th {
    background-color: transparent;
  }
}

.deposit-header{
  background-color: #1ec283 !important;
  color: white;
  th {
    background-color: transparent;
  }
}

.investment-header{
  background-color: rgb(80, 153, 249) !important;
  color: white;
  th {
    background-color: transparent;
  }
}
.summary-header{
  background-color: rgb(249, 97, 80) !important;
  color: white;
  th {
    background-color: transparent;
  }
}
.claimed-header{
  background-color: #ff9800 !important;
  color: white;
  th {
    background-color: transparent;
  }
}

