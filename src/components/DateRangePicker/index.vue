<template>
  <el-date-picker
    v-model="state.pickedDate"
    :type="props.type"
    unlink-panels
    :range-separator=" $t('common.to') "
    :start-placeholder="defaultDate[0]"
    :end-placeholder="defaultDate[1]"
    :picker-options="pickedOptions"
  />
</template>
<script setup>
import { computed, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n-bridge'
import dayjs from 'dayjs'
import weekday from 'dayjs/plugin/weekday'
import { timezone } from '@/settings'
import { getTimezoneOffset } from '@/filters'
dayjs.extend(weekday)
const { t } = useI18n()
const state = reactive({
  pickedDate: []
})
const props = defineProps({
  type: {
    type: String,
    default: 'daterange'
  }
})
const emit = defineEmits('change')
watch(() => state.pickedDate, function (pickedDate) {
  if (pickedDate == null) {
    emit('change', {
      start_date: null,
      end_date: null
    })
  } else {
    let submitDate = {}
    if (props.type === 'daterange' || props.type === 'datetimerange') {
      const fromDate = dayjs(pickedDate[0]).format('YYYY-MM-DD\THH:mm:ss' + getTimezoneOffset(timezone))
      const toDate = dayjs(pickedDate[1]).endOf('day').format('YYYY-MM-DD\THH:mm:ss' + getTimezoneOffset(timezone))
      submitDate = {
        start_date: fromDate,
        end_date: toDate
      }
    }
    emit('change', { ...submitDate })
  }
},
{ deep: true }
)
const defaultDate = computed(() => {
  if (props.type === 'datetimerange') {
    return [dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')]
  }
  return [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
})
const pickedOptions = {
  shortcuts: [
    {
      text: t('common.this_month'),
      onClick (picker) {
        const end = dayjs().endOf('month')
        const start = dayjs().startOf('month')
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: t('common.this_week'),
      onClick (picker) {
        const end = dayjs().endOf('week')
        const start = dayjs().startOf('week')
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: t('common.lastweek'),
      onClick (picker) {
        const end = dayjs().weekday(-7).endOf('week')
        const start = dayjs().weekday(-7).startOf('week')
        picker.$emit('pick', [start, end])
      }
    }, {
      text: t('common.lastmonth'),
      onClick (picker) {
        const end = dayjs().subtract(1, 'month').endOf('month')
        const start = dayjs().subtract(1, 'month').startOf('month')
        picker.$emit('pick', [start, end])
      }
    }, {
      text: t('common.last3months'),
      onClick (picker) {
        const end = dayjs().subtract(1, 'month').endOf('month')
        const start = dayjs().subtract(3, 'month').startOf('month')
        picker.$emit('pick', [start, end])
      }
    }]
}

</script>
