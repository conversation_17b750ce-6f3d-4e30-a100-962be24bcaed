<template>
  <el-dialog
    :title="props.title"
    :visible.sync="props.show"
    :width="props.width"
    :before-close="handleClose"
  >
    <slot />
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="handleClose">{{ $t('close') }}</el-button>
      <el-button
        v-if="!props.disableSubmit"
        :disabled="props.disabledSubmit"
        type="primary"
        @click="handleSave"
      >{{ $t('save') }}</el-button>
    </span>
  </el-dialog>
</template>

<script setup>
import { defineEmits } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: null
  },
  show: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '30%'
  },
  disableSubmit: {
    type: Boolean,
    default: false
  },
  disabledSubmit: {
    type: Boolean,
    default: false
  }

})

const emit = defineEmits(['submit', 'close'])
const handleClose = () => {
  emit('close')
}
const handleSave = () => {
  emit('submit')
}
</script>
