<template>
  <input
    class="el-input__inner"
    @keypress="isNumber($event)"
    v-model="inputValue"
    :placeholder="props.placeholder"
    :maxlength="props.maxlength"
  >
</template>

<script setup>
import { computed } from 'vue'
const emit = defineEmits('input')
const props = defineProps({
  model: Object,

  placeholder: {
    type: String,
    default: ''
  },
  maxlength: {
    type: Number,
    default: 18
  },
  stringNumber: {
    type: Boolean,
    default: true
  }
})
const inputValue = computed({
  get () {
    return props.model
  },
  set (newValue) {
    if (props.stringNumber) {
      emit('input', newValue)
    } else {
      emit('input', parseInt(newValue))
    }
  }
})
const isNumber = (event) => {
  const keysAllowed = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X']
  const keyPressed = event.key
  if (!keysAllowed.includes(keyPressed)) {
    event.preventDefault()
  }
}

</script>
