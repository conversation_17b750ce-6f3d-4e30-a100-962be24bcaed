<template>
  <div class="image-container">
    <img
      v-if="state.hasNewImage == false && imageUrl!==''"
      class="upload-thumbnail"
      :src="imageUrl"
      alt=""
    >
    <img
      v-else-if="state.image"
      class="upload-thumbnail"
      :src="state.image"
      alt=""
    >
    <div
      v-else
      class="el-upload--picture-card"
      @click="handleClick"
    >
      <span>{{ $t('common.select_file') }}</span>
    </div>
    <el-upload
      action="#"
      list-type="picture"
      :auto-upload="false"
      :on-change="handleChangeImage"
      :multiple="false"
      accept="image/*"
      :show-file-list="false"
    >
      <el-button
        slot="trigger"
        ref="trigger"
        size="small"
        type="primary"
        :class="((state.hasNewImage == false && imageUrl!=='') || state.image)?'upload-button':'upload-button-hidden'"
      >
        {{ $t('common.change_file') }}
      </el-button>
      <div
        class="el-upload__tip"
        slot="tip"
      >
        {{ $t('common.image_size_tip') }}
      </div>
    </el-upload>
  </div>
</template>

<script setup>
import { computed, reactive, ref } from 'vue'

const props = defineProps(['image'])
const trigger = ref()
const handleClick = () => {
  trigger.value.$el.click()
}
const state = reactive({
  hasNewImage: false,
  image: null
})
const imageUrl = computed(() => {
  // Check if props.image is a valid URL
  if (props.image && typeof props.image === 'string' && props.image.trim() !== '') {
    return props.image
  }
  return ''
})
const emit = defineEmits(['change'])
const handleChangeImage = (newFile, fileList) => {
  const file = newFile.raw
  state.image = newFile.url
  state.hasNewImage = true
  emit('change', { file })
}
</script>
<style lang="scss" scoped>
.image-container{
    //padding:10px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .upload-button{
        margin-top:10px;
        display: block;
        &-hidden{
            display: none;
        }
    }
    .upload-thumbnail{
        background-color: #fbfdff;
        border: 1px solid #c0ccda;
        border-radius: 6px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 148px;
        height: 148px;
        cursor: pointer;
        line-height: 146px;
        vertical-align: top;
    }
    .el-upload--picture-card{
        display: flex;
        align-items: center;
        justify-content: center;
        span {
            font-size: 14px;
            color: #a3a9af
        }
    }
}
</style>
