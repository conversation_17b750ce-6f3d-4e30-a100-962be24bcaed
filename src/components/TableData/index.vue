<template>
  <div>
    <el-table
      ref="tableRef"
      v-loading="props.loading"
      :data="props.data"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      @sort-change="handleSortChange"
      class="border-gray"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :row-key="(item)=>item.id"
    >
      <el-table-column
        v-if="props.enableSelect"
        type="selection"
        :reserve-selection="true"
        width="55"
      />
      <slot />
    </el-table>
    <slot name="pagination">
      <div
        :class="{ hidden: hidden }"
        class="pagination-container"
      >
        <el-pagination
          :background="props.background"
          :current-page="props.currentPage"
          :page-size="state.pageSize"
          :layout="props.layout"
          :page-sizes="props.pageSizes"
          :total="props.total"
          v-bind="$attrs"
          @size-change="handleSizeChange"
          @next-click="handleChangePage"
          @prev-click="handleChangePage"
          @current-change="handleChangePage"
        />
      </div>
    </slot>
  </div>
</template>
<script setup>
import { defineEmits, defineProps, nextTick, reactive, ref, watch } from 'vue'

const props = defineProps({
  loading: false,
  data: Object | Array,
  total: {
    require: true,
    type: Number
  },
  currentPage: {
    type: Number,
    default: 1
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  autoScroll: {
    type: Boolean,
    default: true
  },
  hidden: {
    type: Boolean,
    default: false
  },
  enableSelect: {
    type: Boolean,
    default: false
  },
  pageSizes: {
    type: Array,
    default () {
      return [10, 20, 30, 50]
    }
  }
})
const tableRef = ref(null)
const state = reactive({
  pageSize: 10,
  selectedRows: {}
})
const emit = defineEmits(['change', 'sortChange', 'selectRow'])

const handleChangePage = (value) => {
  emit('change', {
    page: value,
    pageSize: state.pageSize
  })
}

const handleSizeChange = async (value) => {
  state.pageSize = value
  await nextTick()
  emit('change', {
    page: 1,
    pageSize: state.pageSize
  })
}
const handleSelectionChange = (rows) => {
  emit('selectRow', rows)
}
const selectAll = (items) => {
  tableRef.value.toggleAllSelection()
  // tableRef.value.
}
const clearSelect = () => {
  tableRef.value.clearSelection()
}
const handleSortChange = (sortCol) => {
  const sortData = {
    sortBy: '',
    sort: ''
  }
  if (sortCol.order) {
    sortData.sortBy = sortCol.prop
    sortData.sort = sortCol.order == 'ascending' ? 'asc' : 'desc'
  }
  emit('sortChange', sortData)
}
defineExpose({
  selectAll,
  clearSelect
})
</script>
