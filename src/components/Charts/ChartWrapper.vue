
<template>
  <el-card class="chart-wrapper">
    <div
      slot="header"
      class="chart-header"
    >
      <span class="chart-title">{{
        props.chartTitle
      }}</span>
      <div
        class="chart-controls"
        v-if="$slots.chartControls"
      >
        <slot name="chartControls" />
      </div>
      <div class="chart-statistics">
        <slot name="chartStatistics" />
      </div>
    </div>

    <div
      v-loading="loading"
      :element-loading-text="$t('dashboard.loading_chart_data')"
      class="chart-container"
      :style="{ height: height, width: width }"
    >
      <slot name="chart" />
    </div>

    <div
      v-if="error"
      class="error-message"
    >
      <el-alert
        :title="$t('dashboard.chart_error_title')"
        :description="errorMessage"
        type="error"
        show-icon
        :closable="false"
      />
    </div>

    <div
      v-if="!loading && !error && isEmpty"
      class="empty-message"
    >
      <el-alert
        :title="$t('dashboard.no_data_title')"
        :description="$t('dashboard.no_data_description')"
        type="info"
        show-icon
        :closable="false"
      />
    </div>
  </el-card>
</template>
<script setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  isEmpty: {
    type: Boolean,
    default: false
  },
  selectedRange: {
    type: Number,
    default: 7
  },
  height: {
    type: String,
    default: '400px'
  },
  width: {
    type: String,
    default: '100%'
  },
  chartTitle: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.chart-wrapper {
  margin-bottom: 32px;

  .chart-header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .chart-title {
      font-size: 16px;
      padding-bottom: 15px;
      font-weight: 500;
      text-align: left;
      color: #303133;
    }

    .chart-controls {
      display: flex;
      padding-bottom: 15px;
      width: 100%;
      align-items: center;
    }
    .chart-statistics {
      display: flex;
      width: 100%;
      align-items: center;
    }
  }

  .chart-container {
    min-height: 400px;
  }

  .error-message,
  .empty-message {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .chart-wrapper {
    .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .chart-container {
      min-height: 300px;
    }
  }
}
</style>
