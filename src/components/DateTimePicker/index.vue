<template>
  <el-date-picker
    v-model="pickedDate"
    :type="pickedType"
    unlink-panels
    @change="handleDateChange"
  />
</template>

<script>

import { appDatetime, appDateTimeIso, getTimezoneOffset } from '@/filters'
import dayjs from 'dayjs'
import { timezone } from '@/settings'

export default {
  name: 'DatePicker',
  props: {
    modelValue: {
      type: String
    },
    value: {
      type: String
    },
    type: {
      type: String,
      default: 'datetime'
    }
  },
  data () {
    return {
      pickedType: this.type,
      pickedDate: null
    }
  },
  watch: {
    value: {
      immediate: true,
      handler (newValue) {
        // Display default date as GMT8
        this.pickedDate = newValue ? appDatetime(newValue) : null
      }
    }
  },
  methods: {
    handleDateChange (value) {
      // Picked time always counted as GMT8
      this.pickedDate = dayjs(value).format('YYYY-MM-DD\THH:mm:ss' + getTimezoneOffset(timezone))
      this.$emit('change', this.pickedDate)
    }
  }
}
</script>
