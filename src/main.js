import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'
import './styles/app.scss'
import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import i18n from './lang' // internationalization
import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters
import ElTreeGrid from 'element-tree-grid'
import Json<PERSON>iewer from 'vue-json-viewer'
import wysiwyg from 'vue-wysiwyg'
import '@/styles/vueWysiwyg.css'
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
/*
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}
*/

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})
Vue.component(ElTreeGrid.name, ElTreeGrid)
Vue.component(
  'date-time-picker',
  require('./components/DateTimePicker/index.vue').default
)
Vue.use(JsonViewer)
Vue.use(wysiwyg, {
  // hideModules: { 'code': true }
  maxHeight: '250px'
})
/* 提示快速封装 */
import '@/components/Larke/tip'
import dayjs from 'dayjs'

const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
dayjs.extend(utc)
dayjs.extend(timezone)

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false
Vue.use(i18n)
const app = new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
})
app.$mount('#app')
