import Layout from '@/layout'

const route = {
  path: '/accounts',
  component: Layout,
  redirect: '/admin/index',
  alwaysShow: true,
  name: 'Accounts',
  meta: {
    title: 'account',
    icon: 'el-icon-s-custom',
    roles: [
      'larke-admin.accounts',
      'larke-admin.accounts.player-list',
      'larke-admin.accounts.operation-logs',
      'larke-admin.accounts.general-agents',
      'larke-admin.accounts.local-agents',
      'larke-admin.accounts.regional-agents',
      'larke-admin.accounts.login-logs',
      'larke-admin.accounts.black-list',
      'larke-admin.accounts.risk-control'
    ]
  },
  children: [
    {
      path: '/accounts/player-list',
      component: () => import('@/views/account/player/index'),
      name: 'Player',
      meta: {
        title: 'menu.accounts.player_list',
        icon: 'el-icon-user-solid',
        roles: [
          'larke-admin.accounts.player-list'
        ]
      }
    },

    {
      path: '/accounts/general-agents',
      component: () => import('@/views/account/agents/general/index'),
      name: 'GeneralAgents',
      meta: {
        title: 'menu.accounts.general_agents',
        icon: 'el-icon-s-cooperation',
        roles: [
          'larke-admin.accounts.general-agents'
        ]
      }
    },
    {
      path: '/accounts/regional-agents',
      component: () => import('@/views/account/agents/regional/index'),
      name: 'RegionalAgents',
      meta: {
        title: 'menu.accounts.regional_agents',
        icon: 'el-icon-office-building',
        roles: [
          'larke-admin.accounts.regional-agents'
        ]
      }
    },
    {
      path: '/accounts/local-agents',
      component: () => import('@/views/account/agents/local/index'),
      name: 'LocalAgents',
      meta: {
        title: 'menu.accounts.local_agents',
        icon: 'el-icon-user',
        roles: [
          'larke-admin.accounts.local-agents'
        ]
      }
    },
    {
      path: '/accounts/login-logs',
      component: () => import('@/views/account/login-log/index'),
      name: 'LoginLogs',
      meta: {
        title: 'menu.accounts.login_logs',
        icon: 'el-icon-key',
        roles: [
          'larke-admin.accounts.login-logs'
        ]
      }
    },
    {
      path: '/accounts/operation-logs',
      component: () => import('@/views/account/operation-log/index'),
      name: 'OperationLogs',
      meta: {
        title: 'menu.accounts.operation_logs',
        icon: 'el-icon-document',
        roles: [
          'larke-admin.accounts.operation-logs'
        ]
      }
    },
    {
      path: '/accounts/risk-control',
      component: () => import('@/views/account/risk-control/index'),
      name: 'RiskControl',
      meta: {
        title: 'menu.accounts.risk_control',
        icon: 'el-icon-warning',
        roles: [
          'larke-admin.accounts.risk-control'
        ]
      }
    },
    {
      path: '/accounts/black-list',
      component: () => import('@/views/account/black-list/index'),
      name: 'BlackList',
      meta: {
        title: 'menu.accounts.black_list',
        icon: 'el-icon-circle-close',
        roles: [
          'larke-admin.ip-blacklists',
          'larke-admin.ip-blacklists.index'
        ]
      }
    }
  ]
}

export default route

