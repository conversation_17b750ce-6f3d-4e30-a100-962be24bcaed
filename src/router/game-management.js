import Layout from '@/layout'

const route = {
  path: '/game-management',
  component: Layout,
  redirect: '/game-management/game-config',
  alwaysShow: true, // will always show the root menu
  name: 'GameManagement',
  meta: {
    title: 'gameManagement',
    icon: 'el-icon-s-grid',
    roles: [
      'larke-admin.admin.index'
    ] // you can set roles in root nav
  },
  children: [
    {
      path: '/game-management/game-config',
      component: () => import('@/views/game-management/game-config/index'),
      name: 'GameConfig',
      meta: {
        title: 'gameConfig',
        icon: 'el-icon-s-operation',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/dice-config',
      component: () => import('@/views/game-management/game-config/dice-config/index'),
      name: 'DiceConfig',
      meta: {
        title: 'diceConfig',
        icon: 'el-icon-menu',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/plinko-config',
      component: () => import('@/views/game-management/game-config/plinko-config/index'),
      name: 'PlinkoConfig',
      meta: {
        title: 'plinkoConfig',
        icon: 'el-icon-baseball',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/plinko-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/plinko-config/client-config'),
      name: 'PlinkoClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'plinkoClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/dice-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/dice-config/client-config'),
      name: 'DiceClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'diceClientConfig',
        icon: 'el-icon-element-plus',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/european-roulette-config',
      component: () => import('@/views/game-management/game-config/european-roulette-config/index'),
      name: 'EuropeanRouletteConfig',
      meta: {
        title: 'europeanRouletteConfig',
        icon: 'el-icon-orange ',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/european-roulette-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/european-roulette-config/client-config'),
      name: 'EuropeanRouletteClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'europeanRouletteClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/double-config',
      component: () => import('@/views/game-management/game-config/double-config/index'),
      name: 'DoubleConfig',
      meta: {
        title: 'doubleConfig',
        icon: 'el-icon-money',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/double-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/double-config/client-config'),
      name: 'DoubleClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'doubleClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/roulette-config',
      component: () => import('@/views/game-management/game-config/roulette-config/index'),
      name: 'RouletteConfig',
      meta: {
        title: 'rouletteConfig',
        icon: 'el-icon-help',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/roulette-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/roulette-config/client-config'),
      name: 'RouletteClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'rouletteClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/crash-config',
      component: () => import('@/views/game-management/game-config/crash-config/index'),
      name: 'CrashConfig',
      meta: {
        title: 'crashConfig',
        icon: 'el-icon-position',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/crash-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/crash-config/client-config'),
      name: 'CrashClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'crashClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/ring-config',
      component: () => import('@/views/game-management/game-config/ring-config/index'),
      name: 'RingConfig',
      meta: {
        title: 'ringConfig',
        icon: 'el-icon-pie-chart',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/ring-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/ring-config/client-config'),
      name: 'RingClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'ringClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/mines-config',
      component: () => import('@/views/game-management/game-config/mines-config/index'),
      name: 'MinesConfig',
      meta: {
        title: 'minesConfig',
        icon: 'el-icon-s-opportunity',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/mines-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/mines-config/client-config'),
      name: 'MinesClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'minesClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/fruit-slots-config',
      component: () => import('@/views/game-management/game-config/fruit-slots-config/index'),
      name: 'FruitSlotsConfig',
      meta: {
        title: 'fruitSlotsConfig',
        icon: 'el-icon-apple',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/fruit-slots-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/fruit-slots-config/client-config'),
      name: 'FruitSlotsClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'fruitSlotsClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/mass-keno-config',
      component: () => import('@/views/game-management/game-config/mass-keno-config/index'),
      name: 'MassKenoConfig',
      meta: {
        title: 'massKenoConfig',
        icon: 'el-icon-magic-stick',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/mass-keno-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/mass-keno-config/client-config'),
      name: 'MassKenoClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'massKenoClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/oddstrader-config',
      component: () => import('@/views/game-management/game-config/oddstrader-config/index'),
      name: 'OddstraderConfig',
      meta: {
        title: 'oddstraderConfig',
        icon: 'el-icon-document',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    },
    {
      path: '/game-management/oddstrader-config/client/:clientId',
      component: () => import('@/views/game-management/game-config/oddstrader-config/client-config'),
      name: 'OddstraderClientConfig',
      hidden: true, // Hide from sidebar menu
      meta: {
        title: 'oddstraderClientConfig',
        icon: 'el-icon-s-data',
        roles: [
          'larke-admin.admin.index'
        ]
      }
    }
  ]
}

export default route
