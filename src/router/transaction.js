import Layout from '@/layout'

const route = {
  path: '/transaction',
  component: Layout,
  redirect: '/admin/index',
  alwaysShow: true,
  name: 'Transactions',
  meta: {
    title: 'transaction',
    icon: 'el-icon-s-finance',
    roles: [
      'larke-admin.transactions'
    ]
  },
  children: [
    {
      path: '/transaction/fund-logs',
      component: () => import('@/views/transaction/fund-log/index'),
      name: 'FundLogs',
      meta: {
        title: 'menu.transaction.fund_log',
        icon: 'el-icon-money',
        roles: [
          'larke-admin.transactions.fund-logs'
        ]
      }
    },
    {
      path: '/transactions/betting-logs',
      component: () => import('@/views/transaction/betting-log/index'),
      name: 'BettingLogs',
      meta: {
        title: 'menu.transaction.betting_log',
        icon: 'el-icon-trophy',
        roles: [
          'larke-admin.transactions.betting-logs'
        ]
      }
    },
    {
      path: '/transactions/data-export',
      component: () => import('@/views/transaction/data-export/index'),
      name: 'DataExport',
      meta: {
        title: 'menu.transaction.data_export',
        icon: 'el-icon-download',
        roles: [
          'larke-admin.transactions.data-export'
        ]
      }
    },
    {
      path: '/transactions/liquidation-report',
      component: () => import('@/views/transaction/liquidation-report/index'),
      name: 'FinanceLiquidationReport',
      meta: {
        title: 'menu.transaction.liquidation_report',
        icon: 'el-icon-data-analysis',
        roles: [
          'larke-admin.transactions.liquidation-report'
        ]
      }
    },
    {
      path: '/transactions/player-report',
      component: () => import('@/views/transaction/player-report/index'),
      name: 'PlayerReport',
      meta: {
        title: 'menu.transaction.player_report',
        icon: 'el-icon-user-solid',
        roles: [
          'larke-admin.transactions.player-report'
        ]
      }
    },
    {
      path: '/transactions/sales-report',
      component: () => import('@/views/transaction/sale-report/index'),
      name: 'SalesReport',
      meta: {
        title: 'menu.transaction.sale_report',
        icon: 'el-icon-s-marketing',
        roles: [
          'larke-admin.transactions.sales-report'
        ]
      }
    }
  ]
}

export default route

