import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get European Roulette game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getEuropeanRouletteConfigs (params) {
  return request({
    url: '/games/european-roulette/config',
    method: 'get',
    params
  })
}

/**
 * Update European Roulette game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data (min_amount, max_amount)
 * @returns {Promise}
 */
export function updateEuropeanRouletteConfig (id, data) {
  return request({
    url: `/games/european-roulette/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific European Roulette configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getEuropeanRouletteConfigDetail (id) {
  return request({
    url: `/games/european-roulette/${id}`,
    method: 'get'
  })
}

/**
 * Create new European Roulette configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createEuropeanRouletteConfig (data) {
  return request({
    url: '/games/european-roulette/config',
    method: 'post',
    data
  })
}

/**
 * Delete European Roulette configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteEuropeanRouletteConfig (id) {
  return request({
    url: `/games/european-roulette/${id}`,
    method: 'delete'
  })
}
