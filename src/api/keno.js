import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Keno game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getKenoConfigs (params) {
  return request({
    url: '/games/keno/config',
    method: 'get',
    params
  })
}

/**
 * Update Keno game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateKenoConfig (id, data) {
  return request({
    url: `/games/keno/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Keno configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getKenoConfigDetail (id) {
  return request({
    url: `/games/keno/${id}`,
    method: 'get'
  })
}

/**
 * Create new Keno configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createKenoConfig (data) {
  return request({
    url: '/games/keno/config',
    method: 'post',
    data
  })
}

/**
 * Delete Keno configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteKenoConfig (id) {
  return request({
    url: `/games/keno/${id}`,
    method: 'delete'
  })
}
