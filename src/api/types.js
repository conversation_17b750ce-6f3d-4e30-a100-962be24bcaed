/**
 * @typedef {Object} PaginationData
 * @property {array} data
 * @property {number} total
 * @property {number} current_page
 */

/**
 * @typedef {Object} Pagination
 * @property {boolean} success status of request
 * @property {string} message message
 * @property {number} code status code
 * @property {PaginationData} data data
 */

/**
 * @typedef {Object} DetailData
 * @property {boolean} success status of request
 * @property {string} message message
 * @property {number} code status code
 * @property {Object} data data
 */
