import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Roulette game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getRouletteConfigs (params) {
  return request({
    url: '/games/roulette/config',
    method: 'get',
    params
  })
}

/**
 * Update Roulette game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data with config JSON string
 * @returns {Promise}
 */
export function updateRouletteConfig (id, data) {
  return request({
    url: `/games/roulette/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Roulette configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getRouletteConfigDetail (id) {
  return request({
    url: `/games/roulette/${id}`,
    method: 'get'
  })
}

/**
 * Create new Roulette configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createRouletteConfig (data) {
  return request({
    url: '/games/roulette/config',
    method: 'post',
    data
  })
}

/**
 * Delete Roulette configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteRouletteConfig (id) {
  return request({
    url: `/games/roulette/${id}`,
    method: 'delete'
  })
}
