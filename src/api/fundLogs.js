import request from '@/utils/request'

/**
 * Get fund logs with filtering and pagination
 * @param {Object} params - Query parameters
 * @returns {Promise}
 */
export function getFundLogs (params) {
  return request({
    url: '/transactions/fund-logs',
    method: 'get',
    params
  })
}

/**
 * Get fund logs filter options
 * @returns {Promise}
 */
export function getFundLogsFilterOptions () {
  return request({
    url: '/transactions/fund-logs/filter-options',
    method: 'get'
  })
}

/**
 * Get fund logs statistics
 * @returns {Promise}
 */
export function getFundLogsStatistics () {
  return request({
    url: '/transactions/fund-logs/statistics',
    method: 'get'
  })
}
