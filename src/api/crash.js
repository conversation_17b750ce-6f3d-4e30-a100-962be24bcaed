import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Crash game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getCrashConfigs (params) {
  return request({
    url: '/games/crash/config',
    method: 'get',
    params
  })
}

/**
 * Update Crash game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateCrashConfig (id, data) {
  return request({
    url: `/games/crash/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Crash configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getCrashConfigDetail (id) {
  return request({
    url: `/games/crash/${id}`,
    method: 'get'
  })
}

/**
 * Create new Crash configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createCrashConfig (data) {
  return request({
    url: '/games/crash/config',
    method: 'post',
    data
  })
}

/**
 * Delete Crash configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteCrashConfig (id) {
  return request({
    url: `/games/crash/${id}`,
    method: 'delete'
  })
}
