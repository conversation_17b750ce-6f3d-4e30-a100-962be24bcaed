import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get ring game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getRingConfigs (params) {
  return request({
    url: '/games/ring/config',
    method: 'get',
    params
  })
}

/**
 * Update ring game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateRingConfig (id, data) {
  return request({
    url: `/games/ring/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific ring configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getRingConfigDetail (id) {
  return request({
    url: `/games/ring/${id}`,
    method: 'get'
  })
}

/**
 * Create new ring configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createRingConfig (data) {
  return request({
    url: '/games/ring/config',
    method: 'post',
    data
  })
}

/**
 * Delete ring configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteRingConfig (id) {
  return request({
    url: `/games/ring/${id}`,
    method: 'delete'
  })
}
