import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Plinko game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getPlinkoConfigs (params) {
  return request({
    url: '/games/plinko-game/config',
    method: 'get',
    params
  })
}

/**
 * Update Plinko game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data (color, position, rate, number, calculation)
 * @returns {Promise}
 */
export function updatePlinkoConfig (id, data) {
  return request({
    url: `/games/plinko-game/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Plinko configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getPlinkoConfigDetail (id) {
  return request({
    url: `/games/plinko-game/${id}`,
    method: 'get'
  })
}

/**
 * Create new Plinko configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createPlinkoConfig (data) {
  return request({
    url: '/games/plinko-game/config',
    method: 'post',
    data
  })
}

/**
 * Delete Plinko configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deletePlinkoConfig (id) {
  return request({
    url: `/games/plinko-game/${id}`,
    method: 'delete'
  })
}
