import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Massachusetts Keno game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getMassKenoConfigs (params) {
  return request({
    url: '/games/mass-keno/config',
    method: 'get',
    params
  })
}

/**
 * Update Massachusetts Keno game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateMassKenoConfig (id, data) {
  return request({
    url: `/games/mass-keno/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Massachusetts Keno configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getMassKenoConfigDetail (id) {
  return request({
    url: `/games/mass-keno/${id}`,
    method: 'get'
  })
}

/**
 * Create new Massachusetts Keno configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createMassKenoConfig (data) {
  return request({
    url: '/games/mass-keno/config',
    method: 'post',
    data
  })
}

/**
 * Delete Massachusetts Keno configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteMassKenoConfig (id) {
  return request({
    url: `/games/mass-keno/${id}`,
    method: 'delete'
  })
}
