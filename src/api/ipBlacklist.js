import request from '@/utils/request'

/**
 * Get IP blacklist entries with filtering and pagination
 * @param {Object} params - Query parameters
 * @returns {Promise}
 */
export function getIpBlacklistList (params) {
  return request({
    url: '/ip-blacklists',
    method: 'get',
    params
  })
}

/**
 * Get specific IP blacklist entry details
 * @param {number} id - IP blacklist entry ID
 * @returns {Promise}
 */
export function getIpBlacklistDetail (id) {
  return request({
    url: `/ip-blacklists/${id}`,
    method: 'get'
  })
}

/**
 * Create new IP blacklist entry
 * @param {Object} data - IP blacklist data
 * @returns {Promise}
 */
export function createIpBlacklist (data) {
  return request({
    url: '/ip-blacklists',
    method: 'post',
    data
  })
}

/**
 * Update existing IP blacklist entry
 * @param {number} id - IP blacklist entry ID
 * @param {Object} data - Updated IP blacklist data
 * @returns {Promise}
 */
export function updateIpBlacklist (id, data) {
  return request({
    url: `/ip-blacklists/${id}`,
    method: 'put',
    data
  })
}

/**
 * Delete IP blacklist entry
 * @param {number} id - IP blacklist entry ID
 * @returns {Promise}
 */
export function deleteIpBlacklist (id) {
  return request({
    url: `/ip-blacklists/${id}`,
    method: 'delete'
  })
}

/**
 * Perform bulk operations on IP blacklist entries
 * @param {Array} ids - Array of IP blacklist entry IDs
 * @param {string} action - Action to perform ('activate', 'deactivate', 'delete')
 * @param {string} status - Status for activate/deactivate actions
 * @returns {Promise}
 */
export function bulkUpdateIpBlacklist (ids, action, status = null) {
  const data = { ids, action }
  if (status) {
    data.status = status
  }

  return request({
    url: '/ip-blacklists/bulk-update',
    method: 'post',
    data
  })
}

/**
 * Check if a specific IP address is blacklisted
 * @param {string} ipAddress - IP address to check
 * @returns {Promise}
 */
export function checkIpBlacklist (ipAddress) {
  return request({
    url: '/ip-blacklists/check-ip',
    method: 'post',
    data: {
      ip_address: ipAddress
    }
  })
}

/**
 * Get IP blacklist statistics
 * @returns {Promise}
 */
export function getIpBlacklistStatistics () {
  return request({
    url: '/ip-blacklists/statistics',
    method: 'get'
  })
}
