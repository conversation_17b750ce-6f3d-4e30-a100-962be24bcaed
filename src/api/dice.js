import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Dice game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getDiceConfigs (params) {
  return request({
    url: '/games/dice-game/config',
    method: 'get',
    params
  })
}

/**
 * Update Dice game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateDiceConfig (id, data) {
  return request({
    url: `/games/dice-game/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Dice configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getDiceConfigDetail (id) {
  return request({
    url: `/games/dice-game/${id}`,
    method: 'get'
  })
}

/**
 * Create new Dice configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createDiceConfig (data) {
  return request({
    url: '/games/dice-game/config',
    method: 'post',
    data
  })
}

/**
 * Delete Dice configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteDiceConfig (id) {
  return request({
    url: `/games/dice-game/${id}`,
    method: 'delete'
  })
}
