import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Mines game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getMinesConfigs (params) {
  return request({
    url: '/games/mines/config',
    method: 'get',
    params
  })
}

/**
 * Update Mines game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateMinesConfig (id, data) {
  return request({
    url: `/games/mines/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Mines configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getMinesConfigDetail (id) {
  return request({
    url: `/games/mines/${id}`,
    method: 'get'
  })
}

/**
 * Create new Mines configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createMinesConfig (data) {
  return request({
    url: '/games/mines/config',
    method: 'post',
    data
  })
}

/**
 * Delete Mines configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteMinesConfig (id) {
  return request({
    url: `/games/mines/${id}`,
    method: 'delete'
  })
}
