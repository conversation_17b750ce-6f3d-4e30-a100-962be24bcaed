import request from '@/utils/request'

export function getList (params) {
  return request({
    url: '/admin',
    method: 'get',
    params
  })
}

export function getListAgent (params, type) {
  return request({
    url: `/accounts/${type}`,
    method: 'get',
    params
  })
}

export function getDetail (id) {
  return request({
    url: `/admin/${id}`,
    method: 'get'
  })
}

export function getRules (id) {
  return request({
    url: `/admin/${id}/rules`,
    method: 'get'
  })
}

export function getGroups () {
  return request({
    url: '/admin/groups',
    method: 'get'
  })
}

export function createAdmin (data, type) {
  return request({
    url: `/agents/${type}`,
    method: 'post',
    data
  })
}

export function updateAdmin (id, data) {
  return request({
    url: `/admin/${id}`,
    method: 'put',
    data
  })
}

export function deleteAdmin (id) {
  return request({
    url: `/admin/${id}`,
    method: 'delete'
  })
}

export function enableAdmin (id) {
  return request({
    url: `/admin/${id}/enable`,
    method: 'patch'
  })
}

export function disableAdmin (id) {
  return request({
    url: `/admin/${id}/disable`,
    method: 'patch'
  })
}

export function updatePassword (id, data) {
  return request({
    url: `/admin/${id}/password`,
    method: 'patch',
    data
  })
}

export function updateAccess (id, data) {
  return request({
    url: `/admin/${id}/access`,
    method: 'patch',
    data
  })
}

export function resetPermission () {
  return request({
    url: `/admin/reset-permission`,
    method: 'put'
  })
}

export function logoutAdmin (refreshToken) {
  return request({
    url: `/admin/logout/${refreshToken}`,
    method: 'delete'
  })
}

export function get2Fa () {
  return request({
    url: `/admin/two-fa/qr`,
    method: 'get'
  })
}
export function verify2Fa (data) {
  return request({
    url: `/admin/two-fa/verify`,
    method: 'post',
    data
  })
}

export function getRegionalRebates (regionalId) {
  return request({
    url: `/regional-rebates/${regionalId}`,
    method: 'get'
  })
}

export function updateRegionalRebates (regionalId, data) {
  return request({
    url: `/regional-rebates/${regionalId}`,
    method: 'put',
    data
  })
}

export function getActivityLogs (params) {
  return request({
    url: '/activity-logs',
    method: 'get',
    params
  })
}

export function getListAgentByType (type, params) {
  return request({
    url: `/agents/${type}`,
    method: 'get',
    params
  })
}
