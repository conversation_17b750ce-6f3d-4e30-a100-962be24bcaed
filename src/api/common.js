import request from '@/utils/request'

export function getSupportedLanguages () {
  return request({
    url: '/common/supportedLocales',
    method: 'get'
  })
}

export function getSupportedCurrencies () {
  return request({
    url: '/common/supportedCurrencies',
    method: 'GET'
  })
}

export function getAircoinCurrencies () {
  return request({
    url: '/common/aircoin-currencies',
    method: 'GET'
  })
}

export function getGameProviderCategories () {
  return request({
    url: '/gameProvider/categories',
    method: 'GET'
  })
}

export function uploadFile (data) {
  return request({
    url: '/common/upload/file',
    method: 'post',
    data
  })
}

export function getSupportedGameTitles () {
  return request({
    url: '/common/supportedGameTitles',
    method: 'GET'
  })
}

export function getSupportedDirectusGameTitles () {
  return request({
    url: '/common/supportedDirectusGameTitles',
    method: 'GET'
  })
}

export function getSupportedBotTypes () {
  return request({
    url: '/common/supportedBotTypes',
    method: 'GET'
  })
}

export function getSupportedBotStatuses () {
  return request({
    url: '/common/supportedBotStatuses',
    method: 'GET'
  })
}
