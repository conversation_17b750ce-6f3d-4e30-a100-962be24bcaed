import request from '@/utils/request'

/**
 * Get player logs with filtering and pagination
 * @param {Object} params - Query parameters
 * @returns {Promise}
 */
export function getPlayerLogs (params) {
  return request({
    url: '/player-logs',
    method: 'get',
    params
  })
}

/**
 * Get available log types for filtering
 * @returns {Promise}
 */
export function getPlayerLogTypes () {
  return request({
    url: '/player-logs/types',
    method: 'get'
  })
}

/**
 * Get player log statistics
 * @returns {Promise}
 */
export function getPlayerLogStatistics () {
  return request({
    url: '/player-logs/statistics',
    method: 'get'
  })
}
