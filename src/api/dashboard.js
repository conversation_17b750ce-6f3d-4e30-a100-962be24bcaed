import request from '@/utils/request'

export function getSummaryReport (params) {
  return request({
    url: '/dashboards/summary-report',
    method: 'get',
    params
  })
}
export function getRetentionReport (params) {
  return request({
    url: '/dashboards/retention-report',
    method: 'get',
    params
  })
}

export function getBetSummary () {
  return request({
    url: '/dashboards/bet-report',
    method: 'get'
  })
}

export function getAverateWinLostReport (params) {
  return request({
    url: '/dashboards/average-win-lose-report',
    method: 'get',
    params
  })
}
