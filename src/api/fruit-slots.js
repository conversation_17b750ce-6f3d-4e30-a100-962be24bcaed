import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Fruit Slots configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getFruitSlotsConfigs (params) {
  return request({
    url: '/games/fruit-slots/config',
    method: 'get',
    params
  })
}

/**
 * Update Fruit Slots configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function updateFruitSlotsConfig (id, data) {
  return request({
    url: `/games/fruit-slots/${id}/update`,
    method: 'put',
    data
  })
}

// Fruits Management Functions

/**
 * Get all fruit configurations
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getFruits (params) {
  return request({
    url: '/games/fruit-slots/fruits',
    method: 'get',
    params
  })
}

/**
 * Update specific fruit configuration by ID
 * @param {number} id - Fruit configuration ID
 * @param {Object} data - Fruit configuration data
 * @returns {Promise}
 */
export function updateFruit (id, data) {
  return request({
    url: `/games/fruit-slots/fruits/${id}`,
    method: 'put',
    data
  })
}

// Game Settings Management Functions

/**
 * Get game settings configuration
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getGameSettings (params) {
  return request({
    url: '/games/fruit-slots/game-settings',
    method: 'get',
    params
  })
}

/**
 * Update specific game settings by ID
 * @param {number} id - Game settings ID
 * @param {Object} data - Game settings data
 * @returns {Promise}
 */
export function updateGameSettings (id, data) {
  return request({
    url: `/games/fruit-slots/game-settings/${id}`,
    method: 'put',
    data
  })
}

// Lucky Draws Management Functions

/**
 * Get lucky draw configurations
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getLuckyDraws (params) {
  return request({
    url: '/games/fruit-slots/lucky-draws',
    method: 'get',
    params
  })
}

/**
 * Update specific lucky draw configuration by ID
 * @param {number} id - Lucky draw configuration ID
 * @param {Object} data - Lucky draw configuration data
 * @returns {Promise}
 */
export function updateLuckyDraw (id, data) {
  return request({
    url: `/games/fruit-slots/lucky-draws/${id}`,
    method: 'put',
    data
  })
}

