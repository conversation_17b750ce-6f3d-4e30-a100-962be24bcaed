import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Double game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getDoubleConfigs (params) {
  return request({
    url: '/games/double/config',
    method: 'get',
    params
  })
}

/**
 * Update Double game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data (red_prize, black_prize, special_prize, min_amount, max_amount)
 * @returns {Promise}
 */
export function updateDoubleConfig (id, data) {
  return request({
    url: `/games/double/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Double configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getDoubleConfigDetail (id) {
  return request({
    url: `/games/double/${id}`,
    method: 'get'
  })
}

/**
 * Create new Double configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createDoubleConfig (data) {
  return request({
    url: '/games/double/config',
    method: 'post',
    data
  })
}

/**
 * Delete Double configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteDoubleConfig (id) {
  return request({
    url: `/games/double/${id}`,
    method: 'delete'
  })
}
