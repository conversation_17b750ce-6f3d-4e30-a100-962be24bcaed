import request from '@/utils/request'

/**
 * Get client list
 * @returns {Promise}
 */
export function getClientList (params) {
  return request({
    url: '/client/list',
    method: 'get',
    params
  })
}

/**
 * Get Oddstrader game configurations for a specific client
 * @param {Object} params - Query parameters including client_id
 * @returns {Promise}
 */
export function getOddstraderConfigs (params) {
  return request({
    url: '/games/oddstrader/config',
    method: 'get',
    params
  })
}

/**
 * Update Oddstrader game configuration
 * @param {number} id - Configuration ID
 * @param {Object} data - Configuration data (currency, interval, units)
 * @returns {Promise}
 */
export function updateOddstraderConfig (id, data) {
  return request({
    url: `/games/oddstrader/${id}/update`,
    method: 'put',
    data
  })
}

/**
 * Get specific Oddstrader configuration details
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function getOddstraderConfigDetail (id) {
  return request({
    url: `/games/oddstrader/${id}`,
    method: 'get'
  })
}

/**
 * Create new Oddstrader configuration
 * @param {Object} data - Configuration data
 * @returns {Promise}
 */
export function createOddstraderConfig (data) {
  return request({
    url: '/games/oddstrader/config',
    method: 'post',
    data
  })
}

/**
 * Delete Oddstrader configuration
 * @param {number} id - Configuration ID
 * @returns {Promise}
 */
export function deleteOddstraderConfig (id) {
  return request({
    url: `/games/oddstrader/${id}`,
    method: 'delete'
  })
}
