// import parseTime, formatTime and set to filter
import { timezone } from '@/settings'
import dayjs from 'dayjs'

export { formatTime, parseTime } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize (time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo (time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter (num, digits) {
  const si = [
    { value: 1E18, symbol: 'E' },
    { value: 1E15, symbol: 'P' },
    { value: 1E12, symbol: 'T' },
    { value: 1E9, symbol: 'G' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter (num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst (string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * 格式化文件大小
 * @param {string} value
 */
export function renderSize (value) {
  if (value == null || value == '') {
    return '0 Bytes'
  }
  var unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  var index = 0
  var srcsize = parseFloat(value)
  index = Math.floor(Math.log(srcsize) / Math.log(1024))
  var size = srcsize / Math.pow(1024, index)
  size = size.toFixed(2)// 保留的小数位数
  return size + unitArr[index]
}

export function entityToString (entity) {
  const div = document.createElement('div')
  div.innerHTML = entity
  const res = div.innerText || div.innerText
  return res
}

export function appDate (value) {
  if (!value) return ''
  return dayjs.utc(value).tz(timezone).format('YYYY-MM-DD')
}

export function appDatetime (value) {
  if (!value) return ''
  return dayjs.utc(value).tz(timezone).format('YYYY-MM-DD HH:mm:ss')
}

export function appIsoDatetime (value) {
  if (!value) return ''
  return dayjs(value).toISOString()
}

export function getTimezoneOffset (timezone) {
  const now = new Date()
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone: timezone,
    hour12: false,
    timeZoneName: 'longOffset'
  })

  const parts = formatter.formatToParts(now)
  const timeZoneName = parts.find(part => part.type === 'timeZoneName').value
  const offsetMatch = timeZoneName.match(/GMT([+-]\d+)/)

  return (offsetMatch ? offsetMatch[1] : '+00') + ':00'
}

export function appDateTimeIso (value) {
  return value ? dayjs(value).tz(timezone).format('YYYY-MM-DD\THH:mm:ss' + getTimezoneOffset(timezone)) : null
}

export function appCurrency (number) {
  if (number === null || number === undefined || number === '') {
    return '0.00'
  }
  const numValue = parseFloat(number)
  if (isNaN(numValue)) {
    return '0.00'
  }
  return numValue.toFixed(2)
}

export function removeModelName (value) {
  if (!value) return ''
  return value.replace('App\\Models\\', '')
}
