# Double Game Configuration Backend Requirements

This document outlines the Laravel backend implementation required to support the Double game configuration management system.

## API Endpoints

The frontend expects the following API endpoints to be implemented:

### 1. Get Client List
- **Endpoint**: `GET /admin-api/client/list`
- **Parameters**: 
  - `page` (optional): Page number for pagination
  - `per_page` (optional): Number of items per page
- **Response Format**:
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Client Name",
        "status": "active",
        "created_at": "2024-01-01 00:00:00"
      }
    ],
    "total": 100,
    "current_page": 1,
    "per_page": 20
  }
}
```

### 2. Get Double Game Configurations
- **Endpoint**: `GET /admin-api/games/double/config`
- **Parameters**:
  - `client_id` (required): Client ID to fetch configurations for
- **Response Format**:
```json
{
  "code": 0,
  "message": "Success",
  "data": [
    {
      "id": 1,
      "client_id": 1,
      "red_prize": 2.0,
      "black_prize": 2.0,
      "special_prize": 14.0,
      "min_amount": 1.0,
      "max_amount": 10000.0,
      "created_at": "2024-01-01 00:00:00",
      "updated_at": "2024-01-01 00:00:00"
    }
  ]
}
```

### 3. Update Double Game Configuration
- **Endpoint**: `PUT /admin-api/games/double/{id}/update`
- **Parameters**:
  - `id` (URL parameter): Configuration ID
- **Request Body**:
```json
{
  "red_prize": 2.0,
  "black_prize": 2.0,
  "special_prize": 14.0,
  "min_amount": 1.0,
  "max_amount": 10000.0,
  "client_id": 1
}
```
- **Response Format**:
```json
{
  "code": 0,
  "message": "Configuration updated successfully",
  "data": {
    "id": 1,
    "client_id": 1,
    "red_prize": 2.0,
    "black_prize": 2.0,
    "special_prize": 14.0,
    "min_amount": 1.0,
    "max_amount": 10000.0,
    "updated_at": "2024-01-01 00:00:00"
  }
}
```

## Laravel Implementation Structure

### 1. Routes (routes/api.php or routes/admin.php)
```php
Route::prefix('admin-api')->middleware(['auth:api', 'admin'])->group(function () {
    // Client routes
    Route::get('/client/list', [ClientController::class, 'list']);
    
    // Double game configuration routes
    Route::prefix('games/double')->group(function () {
        Route::get('/config', [DoubleGameConfigController::class, 'index']);
        Route::post('/config', [DoubleGameConfigController::class, 'store']);
        Route::get('/{id}', [DoubleGameConfigController::class, 'show']);
        Route::put('/{id}/update', [DoubleGameConfigController::class, 'update']);
        Route::delete('/{id}', [DoubleGameConfigController::class, 'destroy']);
    });
});
```

### 2. Controller (app/Http/Controllers/Admin/DoubleGameConfigController.php)
```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DoubleGameConfigRequest;
use App\Models\DoubleGameConfig;
use Illuminate\Http\Request;

class DoubleGameConfigController extends Controller
{
    public function index(Request $request)
    {
        $clientId = $request->get('client_id');
        
        if (!$clientId) {
            return response()->json([
                'code' => 1,
                'message' => 'Client ID is required'
            ], 400);
        }

        $configs = DoubleGameConfig::where('client_id', $clientId)->get();

        return response()->json([
            'code' => 0,
            'message' => 'Success',
            'data' => $configs
        ]);
    }

    public function store(DoubleGameConfigRequest $request)
    {
        $config = DoubleGameConfig::create($request->validated());

        return response()->json([
            'code' => 0,
            'message' => 'Configuration created successfully',
            'data' => $config
        ]);
    }

    public function show($id)
    {
        $config = DoubleGameConfig::findOrFail($id);

        return response()->json([
            'code' => 0,
            'message' => 'Success',
            'data' => $config
        ]);
    }

    public function update(DoubleGameConfigRequest $request, $id)
    {
        $config = DoubleGameConfig::findOrFail($id);
        $config->update($request->validated());

        return response()->json([
            'code' => 0,
            'message' => 'Configuration updated successfully',
            'data' => $config
        ]);
    }

    public function destroy($id)
    {
        $config = DoubleGameConfig::findOrFail($id);
        $config->delete();

        return response()->json([
            'code' => 0,
            'message' => 'Configuration deleted successfully'
        ]);
    }
}
```

### 3. Form Request (app/Http/Requests/Admin/DoubleGameConfigRequest.php)
```php
<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class DoubleGameConfigRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Adjust based on your authorization logic
    }

    public function rules()
    {
        return [
            'client_id' => 'required|integer|exists:clients,id',
            'red_prize' => 'required|numeric|min:0',
            'black_prize' => 'required|numeric|min:0',
            'special_prize' => 'required|numeric|min:0',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'required|numeric|min:0|gt:min_amount',
        ];
    }

    public function messages()
    {
        return [
            'client_id.required' => 'Client ID is required',
            'client_id.exists' => 'Invalid client ID',
            'red_prize.required' => 'Red prize is required',
            'red_prize.numeric' => 'Red prize must be a number',
            'red_prize.min' => 'Red prize must be greater than or equal to 0',
            'black_prize.required' => 'Black prize is required',
            'black_prize.numeric' => 'Black prize must be a number',
            'black_prize.min' => 'Black prize must be greater than or equal to 0',
            'special_prize.required' => 'Special prize is required',
            'special_prize.numeric' => 'Special prize must be a number',
            'special_prize.min' => 'Special prize must be greater than or equal to 0',
            'min_amount.required' => 'Minimum amount is required',
            'min_amount.numeric' => 'Minimum amount must be a number',
            'min_amount.min' => 'Minimum amount must be greater than or equal to 0',
            'max_amount.required' => 'Maximum amount is required',
            'max_amount.numeric' => 'Maximum amount must be a number',
            'max_amount.min' => 'Maximum amount must be greater than or equal to 0',
            'max_amount.gt' => 'Maximum amount must be greater than minimum amount',
        ];
    }
}
```

### 4. Model (app/Models/DoubleGameConfig.php)
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoubleGameConfig extends Model
{
    use HasFactory;

    protected $table = 'double_game_configs';

    protected $fillable = [
        'client_id',
        'red_prize',
        'black_prize',
        'special_prize',
        'min_amount',
        'max_amount',
    ];

    protected $casts = [
        'red_prize' => 'decimal:2',
        'black_prize' => 'decimal:2',
        'special_prize' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}
```

### 5. Migration
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('double_game_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_id');
            $table->decimal('red_prize', 10, 2)->default(2.00);
            $table->decimal('black_prize', 10, 2)->default(2.00);
            $table->decimal('special_prize', 10, 2)->default(14.00);
            $table->decimal('min_amount', 10, 2)->default(1.00);
            $table->decimal('max_amount', 10, 2)->default(10000.00);
            $table->timestamps();

            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->unique('client_id'); // One config per client
        });
    }

    public function down()
    {
        Schema::dropIfExists('double_game_configs');
    }
};
```

## Database Schema

### Table: double_game_configs
- `id` (bigint, primary key, auto increment)
- `client_id` (bigint, foreign key to clients table, unique)
- `red_prize` (decimal 10,2, default 2.00)
- `black_prize` (decimal 10,2, default 2.00)
- `special_prize` (decimal 10,2, default 14.00)
- `min_amount` (decimal 10,2, default 1.00)
- `max_amount` (decimal 10,2, default 10000.00)
- `created_at` (timestamp)
- `updated_at` (timestamp)

## Security Considerations

1. **Authentication**: All endpoints should require authentication
2. **Authorization**: Implement role-based access control
3. **Validation**: Use FormRequest classes for input validation
4. **Rate Limiting**: Implement rate limiting for API endpoints
5. **CORS**: Configure CORS settings appropriately

## Testing

Create feature tests for all endpoints:
- Test client list retrieval
- Test configuration CRUD operations
- Test validation rules
- Test authorization

## Additional Notes

- Follow PSR-12 coding standards
- Use Laravel Pint for code formatting
- Implement proper error handling and logging
- Consider implementing caching for frequently accessed data
- Use database transactions for critical operations
```
